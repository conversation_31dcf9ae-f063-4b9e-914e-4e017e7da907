import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { TextInput, Text, TouchableRipple, HelperText } from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent,A4UHuma } from '../../common';
import { a4uHuma } from '../../../assets';

const salaryOptions = [
  { label: '£20,000 - £30,000', value: '£20,000 - £30,000' },
  { label: '£30,000 - £40,000', value: '£30,000 - £40,000' },
  { label: '£40,000 - £50,000', value: '£40,000 - £50,000' },
  { label: '£50,000+', value: '£50,000+' }
];

export const NewRegisterStepTwoView = () => {
  const styles = useCurrentTheme(createStyles);
  const [jobPreference, setJobPreference] = useState('Marketing Intern');
  const [jobType, setJobType] = useState('£20,000 - £30,000');
  const [showDropdown, setShowDropdown] = useState(false);

  const [jobPreferenceError, setJobPreferenceError] = useState('');
  const [jobTypeError, setJobTypeError] = useState('');

  const [touched, setTouched] = useState({
    jobPreference: false,
    jobType: false,
  });


  const validateJobPreference = (text: string): boolean => {
    if (!text.trim()) {
      setJobPreferenceError('Current position is required');
      return false;
    } else {
      setJobPreferenceError('');
      return true;
    }
  };

  const validateJobType = (text: string): boolean => {
    if (!text.trim()) {
      setJobTypeError('Salary range is required');
      return false;
    } else {
      setJobTypeError('');
      return true;
    }
  };

  const handleContinue = () => {
    Keyboard.dismiss();
    const isJobPreferenceValid = validateJobPreference(jobPreference);
    const isJobTypeValid = validateJobType(jobType);

    setTouched({
      jobPreference: true,
      jobType: true,
    });

    if (isJobPreferenceValid && isJobTypeValid) {
      navigate(screens.NewRegisterStepThree);
    }
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepThree);
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterStepOne);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  return (
    <RegistrationStepComponent
      title="Your Preferences"
      currentStep={2}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <A4UHuma
        image={a4uHuma}
        text="I have extracted your current position from your CV, please review before continuing"
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />
      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Current Position"
          value={jobPreference}
          onChangeText={(text) => {
            setJobPreference(text);
            if (touched.jobPreference) {
              validateJobPreference(text);
            }
          }}
          placeholder="e.g. Software Developer"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!jobPreferenceError && touched.jobPreference && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!jobPreferenceError && touched.jobPreference ? "#FF3B30" : "#39608F"}
          error={!!jobPreferenceError && touched.jobPreference}
          onBlur={() => {
            setTouched({ ...touched, jobPreference: true });
            validateJobPreference(jobPreference);
          }}
        />
        {!!jobPreferenceError && touched.jobPreference && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {jobPreferenceError}
          </HelperText>
        )}
      </View>

      <View style={styles.inputGroup}>
        <View>
          <TextInput
            mode="outlined"
            label="Salary Per Annum"
            value={jobType}
            placeholder="Select salary"
            style={styles.input}
            outlineStyle={[
              styles.inputOutline,
              !!jobTypeError && touched.jobType && styles.errorInputOutline
            ]}
            right={<TextInput.Icon icon="chevron-down" onPress={() => setShowDropdown(true)} />}
            activeOutlineColor="#0E1C5D"
            outlineColor={!!jobTypeError && touched.jobType ? "#FF3B30" : "#39608F"}
            error={!!jobTypeError && touched.jobType}
            editable={false}
            onPressIn={() => {
              setShowDropdown(true);
              setTouched({ ...touched, jobType: true });
              validateJobType(jobType);
            }}
          />
          {!!jobTypeError && touched.jobType && (
            <HelperText type="error" visible={true} style={styles.helperText}>
              {jobTypeError}
            </HelperText>
          )}
        </View>

        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowDropdown(false)}
          >
            <View style={styles.dropdownContainer}>
              <FlatList
                data={salaryOptions}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                  <TouchableRipple
                    style={styles.dropdownItem}
                    onPress={() => {
                      setJobType(item.value);
                      setShowDropdown(false);
                    }}
                    rippleColor="rgba(14, 28, 93, 0.1)"
                  >
                    <Text style={styles.dropdownItemText}>{item.label}</Text>
                  </TouchableRipple>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </RegistrationStepComponent>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    humaContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 25,
      paddingHorizontal: 0,
      width: '100%',
    },
    humaImage: {
      width: 60,
      height: 60,
      marginRight: 16,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
      flex: 1,
      marginLeft: 10,
      lineHeight: 20,
    },
    customComma: {
      fontFamily: 'Poppins-SemiBold',
      color: '#007CFF',
      fontSize: 14,
    },
    inputGroup: {
      marginBottom: 25,
    },
    input: {
      backgroundColor: '#FFFFFF',
      height: 50,
      fontSize: 16,
      fontFamily: 'Poppins-Regular',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    inputOutline: {
      borderRadius: 8,
      borderColor: '#39608F',
      borderWidth: 1,
    },
    errorInputOutline: {
      borderRadius: 8,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    dropdownContainer: {
      width: '90%',
      maxHeight: 250,
      backgroundColor: '#FFFFFF',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#39608F',
      overflow: 'hidden',
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    dropdownItem: {
      paddingVertical: 0,
      paddingHorizontal: 0,
    },
    dropdownItemText: {
      fontFamily: 'Poppins-Regular',
      fontSize: 16,
      color: '#000000',
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    separator: {
      height: 1,
      backgroundColor: '#E0E0E0',
      width: '100%',
    },
  });

  return styles;
};
