import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  Image,
  Dimensions,
  Animated,
  Easing,
} from 'react-native';
import {
  Text,
  Button,
} from 'react-native-paper';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faCircle } from '@fortawesome/free-solid-svg-icons';
import { ITheme, useCurrentTheme } from '../../theme';
import { navigate } from '../../utility';
import Svg, { Path } from 'react-native-svg';

interface IProps {
  route?: {
    params?: {
      title?: string;
      message?: string;
      message2?: string;
      buttonText?: string;
      navigateTo?: string;
      navigateParams?: any;
      customImage?: any;
      imageUrl?: string;
      showCheckmark?: boolean;
      showImage?: boolean;
      checkmarkPosition?: 'top' | 'center';
      onButtonPress?: () => void;
    };
  };
}
const AnimatedCheckMark = ({
  size = 120,
  circleColor = "#0E1C5D",
  checkColor = "#3EBF8F",
  style
}: {
  size?: number;
  circleColor?: string;
  checkColor?: string;
  style: any;
}) => {
  const strokeDashoffset = useState(() => new Animated.Value(700))[0];
  useEffect(() => {
    setTimeout(() => {
      Animated.timing(strokeDashoffset, {
        toValue: 0,
        duration: 1200,
        useNativeDriver: true,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1)
      }).start();
    }, 120);
  }, []);

  const checkSize = size * 0.6;
  const AnimatedPath = Animated.createAnimatedComponent(Path);

  return (
    <View style={[style, {
      width: size,
      height: size,
      justifyContent: 'center',
      alignItems: 'center',
    }]}>
      <FontAwesomeIcon
        icon={faCircle}
        size={size}
        color={circleColor}
        style={{
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.4,
          shadowRadius: 5,
          elevation: 8,
        }}
      />
      <View style={{
        position: 'absolute',
        width: size,
        height: size,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <Svg width={checkSize} height={checkSize} viewBox="0 0 32 32">
          <AnimatedPath
            d="M6 17 L13 24 L26 8"
            fill="none"
            stroke={checkColor}
            strokeWidth={5}
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray={700}
            strokeDashoffset={strokeDashoffset}
          />
        </Svg>
      </View>
    </View>
  );
};

const SuccessScreen = (props: IProps) => {
  const theme = useCurrentTheme(theme => theme);
  const styles = useCurrentTheme(createStyles);
  const params = props.route?.params || {};

  const title = params.title || 'Congratulation !!!';
  const message = params.message || 'You successfully registered with us!';
  const message2 = params.message2 || '';
  const buttonText = params.buttonText || 'Next';
  const navigateTo = params.navigateTo;
  const navigateParams = params.navigateParams;
  const customImage = params.customImage;
  const imageUrl = params.imageUrl;
  const showCheckmark = params.showCheckmark !== undefined ? params.showCheckmark : true;
  const showImage = params.showImage !== undefined ? params.showImage : true;
  const checkmarkPosition = params.checkmarkPosition || 'top';

  const handleButtonPress = () => {
    if (params.onButtonPress) {
      params.onButtonPress();
    } else if (navigateTo) {
      navigate(navigateTo, navigateParams);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.title}>{title}</Text>

        {showImage && (
          <View style={styles.imageContainer}>
            {imageUrl ? (
              <View style={styles.customImageContainer}>
                <Image
                  source={{ uri: imageUrl }}
                  style={styles.customImage}
                  resizeMode="contain"
                />
                {showCheckmark && (
                  <View style={[
                    styles.overlayCheckmarkContainer,
                    checkmarkPosition === 'center' ? styles.centerCheckmark : styles.topCheckmark
                  ]}>
                    <AnimatedCheckMark size={120} circleColor="#0E1C5D" checkColor={theme.palette.success} style={styles.checkmarkIcon} />
                  </View>
                )}
              </View>
            ) : customImage && (
              <View style={styles.customImageContainer}>
                <Image
                  source={customImage}
                  style={styles.customImage}
                  resizeMode="contain"
                />
                {showCheckmark && (
                  <View style={[
                    styles.overlayCheckmarkContainer,
                    checkmarkPosition === 'center' ? styles.centerCheckmark : styles.topCheckmark
                  ]}>
                    <AnimatedCheckMark size={120} circleColor="#0E1C5D" checkColor={theme.palette.success} style={styles.checkmarkIcon} />
                  </View>
                )}
              </View>
            )}
          </View>
        )}

        {!showImage && showCheckmark && (
          <View style={styles.standaloneCheckmarkContainer}>
            <AnimatedCheckMark size={120} circleColor="#0E1C5D" checkColor={theme.palette.success} style={styles.checkmarkIcon} />
          </View>
        )}

        <View style={styles.messageContainer}>
          <Text style={styles.message}>{message}</Text>
          {message2 ? <Text style={styles.message2}>{message2}</Text> : null}
        </View>

        <Button
          mode="contained"
          onPress={handleButtonPress}
          style={styles.button}
          labelStyle={styles.buttonText}
        >
          {buttonText}
        </Button>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (_theme: ITheme) => {
  const { width } = Dimensions.get('window');

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'white',
    },
    contentContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 20,
      paddingBottom: 40,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: '#000',
      textAlign: 'center',
      marginBottom: 40,
      fontFamily: 'Poppins-Bold',
    },
    imageContainer: {
      width: width * 0.9,
      height: width * 0.9,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 40,
    },
    customImageContainer: {
      width: '100%',
      height: '100%',
      position: 'relative',
      justifyContent: 'center',
      alignItems: 'center',
    },
    customImage: {
      width: '100%',
      height: '100%',
    },
    overlayCheckmarkContainer: {
      position: 'absolute',
      alignSelf: 'center',
      zIndex: 10,
    },
    topCheckmark: {
      top: 0,
    },
    centerCheckmark: {
      top: '35%',
    },
    standaloneCheckmarkContainer: {
      width: 120,
      height: 120,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 40,
    },
    checkmarkIcon: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.4,
      shadowRadius: 5,
      elevation: 8,
    },
    messageContainer: {
      marginBottom: 60,
      paddingHorizontal: 5,
    },
    message: {
      fontSize: 16,
      color: '#333',
      textAlign: 'center',
      marginBottom: 14,
      fontFamily: 'Poppins-Medium',
    },
    message2: {
      fontSize: 13,
      color: '#333',
      textAlign: 'center',
      fontFamily: 'Poppins-Regular',
    },
    button: {
      width: '100%',
      height: 50,
      marginTop: 30,
      justifyContent: 'center',
      borderRadius: 5,
      backgroundColor: '#0E1C5D',
    },
    buttonText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      color: 'white',
    },
  });
};
export { SuccessScreen as SuccessScreenComponent };