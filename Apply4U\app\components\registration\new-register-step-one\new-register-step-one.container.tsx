import React from 'react';
import { NewRegisterStepOneView } from './new-register-step-one.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterStepOneContainer = () => {
  return <NewRegisterStepOneView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterStepOneContainer);

export { connectedNewRegisterContainer as NewRegisterStepOneContainer };
