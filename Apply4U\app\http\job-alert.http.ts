import {http} from './http-base';
import {IAttbJobAlert, IJobGateJobAlert} from '../interfaces';

const saveAttbJobAlert = async (
  jobAlert: IAttbJobAlert,
): Promise<void> => {
  let url: string = `api/AttbJobAlerts`;
  let result = await http
    .post<any>(url,jobAlert)
    .catch(error => Promise.reject(error));

  return result.data;
};

const searchJobAlert = async (
    desiredKewords:string | undefined,
    desiredLocation:string | undefined,
    userId:number
  ): Promise<any[]> => {
    let params = "";
    if (!!desiredKewords && desiredKewords.trim() != "") {
        params += ((params.trim() == "") ? "?" : "&") + "desiredKewords=" + desiredKewords;
      }
    
      if (!!desiredLocation && desiredLocation.trim() != "") {
        params += ((params.trim() == "") ? "?" : "&") + "desiredLocation=" + desiredLocation;
      }

      if (!!userId && userId != 0) {
        params += ((params.trim() == "") ? "?" : "&") + "userId=" + userId;
      }

    let url: string = `api/AttbJobAlerts/search${params}`;
    let result = await http
      .get<any[]>(url)
      .catch(error => Promise.reject(error));
  
    return result.data;
  };

  const saveJobGateAlert = async (
    jobAlert: IJobGateJobAlert,
  ): Promise<void> => {
    let url: string = `api/affiliates/JobGateJobAlert/NewUser`;
    let result = await http
      .post<any>(url,jobAlert)
      .catch(error => Promise.reject(error));
  
    return result.data;
  };

const jobAlertApi = {
  searchJobAlert,
  saveAttbJobAlert,
  saveJobGateAlert
};

export {jobAlertApi};
