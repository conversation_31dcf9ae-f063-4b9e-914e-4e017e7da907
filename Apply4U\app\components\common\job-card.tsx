import React from 'react';
import { Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { defaultCompanyLogo1 } from '../../assets';
import { ITheme, useCurrentTheme } from '../../theme';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
  faHeart,
  faShareAlt,
  faMapMarkerAlt,
  faBriefcase,
  faPoundSign,
  faListAlt
} from '@fortawesome/free-solid-svg-icons';
import { Text, Image } from '.';
import { isHtml, isNullOrWhitespace } from '../../utility';
import { Swipeable } from 'react-native-gesture-handler';
import { jobCardHeight } from '../../app.constant';
import { faEyeSlash } from '@fortawesome/free-solid-svg-icons';

interface IProps {
  jobId: number;
  jobTitle: string;
  locationText: string;
  salaryRange: string;
  jobType: string;
  jobCategory: string;
  companyName: string;
  companyLogoUrl: string;
  jobDetails: string;
  showFavIcon: boolean;
  showShareIcon: boolean;
  isSwipeable: boolean;
  isAlreadyApplied: boolean;
  applicationUrl: string;
  jobSourceId: number;
  swipeRightText: string;
  isSwipeLeftEnabled: boolean;
  isSwipeRightEnabled: boolean;
  isFav: boolean;
  favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
  shareJob: (jobId: number,jobTitle:string,locationText:string) => void;
  goToJobDetail: (jobId: number) => void;
  onSwipeRight: (jobId: number) => void;
  easyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string) => void;
}

const jobCardAreEqual = (prevProps: IProps, nextProps: IProps) => {
  return (
    prevProps.jobId == nextProps.jobId
    &&
    prevProps.jobTitle == nextProps.jobTitle
    &&
    prevProps.locationText == nextProps.locationText
    &&
    prevProps.salaryRange == nextProps.salaryRange
    &&
    prevProps.jobType == nextProps.jobType
    &&
    prevProps.jobCategory == nextProps.jobCategory
    &&
    prevProps.companyName == nextProps.companyName
    &&
    prevProps.companyLogoUrl == nextProps.companyLogoUrl
    &&
    prevProps.showFavIcon == nextProps.showFavIcon
    &&
    prevProps.showShareIcon == nextProps.showShareIcon
    &&
    prevProps.isSwipeable == nextProps.isSwipeable
    &&
    prevProps.isAlreadyApplied == nextProps.isAlreadyApplied
    &&
    prevProps.applicationUrl == nextProps.applicationUrl
    &&
    prevProps.jobSourceId == nextProps.jobSourceId
    &&
    prevProps.swipeRightText == nextProps.swipeRightText
    &&
    prevProps.isFav == nextProps.isFav
  );
}

const JobCard = React.memo((props: IProps) => {
  const { styles, colors } = useCurrentTheme(createStyles, props);
  const [swipeableRef, setSwipeAbleRef] = React.useState<Swipeable | null>(
    null,
  );

  const renderRightAction = React.useCallback(() => {
    return (
      <View style={styles.renderActionContainer}>
        <View style={styles.hideJobsContainer}>
          <View style={styles.renderActionContentContainer}>
            {/* <FontAwesomeIcon icon={faEyeSlash} size={20} color={'white'} /> */}
            <Text styles={styles.renderRightActionText} text={props.swipeRightText} />
          </View>
        </View>
      </View>
    );
  }, []);

  const renderLeftAction = React.useCallback(() => {
    return (
      <View style={styles.renderActionContainer}>
        <View
          style={
            props.isAlreadyApplied === true
              ? styles.successEasyApplyContainer
              : styles.easyApplyContainer
          }>
          <View style={styles.renderActionContentContainer}>
            <Text styles={styles.renderActionText}>
              {props.isAlreadyApplied === true
                ? 'Already Applied'
                : 'Easy Apply'}
            </Text>
          </View>
        </View>
      </View>
    );
  }, []);

  const swipeAbleContent = (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerCell1}>
          <Image
            defaultSource={defaultCompanyLogo1}
            source={
              !isNullOrWhitespace(props.companyLogoUrl)
                ? { uri: props.companyLogoUrl }
                : defaultCompanyLogo1
            }
            style={styles.companyLogo}
            key={props.jobId}
          />
        </View>
        <View style={styles.headerCell2}>
          <View style={styles.headerCell2Row1}>
            <Pressable
              onPress={() => {
                props.goToJobDetail(props.jobId);
              }}>
              <Text
                styles={styles.jobTitle}
                text={`${props.jobTitle}`}
                numberOfLines={1}
              />
            </Pressable>
          </View>
          {!!props.companyName && (
            <View style={styles.headerCell2Row2}>
              <Text
                styles={styles.companyName}
                numberOfLines={1}
                text={props.companyName}
              />
            </View>
          )}
        </View>
        <View style={styles.headerCell3}>
          <View style={styles.headerCell3Container}>
            {!!props.showFavIcon && props.showFavIcon === true && (
              <TouchableOpacity activeOpacity={1} onPress={() => {
                props.favJobsAddOrRemove(props.jobId, props.isFav);
              }} style={styles.headerCell3Row1}>
                <FontAwesomeIcon
                  icon={faHeart}
                  style={styles.addToFavIcon}
                  color={props.isFav == true ? colors.green : colors.defaultIconColor}
                />
              </TouchableOpacity>
            )}
            {!!props.showShareIcon && props.showShareIcon === true && (
              <TouchableOpacity onPress={() => {
                props.shareJob(props.jobId,props.jobTitle,props.locationText);
              }} style={styles.headerCell3Row2}>
                <FontAwesomeIcon
                  icon={faShareAlt}
                  style={styles.shareIcon}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
      <View style={styles.content}>
        <View style={styles.contentContainer}>
          {!isHtml(props.jobDetails) && (
            <View style={styles.contentRow1}>
              <Text
                numberOfLines={2}
                styles={styles.detailText}
                text={props.jobDetails}
              />
            </View>
          )}

          <View style={styles.contentRow2}>
            <View style={styles.contentRow2Row1}>
              <View style={styles.contentRowCellsContainer}>
                <View style={styles.contentRow1Cell1}>
                  <Text styles={styles.iconText} numberOfLines={1}>
                    <View style={styles.iconStyle}>
                      <FontAwesomeIcon
                        icon={faMapMarkerAlt}
                        style={styles.iconStyle}
                        size={12}
                      />
                    </View>
                    {props.locationText}
                  </Text>
                </View>
                <View style={styles.contentRow1Cell1}>
                  <Text styles={styles.iconText}>
                    <View style={styles.iconStyle}>
                      <FontAwesomeIcon
                        icon={faListAlt}
                        style={styles.iconStyle}
                        size={12}
                      />
                    </View>
                    {props.jobCategory}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.contentRow2Row2}>
              <View style={styles.contentRowCellsContainer}>
                <View style={styles.contentRowCell}>
                  <Text styles={styles.iconText} numberOfLines={1}>
                    <View style={styles.iconStyle}>
                      <FontAwesomeIcon
                        icon={faBriefcase}
                        style={styles.iconStyle}
                        size={12}
                      />
                    </View>
                    {props.jobType}
                  </Text>
                </View>
                <View style={styles.contentRowCell}>
                  <Text styles={styles.iconText}>
                    <View style={styles.iconStyle}>
                      <FontAwesomeIcon
                        icon={faPoundSign}
                        style={styles.iconStyle}
                        size={12}
                      />
                    </View>
                    {props.salaryRange}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <>
      {!!props.isSwipeable && props.isSwipeable == true && (
        <Swipeable
          renderLeftActions={props.isSwipeLeftEnabled ? renderLeftAction : undefined}
          onSwipeableLeftWillOpen={() => {
            if (props.isSwipeLeftEnabled) {
              if (!props.isAlreadyApplied) {
                props.easyApply(props.jobId, props.jobSourceId, props.jobTitle, props.applicationUrl);
              }

              swipeableRef?.close();
            }
          }}
          renderRightActions={props.isSwipeRightEnabled ? renderRightAction : undefined}
          onSwipeableRightOpen={() => {
            if (props.isSwipeRightEnabled) {
              if (props.jobId > 0) {
                props.onSwipeRight(props.jobId);
              } else {
                swipeableRef?.close();
              }
            }
          }}
          ref={ref => setSwipeAbleRef(ref)}>
          <View style={styles.swipeAbleContainer}>{swipeAbleContent}</View>
        </Swipeable>
      )}
      {!props.isSwipeable && swipeAbleContent}
    </>
  );
}, jobCardAreEqual);

const createStyles = (theme: ITheme, props: any) => {
  const styles = StyleSheet.create({
    container: {
      width: props.isSwipeable == true ? '100%' : '97%',
      height: jobCardHeight,
      backgroundColor: '#fff',
      marginTop: theme.spacing(5),
      padding: theme.spacing(3),
      paddingBottom: 10,
      alignSelf: 'center',
      justifyContent: 'center',
      marginHorizontal: props.isSwipeable == true ? 0 : 5,
      borderRadius: theme.borderRadius(5),
    },
    swipeAbleContainer: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
      paddingHorizontal: 10,
    },
    header: {
      flex: 1.3,
      flexDirection: 'row',
    },
    headerCell1: {
      flex: 1,
      flexDirection: 'column',
      //backgroundColor: 'green',
      justifyContent: 'center',
      borderWidth: 1,
      borderRadius: 5,
      borderColor: theme.palette.lightGray,
    },
    headerCell2: {
      flex: 3,
      flexDirection: 'column',
      //backgroundColor: 'red',
      justifyContent: 'center',
    },
    headerCell2Row1: {
      flex: 1,
      flexDirection: 'column',
    },
    headerCell2Row2: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-start',
    },
    headerCell3: {
      flex: 1,
      flexDirection: 'column',
      //backgroundColor: 'blue',
    },
    headerCell3Container: {
      flex: 1,
      flexDirection: 'row',
    },
    headerCell3Row1: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    headerCell3Row2: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    content: {
      flex: 2.5,
      flexDirection: 'row',
      //backgroundColor: 'yellow',
      borderTopWidth: 0.2,
      borderTopColor: 'gray',
    },
    contentContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    contentRow1: {
      flex: 1,
      flexDirection: 'column',
      //backgroundColor: 'red',
      paddingLeft: theme.spacing(5),
    },
    contentRow2: {
      flex: 1.8,
      flexDirection: 'column',
    },
    contentRow2Row1: {
      flex: 1,
      //backgroundColor: 'pink',
    },
    contentRow2Row2: {
      flex: 1,
      //backgroundColor: 'pink',
    },
    contentRowCellsContainer: {
      flex: 1,
      flexDirection: 'row',
    },
    contentRowCell: {
      flex: 1,
      flexDirection: 'column',
      paddingLeft: theme.spacing(5),
    },
    contentRow1Cell1: {
      flex: 1,
      flexDirection: 'column',
      paddingLeft: theme.spacing(5),
      justifyContent: 'center',
    },
    companyLogo: {
      width: '95%',
      height: '95%',
      borderRadius: 3,
      resizeMode: 'contain',
      alignSelf: 'center',
    },
    jobTitle: {
      color: '#4f40d1',
      fontWeight: 'bold',
      letterSpacing: 0.5,
      paddingHorizontal: theme.spacing(2),
    },
    companyName: {
      fontSize: 11,
      color: '#333333',
      letterSpacing: 0.5,
      paddingHorizontal: theme.spacing(2),
    },
    addToFavIconColor: {
      color: '#a2b3e0',
    },
    shareIconColor: {
      color: '#a2b3e0',
    },
    addToFavIcon: {
      marginTop: theme.spacing(6),
    },
    shareIcon: {
      color: '#a2b3e0',
      marginTop: theme.spacing(6),
    },
    detailText: {
      fontSize: 12,
      color: '#333333',
      letterSpacing: 0.5,
      alignSelf: 'flex-start',
    },
    iconStyle: {
      width: 20,
      color: '#a2b3e0',
    },
    iconText: {
      fontSize: 12,
      marginTop: 8,
      color: '#333333',
    },
    renderActionContainer: {
      flex: 1,
    },
    hideJobsContainer: {
      width: '95%',
      height: jobCardHeight,
      marginTop: theme.spacing(5),
      padding: theme.spacing(3),
      paddingBottom: 10,
      alignSelf: 'center',
      justifyContent: 'center',
      borderRadius: theme.borderRadius(5),
      backgroundColor: theme.palette.lightGray,
    },
    easyApplyContainer: {
      width: '95%',
      height: jobCardHeight,
      marginTop: theme.spacing(5),
      padding: theme.spacing(3),
      paddingBottom: 10,
      alignSelf: 'center',
      justifyContent: 'center',
      borderRadius: theme.borderRadius(5),
      backgroundColor: theme.palette.lightGray,
    },
    successEasyApplyContainer: {
      width: '95%',
      height: jobCardHeight,
      marginTop: theme.spacing(5),
      padding: theme.spacing(3),
      paddingBottom: 10,
      alignSelf: 'center',
      justifyContent: 'center',
      borderRadius: theme.borderRadius(5),
      backgroundColor: '#00FF00',
    },
    renderActionContentContainer: {
      paddingRight: 10,
      justifyContent: 'center',
    },
    renderActionText: {
      alignSelf: 'flex-start',
    },
    renderRightActionText: {
      alignSelf: 'flex-end',
    },
  });

  const colors = { defaultIconColor: '#a2b3e0', green: 'green' }
  return { styles, colors };
};

export { JobCard };
