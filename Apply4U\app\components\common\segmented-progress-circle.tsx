import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';

interface SegmentedProgressCircleProps {
  progress: number; // 0 to 1
  segmentCount: number;
  radius: number;
  strokeWidth: number;
  activeColor: string;
  inactiveColor: string;
  bgColor: string;
  children?: React.ReactNode;
}

export const SegmentedProgressCircle: React.FC<SegmentedProgressCircleProps> = ({
  progress,
  segmentCount,
  radius,
  strokeWidth,
  activeColor,
  inactiveColor,
  bgColor,
  children,
}) => {
  const filledSegments = Math.ceil(progress * segmentCount);
  const renderSegments = () => {
    const segments = [];
    const centerX = radius;
    const centerY = radius;
    const segmentRadius = radius - strokeWidth / 2;
    const circumference = 2 * Math.PI * segmentRadius;
    const segmentAngle = (2 * Math.PI) / segmentCount;
    const gapAngle = segmentAngle * 0.05;
    const segmentArcAngle = segmentAngle - gapAngle;

    for (let i = 0; i < segmentCount; i++) {
      const startAngle = i * segmentAngle;
      const endAngle = startAngle + segmentArcAngle;
      const startX = centerX + segmentRadius * Math.cos(startAngle - Math.PI / 2);
      const startY = centerY + segmentRadius * Math.sin(startAngle - Math.PI / 2);
      const endX = centerX + segmentRadius * Math.cos(endAngle - Math.PI / 2);
      const endY = centerY + segmentRadius * Math.sin(endAngle - Math.PI / 2);
      const isFilled = i < filledSegments;
      const largeArcFlag = segmentArcAngle > Math.PI ? 1 : 0;
      const pathData = `
        M ${startX} ${startY}
        A ${segmentRadius} ${segmentRadius} 0 ${largeArcFlag} 1 ${endX} ${endY}
      `;

      segments.push(
        <Path
          key={i}
          d={pathData}
          stroke={isFilled ? activeColor : inactiveColor}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="butt"
        />
      );
    }

    return segments;
  };

  return (
    <View style={[styles.container, { width: radius * 2, height: radius * 2 }]}>
      <Svg width={radius * 2} height={radius * 2}>
        {renderSegments()}
      </Svg>
      <View
        style={[
          styles.innerCircle,
          {
            width: (radius - strokeWidth - 4) * 2,
            height: (radius - strokeWidth - 4) * 2,
            borderRadius: radius - strokeWidth - 4,
            backgroundColor: bgColor,
          },
        ]}
      >
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircle: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
