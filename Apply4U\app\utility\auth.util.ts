import {store} from '../redux';
import {setLoginUserDetailAction, setTokenAction} from '../redux/actions';

export const isAuthenticated = (): boolean => {
  let token = store?.getState()?.token;
  if (
    token &&
    token.access_token &&
    token.expire_on &&
    new Date(token.expire_on) > new Date()
  ) {
    return true;
  } else {
    return false;
  }
};

export const logout = () => {
  store.dispatch(setTokenAction(null));
  store.dispatch(setLoginUserDetailAction(null));
};
