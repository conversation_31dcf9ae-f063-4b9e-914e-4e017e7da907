import { faSearch, faBusinessTime, faHistory } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { CancelTokenSource } from 'axios';
import React from 'react';
import {StyleSheet, View, ScrollView, Pressable,Keyboard} from 'react-native';
import {InputText, Text} from '.';
import { zIndex } from '../../app.constant';
import {InputType} from '../../enum';
import {autocompleteApi} from '../../http';
import { getCancelTokenSource } from '../../http/http-base';
import {IAutoComplete} from '../../interfaces';
import {ITheme, useCurrentTheme} from '../../theme';
import {connect} from 'react-redux';
import { IApplicationState } from '../../redux';


interface IProps {
  placeholder: string;
  name: string;
  autoCompletekey:string;
  styles?: any;
  value?: string;
  onChange?: (
    value: string,
    name: string
  ) => void;
  onOpen:() => void;
  onClose:() => void;
  recentSearches:string[];
  onSelect?:(searchKeywords:string) => void;
  addTypoToItems?:boolean;
}
const KeywordsAutoComplete = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const defaultArray:IAutoComplete[] = [];
  const [cancelTokens,setCancelTokens] = React.useState<CancelTokenSource[]>([]);
  const [buildKeywordsDataSource,setBuildKeywordsDataSource] = React.useState<boolean>(false);
  const handleInputChange = (value: string, name: string) => {
    setIsTyping(true);
    if (!!value && value.trim() != '') {
      let cancelToken = getCancelTokenSource();
      setCancelTokens([...cancelTokens,cancelToken]);
      autocompleteApi.getSectorsAutoComplete(value,cancelToken).then(result => {
        if (result && result.length > 0) {
          setData([...result]);
        }else{
            setData([...defaultArray]);
        }
      }).catch((e:any) => {
      });
    } else{
        if(!!cancelTokens && cancelTokens.length > 0){
            cancelTokens.forEach(tokenSource => {
                tokenSource.cancel('Cancelling requuest');
            });

            setCancelTokens([]);
        }

        setData([...defaultArray]);

        if(value != props.value){
          setBuildKeywordsDataSource(true);
        }
    }

    if (props.onChange) {
      props.onChange(value, name);
    }
  };

  const [data, setData] = React.useState<IAutoComplete[]>([]);

  const buildRecentSearchesDataSource = () =>{
    if(!!props.recentSearches && props.recentSearches.length > 0){
      let recentSearchesItems:IAutoComplete[] = [];
      props.recentSearches.forEach(recentSearchValue => {
        const recentSearchItem:IAutoComplete = {
          DisplayValue:'',
          Key:recentSearchValue,
          Value:recentSearchValue,
        };
        recentSearchesItems.push(recentSearchItem);
      });

      setData([...recentSearchesItems]);
    }
  }

  const [isTyping,setIsTyping] = React.useState<boolean>(false);

  React.useEffect(() => {
    if((!!data && data.length > 0) || (!!isTyping && props.addTypoToItems)){
      props.onOpen();
    }else{
      props.onClose();
    }
  },[data,isTyping,props.addTypoToItems]);

  React.useEffect(() => {
    if((!data || data.length <= 0) && (!props.value || props.value.trim() == '') && buildKeywordsDataSource == true){
      setBuildKeywordsDataSource(false);
      buildRecentSearchesDataSource();
    }
  },[data,props.value,buildKeywordsDataSource]);

  React.useEffect(() => {
    const keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide',() => {
      setData([]);
      setIsTyping(false);
    });
    return () => {
      Keyboard.removeAllListeners('keyboardDidHide');
    }
  },[]);

  const RenderItem = (innerProps: any) => {
    const item: IAutoComplete = innerProps.item;
    return (
      <Pressable
        onPress={() => {
          setData([]);
          setIsTyping(false);
          if (props.onChange) {
            props.onChange(item.Value, props.name);
          }

          if(props.onSelect){
            props.onSelect(item.Value);
          }
        }}
        style={styles.itemContainer}
        >
        <Text numberOfLines={1} styles={styles.itemTextStyle}>
        {`${item.Value} `}
        <Text styles={styles.titleTextStyle} text={`${item.DisplayValue == "Held Position" ? 'Title' : item.DisplayValue}`}/>
        </Text>
      </Pressable>
    );
  };

  const RenderPlaceHolderItem = () => {
    return (
      <View
        style={styles.placeholderItemContainer}
        >
        <Text numberOfLines={1} styles={styles.placeholderItemTextStyle}>
        <FontAwesomeIcon icon={faBusinessTime} size={13} />
        {` Job title or Skill`}
        </Text>
      </View>
    );
  };

  const RenderTypoItem = () => {
    return (
      <Pressable
        onPress={() => {
          setData([]);
          setIsTyping(false);
          if (props.onChange && !!props.value && props.value.trim() != '') {
            props.onChange(props.value, props.name);
          }

          if(props.onSelect && !!props.value && props.value.trim() != ''){
            props.onSelect(props.value);
          }
        }}
        style={styles.itemContainer}
        >
        <Text numberOfLines={1} styles={styles.itemTextStyle}>
        {`${props.value} jobs`}
        </Text>
      </Pressable>
    );
  };

  const RenderRecentSearchesPlaceHolderItem = () => {
    return (
      <View
        style={styles.placeholderItemContainer}
        >
        <Text numberOfLines={1} styles={styles.placeholderItemTextStyle}>
        <FontAwesomeIcon icon={faHistory} size={13} />
        {` Recent Searches`}
        </Text>
      </View>
    );
  };

  return (
    <>
    <View style={styles.container}>
      <View style={styles.secondContainer}>
        <View style={styles.inputContainer}>
          <InputText
            placeholder={props.placeholder}
            key={`input_${props.autoCompletekey}`}
            name={props.name}
            styles={props.styles}
            inputType={InputType.Text}
            value={props.value}
            onBlur={() => {
              setData([]);
              setIsTyping(false);
            }}
            onChangeText={handleInputChange}
            height={50}
            icon={faSearch}
            inputIconSize={20}
            onClick={() => {
              if(!props.value || props.value.trim() == ''){
                buildRecentSearchesDataSource();
              }
            }}
          />
        </View>
      </View>
    </View>
    {((!!data && data.length > 0) || (!!props.addTypoToItems && !!props.value && props.value.trim() != '')) && (
      <View style={styles.itemsContainer}>
        <ScrollView keyboardShouldPersistTaps={'always'}>
          {!!props.addTypoToItems && !!props.value && props.value.trim() != '' && !!isTyping ? <RenderTypoItem /> : undefined}
            {(!!data && data.length > 0) ? ((!props.value || props.value.trim() == '') ? <RenderRecentSearchesPlaceHolderItem /> : <RenderPlaceHolderItem />) : undefined}
          {data.map((item: IAutoComplete) => {
            return (
              <RenderItem
                key={`list_item_${item.Key}_${item.Value}`}
                item={item}
              />
            );
          })}
          </ScrollView>
      </View>
    )}
    </>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
    },
    secondContainer: {
      //flex: 1,
      flexDirection: 'column',
      width:'100%'
    },
    inputContainer: {
      //flex: 1,
      width:'100%',
      flexDirection: 'column',
    },
    itemsContainer: {
      position:'absolute',
      width:'100%',
      top:75,
      left:15,
      height:'84%',
      //borderLeftWidth: 0.5,
      //borderRightWidth: 0.5,
      //borderTopWidth: 0.5,
      //borderColor: theme.palette.gray,
      ...theme.zIndex(zIndex.locationAutoComplete),
    },
    itemContainer: {
      height: 40,
      width: '100%',
      paddingLeft: theme.spacing(5),
      borderColor: 'gray',
      backgroundColor: theme.palette.white,
      justifyContent: 'center',
    },
    placeholderItemContainer: {
        height: 40,
        width: '100%',
        paddingLeft: theme.spacing(5),
        borderColor: 'gray',
        backgroundColor: theme.palette.white,
        justifyContent: 'center',
        borderBottomWidth:1,
        borderBottomColor:theme.palette.black,
      },
    itemTextStyle: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
    },
    placeholderItemTextStyle: {
        ...theme.typography.normal.small,
        color: theme.palette.gray,
      },
    titleTextStyle:{
        ...theme.typography.bold.extraSmall,
        color:theme.palette.black,
        fontSize:9,
    },
    scrollView: {
      flex: 1,
    }
  });

  return styles;
};


const mapStateToProps = (state:IApplicationState) =>{
  return {
    recentSearches:state.recentSearches
  };
}

const connectedKeywordsAutoComplete = connect(mapStateToProps)(KeywordsAutoComplete);
export {connectedKeywordsAutoComplete as KeywordsAutoComplete};