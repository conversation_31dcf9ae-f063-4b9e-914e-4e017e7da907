{"name": "com.apply4u", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "androidRelease": "cd android && gradlew bundleRelease && gradlew assembleRelease", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-native-fontawesome": "^0.3.0", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/slider": "^4.5.1", "@react-native-picker/picker": "^2.7.2", "@react-navigation/bottom-tabs": "^6.0.5", "@react-navigation/drawer": "^6.1.4", "@react-navigation/native": "^6.0.1", "@react-navigation/stack": "^6.0.1", "@testing-library/jest-native": "^5.4.3", "@types/react-native-fs": "^2.13.0", "@types/react-native-loading-spinner-overlay": "^0.5.6", "@types/react-native-vector-icons": "^6.4.18", "axios": "^0.21.1", "babel-plugin-module-resolver": "^5.0.0", "buffer": "^6.0.3", "i18next": "^20.3.5", "module-alias": "^2.2.3", "moment": "^2.30.1", "react": "18.2.0", "react-i18next": "^11.11.4", "react-native": "0.73.6", "react-native-bouncy-checkbox": "^4.0.0", "react-native-device-info": "^10.13.1", "react-native-document-picker": "^7.1.1", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.16.0", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-navbar-color": "^1.0.3", "react-native-paper": "^5.13.5", "react-native-progress-circle": "^2.1.0", "react-native-reanimated": "3.8.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.30.1", "react-native-svg": "^15.1.0", "react-native-testing-library": "^6.0.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.8.4", "react-redux": "^9.1.0", "redux": "^5.0.1", "redux-immutable-state-invariant": "^2.1.0", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "save": "^2.9.0", "save-dev": "^0.0.1-security"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}