# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 32ms
  [gap of 11ms]
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 80ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 107ms
  [gap of 18ms]
generate_cxx_metadata completed in 163ms

# C/C++ build system timings
generate_cxx_metadata 36ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 31ms
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 65ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 19ms
  [gap of 11ms]
generate_cxx_metadata completed in 46ms

