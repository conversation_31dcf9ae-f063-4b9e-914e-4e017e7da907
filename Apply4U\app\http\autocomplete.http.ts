import {http} from './http-base';
import {IAutoComplete} from '../interfaces';
import { CancelTokenSource } from 'axios';

const getLocationAutoComplete = async (
  searchText: string,
): Promise<IAutoComplete[]> => {
  let url: string = `api/AutoComplete/locations?pattern=${searchText}`;
  let result = await http
    .get<IAutoComplete[]>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const getSectorsAutoComplete = async (
  searchText: string,
  cancelToken:CancelTokenSource
): Promise<IAutoComplete[]> => {
  let url: string = `api/AutoComplete/sectors?prefix=${searchText}&searchInParent=false&searchInChildren=true`;
  let result = await http
    .get<IAutoComplete[]>(url,{cancelToken:cancelToken.token})
    .catch(error => Promise.reject(error));

  return result.data;
};

const autocompleteApi = {
  getLocationAutoComplete,
  getSectorsAutoComplete,
};

export {autocompleteApi};
