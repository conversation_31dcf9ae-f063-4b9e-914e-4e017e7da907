import {IAction} from './action.interface';
import {HiddenJobsConstant} from './action.constant';

export const setHiddenJobsAction = (payload: number[]): IAction<number[]> => {
  return {
    type: HiddenJobsConstant.SET_HIDDEN_JOBS,
    payload,
  };
};

export const hideJobAction = (payload:number):IAction<number> => {
  return {
    type:HiddenJobsConstant.HIDE_JOB,
    payload,
  };
}

export const unHideJobAction = (payload:number):IAction<number> =>{
  return {
    type:HiddenJobsConstant.UN_HIDE_JOB,
    payload,
  };
}