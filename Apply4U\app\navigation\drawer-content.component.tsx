import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
  DrawerItem,
} from '@react-navigation/drawer';
import {useLinkBuilder} from '@react-navigation/native';
import React from 'react';
import {connect} from 'react-redux';
import {screens} from '../app.constant';
import {ITokenResult, IScreen} from '../interfaces';
import {IApplicationState, resetStore} from '../redux';
import {
  getPropertyNames,
  isAuthenticated,
  logout,
  navigate,
  toggleDrawer,
} from '../utility';

interface IDrawerContentComponentProps extends DrawerContentComponentProps {
  token: ITokenResult | null;
}

const DrawerContentComponent = (props: IDrawerContentComponentProps) => {
  const buildLink = useLinkBuilder();
  const state = props.state;
  const navigation = props.navigation;
  const descriptors = props.descriptors;
  const [isLoggedIn, setIsLoggedIn] = React.useState<boolean>(
    isAuthenticated(),
  );

  const hiddenItemsAuthenticatedUser: string[] = [screens.Login.screen];
  const hiddenItemsUnAuthenticatedUser: string[] = [];
  let allRoutesToShowOnDrawer: string[] = [];
  let screenKeys = getPropertyNames(screens);
  if (screenKeys && screenKeys.length > 0) {
    screenKeys.forEach(screenKey => {
      let screen = screens[screenKey];
      if (typeof screen !== 'string') {
        if (screen.showOnDrawer === true) {
          allRoutesToShowOnDrawer = [...allRoutesToShowOnDrawer, screen.screen];
        }
      }
    });
  }

  const [routeState, setRouteState] = React.useState({
    ...state,
    routes: isLoggedIn
      ? state?.routes?.filter(
          m =>
            !hiddenItemsAuthenticatedUser.includes(m.name) &&
            allRoutesToShowOnDrawer.includes(m.name),
        )
      : state?.routes?.filter(
          m =>
            !hiddenItemsUnAuthenticatedUser.includes(m.name) &&
            allRoutesToShowOnDrawer.includes(m.name),
        ),
  });

  React.useEffect(() => {
    let isLoggedInNow = isAuthenticated();
    setIsLoggedIn(isLoggedInNow);
    setRouteState({
      ...state,
      routes: isLoggedInNow
        ? state.routes.filter(
            m =>
              !hiddenItemsAuthenticatedUser.includes(m.name) &&
              allRoutesToShowOnDrawer.includes(m.name),
          )
        : state?.routes?.filter(
            m =>
              !hiddenItemsUnAuthenticatedUser.includes(m.name) &&
              allRoutesToShowOnDrawer.includes(m.name),
          ),
    });
  }, [props.token]);

  return (
    <DrawerContentScrollView {...props}>
      {routeState.routes.map((route, i) => {
        let descriptor = descriptors[route.key];
        if (route.name === 'App') return;
        const focused = state.routeNames.indexOf(route.name) === state.index;
        const {title, drawerLabel, drawerIcon} = descriptors[route.key].options;

        return (
          <DrawerItem
            key={route.key}
            label={
              drawerLabel !== undefined
                ? drawerLabel
                : title !== undefined
                ? title
                : route.name
            }
            {...descriptor}
            icon={drawerIcon}
            focused={focused}
            activeTintColor={descriptor.options.drawerActiveTintColor}
            inactiveTintColor={descriptor.options.drawerInactiveTintColor}
            activeBackgroundColor={
              descriptor.options.drawerActiveBackgroundColor
            }
            inactiveBackgroundColor={
              descriptor.options.drawerInactiveBackgroundColor
            }
            labelStyle={descriptor.options.drawerLabelStyle}
            style={descriptor.options.drawerItemStyle}
            to={buildLink(route.name, route.params)}
            onPress={() => {
              if (focused) {
                toggleDrawer();
              } else {
                navigate(route.name);
              }
            }}
          />
        );
      })}
      {isLoggedIn && (
        <DrawerItem
          label={screens.LogoutDrawerLabel}
          onPress={() => {
            toggleDrawer();
            navigate(screens.Login);
            logout();
            //resetStore();
          }}
        />
      )}
    </DrawerContentScrollView>
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedDrawerContentComponent = connect(mapStateToProps)(
  DrawerContentComponent,
);

export {connectedDrawerContentComponent as DrawerContentComponent};
