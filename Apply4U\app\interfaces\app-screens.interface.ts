import {IScreen} from './screen.interface';

export interface IAppScreens {
  Default: string;
  LogoutDrawerLabel: string;
  JobDetail: IScreen;
  CreateAccount: IScreen;
  OtpVerification: IScreen;
  SuccessScreen: IScreen;
  JobSearchResults: IScreen;
  JobSearchFilter: IScreen;
  NewRegisterIntro: IScreen;
  NewRegisterStepOne: IScreen;
  NewRegisterStepTwo: IScreen;
  NewRegisterStepThree: IScreen;
  NewRegisterStepFour: IScreen;
  NewRegisterStepFive: IScreen;
  RegisterStepOne: IScreen;
  RegisterStepTwo: IScreen;
  RegisterStepThree: IScreen;
  RegisterStepFour: IScreen;
  RegisterStepFive: IScreen;
  Dashboard: IScreen;
  ForgotPassword: IScreen;
  NewForgotPassword: IScreen;
  NewLogin: IScreen;
  Home: IScreen;
  Login: IScreen;
  Notifications: IScreen;
  Pricing: IScreen;
  IntroSlider: IScreen;
  ApplyNow: IScreen;
  ApplySuccessful: IScreen;
  HiddenJobs:IScreen;
  MyJobs:IScreen;
  ShortlistedJobs:IScreen;
  Browser:IScreen;
  Settings:IScreen;
  CvUpload:IScreen;
  [key: string]: IScreen | string;
}
