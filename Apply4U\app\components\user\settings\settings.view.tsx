import React from 'react';
import { Keyboard, StyleSheet, View } from 'react-native';
import { Button, MainLayout, Text } from '../../common';
import { ITheme, useCurrentTheme } from '../../../theme';

interface IProps{
    onDeleteClick:() => void;
}

export const SettingsView = (props:IProps) =>{
    const styles = useCurrentTheme(createStyles);
    return <MainLayout>
        <View style={styles.header2}>
        <View style={styles.header2Text}>
          <Text styles={styles.notificationHeadText} text={'Account Settings'} />
        </View>
      </View>
        {<View>
        <Text styles={styles.textStyle} text={`Once you delete this account, all your information on Apply4U will be lost forever. We will not be able to restore your account once this action is done.`} />
        </View>}
        <Button
                            styles={styles.btnFindJobs}
                            textStyles={styles.othersZIndex}
                            pressed={() => {
                                Keyboard.dismiss();
                                props.onDeleteClick();
                            }}
                            text={'Delete Account'}
                        />
    </MainLayout>;
}

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        btnFindJobs: {
            ...theme.buttons.danger,
            width: '60%',
            alignSelf: 'center',
            borderRadius: theme.borderRadius(7),
            ...theme.zIndex(1),
        },
        othersZIndex: {
            ...theme.zIndex(1),
        },
        textStyle:{
            ...theme.typography.normal.medium,
            alignSelf:'center',
            marginTop:theme.spacing(7),
            marginBottom:theme.spacing(3),
            padding:theme.spacing(7),
            color:'#454545'
          },
          header2: {
            height: 60,
            backgroundColor: theme.palette.primary,
            justifyContent: 'center',
          },
          header2Text: {
            color: '#fff',
            fontSize: 18,
            fontWeight: 'bold',
            letterSpacing: 0.5,
            alignSelf: 'center',
          },
          notificationHeadText:{
            ...theme.typography.bold.large
          }
    });

    const colors = { yesButtonColor: theme.palette.error, noButtonColor:theme.palette.lightGray }
    return { ...styles, ...colors };
};