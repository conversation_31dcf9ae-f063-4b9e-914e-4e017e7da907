import {IUserProfile} from './user-profile.interface';
import {IAddress} from './address.interface';
import {IFile} from './file.interface';
export interface IUserDetail {
  UserProfile?: IUserProfile;
  LoginName?: string;
  Id?: number;
  Emails?: any;
  UserJobPreference?: any;
  Password?: string;
  WebAddresses?: any;
  LastLoginOn?: string;
  Addresses?: IAddress[];
  RegisteredOn?: string;
  Qualifications?: any;
  IsSelfRegistered?: boolean;
  Experiences?: any[];
  IsActive?: boolean;
  Phones?: any;
  IsCvProvided?: boolean;
  UserLanguages?: any;
  CvEvaluation?: number;
  UserSkills?: any;
  InterestedInMatchedJobsByEmail?: boolean;
  IsAdmin?: boolean;
  UserOtherInformation?: any;
  ResumeInformation?: any;
  InitialScreening?: any;
  EducationLevel?: any;
  FileDescription?: any;
  FmCvTermsAgreement?: any;
  IsCvParsed?: boolean;
  Company?: any;
  ClientTrialMessage?: any;
  IsPasswordEncrypted?: boolean;
  UniqueId?: any;
  CompanyJoinRequest?: any;
  UserPreference?: any;
  UserTage?: any;
  File?: IFile;
}
