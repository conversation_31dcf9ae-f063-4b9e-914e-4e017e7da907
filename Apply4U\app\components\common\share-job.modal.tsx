import React from 'react';
import { Modal, KeyboardAvoidingView, StyleSheet, View } from 'react-native';
import { Button, Text, InputText } from '.';
import { InputType } from '../../enum';
import { IShareJob, IShareJobModalProps, IUserDetail } from '../../interfaces';
import { ValidationResults, ValidationRules } from '../../interfaces/validation';
import { ITheme, useCurrentTheme } from '../../theme';
import { getFormattedJobDetailUrlText, validate, validateAll } from '../../utility';
import {jobApi} from '../../http';
import { hideLoading, showLoading } from '../../redux/actions';
import { connect } from 'react-redux';
import { IApplicationState } from '../../redux';
import { showErrorMessage, showSuccessMessage } from '../../external/toaster';
import { apply4UWebUrl } from '../../app.constant';

interface IProps {
    job: IShareJobModalProps | undefined;
    loginUser:IUserDetail|null;
    closeMe: () => void;
    showLoading: (
        loadingText?: string,
        autoHide?: boolean,
        autoHideMilliseconds?: number,
      ) => void;
    hideLoading: () => void;
}
const ShareJobModal = (props: IProps) => {
    const styles = useCurrentTheme(createStyles);
    const [shareJobData,setShareJobData] = React.useState<IShareJob>({emailId:''});
    const validations: ValidationRules<IShareJob> = {
        emailId: {
          validationRules: {
            required: {
              isRequired: true,
              message: 'EmailId required.',
            },
            validEmail: {
              checkValidEmail: true,
              message: 'Invalid email.',
            },
          },
        },
      };

      const [validationResults, setValidationResults] = React.useState<
      ValidationResults<IShareJob>
    >({emailId: {validationResults: []}});
    const resetValidation = (key: keyof IShareJob) => {
      let results = {...validationResults};
      results[key].validationResults = [];
      setValidationResults({...results});
    };

    const validateIt = (key: keyof IShareJob) => {
        let results = validate(shareJobData[key], validations[key]?.validationRules);
        let updatedValidationResults = {...validationResults};
        updatedValidationResults[key].validationResults = results;
        setValidationResults({...updatedValidationResults});
      };

      const handleShareJob = () =>{
        let validateAllResult = validateAll<IShareJob>(validations, shareJobData);
        if (
          validateAllResult.isValid === false &&
          validateAllResult.validationResults
        ) {
          setValidationResults({...validateAllResult.validationResults});
        }
    
        if (validateAllResult.isValid){
            const jobDetailUrl = `${apply4UWebUrl}/jobs/${getFormattedJobDetailUrlText(props.job?.jobTitle??'',props.job?.locationText??'')}/${props.job?.jobId}`;
            props.showLoading();
            jobApi.shareJob(props.job?.jobId??0,props.loginUser?.Id??0,shareJobData.emailId,jobDetailUrl)
            .then(() =>{
                props.closeMe();
                showSuccessMessage('Shared Successfully');
            })
            .catch(() =>{
                showErrorMessage('Unable to share job.');
            })
            .finally(() =>{
                props.hideLoading();
            });
        }
      }
      const handleCancel = () => {
          props.closeMe();
      }
    
    return (
        <Modal
            animationType="none"
            transparent={true}
            visible={!!props.job}
        >
            <View style={styles.centeredView}>
                <View style={styles.modalView}>
                    <View style={styles.header}>
                        <Text styles={styles.headerText} text={`Share Job [${props.job?.jobId}]`} />
                    </View>

                    <View style={styles.content}>
                        <KeyboardAvoidingView enabled>
                            <InputText
                                name={'emailId'}
                                inputType={InputType.EmailAddress}
                                onChangeText={(value:string,name:string) =>{setShareJobData({emailId:value});}}
                                placeholder={'Enter Email'}
                                value={shareJobData.emailId}
                                validationResults={
                                    validationResults?.emailId?.validationResults
                                }
                                onBlur={e => {
                                    validateIt('emailId');
                                }}
                                onFocus={e => {
                                    resetValidation('emailId');
                                }}
                                onClick={()=>{
                                    resetValidation('emailId');
                                }}
                                styles={styles.inputTextStyle}
                            />
                        </KeyboardAvoidingView>
                    </View>
                    <View style={styles.footer}>
                        <View style={styles.buttonContainer}>
                            <Button pressed={handleShareJob} styles={styles.shareButton} text={'Share'} />
                        </View>
                        <View style={styles.buttonContainer}>
                            <Button pressed={handleCancel} styles={styles.closeButton} text={'Cancel'} />
                        </View>
                    </View>
                </View>
            </View>
        </Modal>
    );
}

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        centeredView: {
            flex: 1,
            alignItems: "center",
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
        },
        modalView: {
            flexDirection: 'column',
            backgroundColor: theme.palette.white,
            borderRadius: 10,
            height: 200,
            //padding: 35,
            alignItems: "center",
            shadowColor: theme.palette.lightGray,
            shadowOffset: {
                width: 0,
                height: 2
            },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
            width: '95%',
            margin: 20,
        },

        header: {
            height: 50,
            justifyContent: 'center',
            //backgroundColor:'green',
            width: '100%',
            borderBottomWidth: 2,
            borderBottomColor: theme.palette.lightGray,
        },
        footer: {
            height: 50,
            justifyContent: 'center',
            alignItems: 'center',
            //backgroundColor:'green',
            width: '100%',
            flex: 1,
            flexDirection: 'row',
        },
        content: {
            height: 90,
            justifyContent: 'center',
            //backgroundColor:'green',
            width: '100%',
            paddingHorizontal:theme.spacing(5),
        },
        headerText: {
            ...theme.typography.bold.large,
            color: '#333333',
            alignSelf: 'center'
        },
        shareButton: {
            width: 150,
            alignSelf: 'flex-end',
            borderRadius: theme.borderRadius(5),
            height: 40,
            marginTop: 0,
            marginRight: theme.spacing(2),
        },
        closeButton: {
            ...theme.buttons.secondary,
            width: 150,
            alignSelf: 'flex-start',
            borderRadius: theme.borderRadius(5),
            height: 40,
            marginTop: 0,
            marginLeft: theme.spacing(2),
        },
        buttonContainer: {
            flex: 1,
        },
        inputTextStyle:{
            borderRadius:theme.borderRadius(5),
        }
    });

    return styles;
};

const mapStateToProps = (state: IApplicationState) => {
    return {
      loginUser: state.loginUser,
    };
  };
  
const mapDispatchToProps = {
    showLoading,
    hideLoading,
  };
  
  const connectedShareJobModal = connect(
    mapStateToProps,
    mapDispatchToProps,
  )(ShareJobModal);
  export {connectedShareJobModal as ShareJobModal};