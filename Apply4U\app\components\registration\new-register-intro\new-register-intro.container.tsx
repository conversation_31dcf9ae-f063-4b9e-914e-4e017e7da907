import React from 'react';
import { NewRegisterIntroView } from './new-register-intro.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterIntroContainer = () => {
  return <NewRegisterIntroView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterIntroContainer);

export { connectedNewRegisterContainer as NewRegisterIntroContainer };
