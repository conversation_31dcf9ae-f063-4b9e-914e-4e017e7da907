import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Pressable,
  Animated,
  Easing,
} from 'react-native';
import {Text} from 'react-native-paper';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { ITheme, useCurrentTheme } from '../../theme';
import { navigate } from '../../utility';
import { BlueheadContainer } from './bluehead-container';
import { SegmentedProgressCircle } from './segmented-progress-circle';

interface RegistrationStepProps {
  title: string;
  currentStep: number;
  totalSteps: number;
  onContinue: () => void;
  onSkip: () => void;
  onBack: () => void;
  onHome: () => void;
  children: React.ReactNode;
}

export const RegistrationStepComponent = (props: RegistrationStepProps) => {
  const {
    title,
    currentStep,
    totalSteps,
    onContinue,
    onSkip,
    onBack,
    onHome,
    children,
  } = props;

  const { styles, theme } = useCurrentTheme(createStyles);
  const [dotAnimations, setDotAnimations] = useState<Animated.Value[]>([]);

  useEffect(() => {
    const animations = Array.from({ length: totalSteps }, (_, i) =>
      new Animated.Value(i === currentStep - 1 ? 1 : 0)
    );
    setDotAnimations(animations);
  }, [totalSteps]);

  useEffect(() => {
    dotAnimations.forEach((anim, index) => {
      Animated.timing(anim, {
        toValue: index === currentStep - 1 ? 1 : 0,
        duration: 300,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
        useNativeDriver: false,
      }).start();
    });
  }, [currentStep, dotAnimations]);

  return (
    <BlueheadContainer
      showBackButton={true}
      showHomeButton={true}
      onHomePress={onHome}
      onBackPress={onBack}
    >
      <View style={styles.container}>
        <View style={styles.headingContainer}>
          <Text style={styles.headingTitle}>
            {title}
          </Text>
        </View>

        <View style={styles.formContainer}>
          {children}
        </View>

        <View style={styles.bottomContainer}>
          <View style={styles.bottomContainerRow1}>
            <Pressable onPress={onSkip} style={styles.skipButton}>
              <Text style={styles.skipButtonText}>Skip</Text>
            </Pressable>
          </View>

          <View style={styles.bottomContainerRow2}>
            <View style={styles.paginationWrapper}>
              {Array.from({ length: totalSteps }, (_, index) => {
                if (dotAnimations.length === 0) {
                  return (
                    <View
                      key={index}
                      style={[
                        styles.paginationDots,
                        index === currentStep - 1 ? styles.activePaginationDot : styles.inactivePaginationDot,
                      ]}
                    />
                  );
                }

                const dotWidth = dotAnimations[index].interpolate({
                  inputRange: [0, 1],
                  outputRange: [8, 24],
                });

                const backgroundColor = dotAnimations[index].interpolate({
                  inputRange: [0, 1],
                  outputRange: ['#D9D9D9', theme.palette.primary],
                });

                return (
                  <Animated.View
                    key={index}
                    style={[
                      styles.paginationDots,
                      {
                        width: dotWidth,
                        backgroundColor,
                      },
                    ]}
                  />
                );
              })}
            </View>
          </View>

          <View style={styles.bottomContainerRow3}>
            <View style={styles.nextButtonContainer}>
              <SegmentedProgressCircle
                progress={(currentStep) / totalSteps}
                segmentCount={totalSteps}
                radius={30}
                strokeWidth={6}
                activeColor="#0F1E5A"
                inactiveColor="#D0E1FF"
                bgColor="#F5F9FF">
                <Pressable onPress={onContinue} style={styles.nextButtonInner}>
                  <View style={styles.arrowContainer}>
                    <FontAwesomeIcon icon={faChevronRight} color="#FFFFFF" size={20} />
                  </View>
                </Pressable>
              </SegmentedProgressCircle>
            </View>
          </View>
        </View>
      </View>
    </BlueheadContainer>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
    },
    headingContainer: {
      width: '90%',
      marginBottom: 5,
      alignItems: 'center',
    },
    headingTitle: {
      fontSize: 26,
      fontFamily: 'Poppins-Bold',
      color: '#0E1C5D',
      marginBottom: 10,
      textAlign: 'center',
    },
    formContainer: {
      width: '90%',
    },
    bottomContainer: {
      flexDirection: 'row',
      height: 90,
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      position: 'absolute',
      bottom: 0,
      paddingHorizontal: 20,
      backgroundColor: '#FFFFFF',
    },
    bottomContainerRow1: {
      flex: 1,
      justifyContent: 'center',
    },
    bottomContainerRow2: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    bottomContainerRow3: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'flex-end',
    },
    skipButton: {
      backgroundColor: '#D9D9D9',
      paddingVertical: 10,
      paddingHorizontal: 40,
      borderRadius: 5,
      alignSelf: 'flex-start',
    },
    skipButtonText: {
      fontSize: 14,
      fontFamily: 'Poppins-SemiBold',
      color: '#0E1B5D',
    },
    paginationWrapper: {
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    paginationDots: {
      height: 8,
      width: 8,
      borderRadius: 4,
      marginLeft: theme.spacing(5),
    },
    activePaginationDot: {
      width: 24,
      backgroundColor: theme.palette.primary,
      height: 8,
      borderRadius: 4,
    },
    inactivePaginationDot: {
      backgroundColor: '#D9D9D9',
    },
    nextButtonContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing(5),
    },
    nextButtonInner: {
      width: '100%',
      height: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    arrowContainer: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.palette.primary,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: 'white',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 1,
    },
  });

  return { styles, theme };
};
