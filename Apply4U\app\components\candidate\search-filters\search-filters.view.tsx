import React from 'react';
import {
    ScrollView,
    StyleSheet,
    View,
    KeyboardAvoidingView,
    Keyboard,
} from 'react-native';
import { useCurrentTheme, useTheme, ITheme } from '../../../theme';
import {
    Text,
    InputText,
    Button,
    MainLayout,
    LocationAutoComplete,
    KeywordsAutoComplete,
    MilesAwaySlider,
    Checkbox,
    DropDown,
    Collapsible
} from '../../common';
import { InputType } from '../../../enum';
import { useTranslation } from 'react-i18next';
import {
    IDropDownDataSource,
    IEnum,
    IAutoComplete,
    ISearchParam,
    ISector,
} from '../../../interfaces';
import { getChunks } from '../../../utility';
import {
    anyJobStatusId,
    anyJobTypeId,
    dropdownPlaceholderValue,
    maximumSearchRadius,
    milesAwaySliderStep,
    minimumSearchRadius,
    searchDefaultRadius,
    zIndex,
} from '../../../app.constant';
import { faPoundSign } from '@fortawesome/free-solid-svg-icons';

interface IProps {
    jobStatuses: IEnum[] | undefined;
    jobTypes: IEnum[] | undefined;
    salaryPerDataSource: IEnum[];
    sectorsDataSource: ISector[];
    searchWithinDataSource: IDropDownDataSource[];
    handleJobTypesChange: (selectedJobTypes: number[]) => void;
    handleJobStatusesChange: (selectedJobStatuses: number[]) => void;
    handleChange: (value: any, name: string) => void;
    searchParam?: ISearchParam | null;
    handleLocationChange: (
        value: any,
        name: keyof ISearchParam,
        selectedLocationFeild: keyof ISearchParam,
        selectedLocation: IAutoComplete | null | undefined,
    ) => void;
    findJobs: () => void;
    isSearchResultsPage: boolean;
}
const SearchFiltersViewContent = (props: IProps) => {
    const { t, i18n } = useTranslation(['SearchFilter']);
    const [showFilters, setShowFilters] = React.useState<boolean>(
        props?.searchParam?.applyFilters ?? false,
    );
    const styles = useCurrentTheme(createStyles);
    const [selectedJobTypes, setSelectedJobTypes] = React.useState<number[]>(
        props.searchParam?.selectedJobTypes ?? [],
    );
    const [selectedJobStatuses, setSelectedJobStatuses] = React.useState<
        number[]
    >(props.searchParam?.selectedJobCategories ?? []);

    const handleJobStatusesChange = (id: number) => {
        let statuses = [...selectedJobStatuses];
        if (statuses.includes(id)) {
            let filtered: number[] = [];

            if (id !== anyJobStatusId) {
                filtered = statuses.filter(m => m !== id);
            }

            setSelectedJobStatuses([...filtered]);
            props.handleJobStatusesChange([...filtered]);
        } else {
            let newStatuses = [anyJobStatusId];

            if (id !== anyJobStatusId) {
                let withoutAny: number[] = statuses.filter(m => m !== anyJobStatusId);
                newStatuses = [...withoutAny, id];
            }

            setSelectedJobStatuses([...newStatuses]);
            props.handleJobStatusesChange([...newStatuses]);
        }
    };

    const handleJobTypesChange = (id: number) => {
        let types = [...selectedJobTypes];
        if (types.includes(id)) {
            let filtered: number[] = [];

            if (id !== anyJobTypeId) {
                filtered = types.filter(m => m !== id);
            }

            setSelectedJobTypes([...filtered]);
            props.handleJobTypesChange([...filtered]);
        } else {
            let newType = [anyJobTypeId];

            if (id !== anyJobTypeId) {
                let withoutAny: number[] = types.filter(m => m !== anyJobTypeId);
                newType = [...withoutAny, id];
            }

            setSelectedJobTypes([...newType]);
            props.handleJobTypesChange([...newType]);
        }
    };

    const toggleFilters = () => {
        const applyFilters = !showFilters;
        props.handleChange(applyFilters, 'applyFilters');
        setShowFilters(applyFilters);
    };

    const [isSalaryAreaCollapsed, setIsSalaryAreaCollapsed] = React.useState<boolean>(!props.isSearchResultsPage);
    const [isJobTypeAreaCollapsed, setIsJobTypeAreaCollapsed] = React.useState<boolean>(true);
    const [isJobStatusAreaCollapsed, setIsJobStatusAreaCollapsed] = React.useState<boolean>(true);
    const [isIndustriesAreaCollapsed, setIsIndustriesAreaCollapsed] = React.useState<boolean>(true);
    const [isSearchWithinAreaCollapsed, setIsSearchWithinAreaCollapsed] = React.useState<boolean>(true);
    const [isLocationAutoCompleteShowingSuggestions, setIsLocationAutoCompleteShowingSuggestions] = React.useState<boolean>(false);
    const [isKeywordsAutoCompleteShowingSuggestions, setIsKeywordsAutoCompleteShowingSuggestions] = React.useState<boolean>(false);

    return (
        <>
            {!props.isSearchResultsPage && <View style={isLocationAutoCompleteShowingSuggestions || isKeywordsAutoCompleteShowingSuggestions ? styles.keywordsViewHeight : styles.keywordsView}>
                <KeywordsAutoComplete
                    autoCompletekey={'searchFiltersKeywords'}
                    name={'keywords'}
                    key='Same'
                    placeholder={t('Placeholders.What')}
                    styles={styles.inputField}
                    value={props.searchParam?.keywords}
                    onChange={props.handleChange}
                    onOpen={() => { setIsKeywordsAutoCompleteShowingSuggestions(true); }}
                    onClose={() => { setIsKeywordsAutoCompleteShowingSuggestions(false); }}
                    addTypoToItems={false}
                />

                {!isKeywordsAutoCompleteShowingSuggestions && <LocationAutoComplete
                    name={'locationText'}
                    placeholder={t('Placeholders.Where')}
                    styles={styles.inputField}
                    selectedItem={props.searchParam?.selectedLocation}
                    value={props.searchParam?.locationText}
                    onChange={(
                        value: string,
                        name: string,
                        selectedItem?: IAutoComplete | null | undefined,
                    ) => {
                        props.handleLocationChange(
                            value,
                            'locationText',
                            'selectedLocation',
                            selectedItem,
                        );
                    }}
                    onOpen={() => { setIsLocationAutoCompleteShowingSuggestions(true); }}
                    onClose={() => { setIsLocationAutoCompleteShowingSuggestions(false); }}
                />}
            </View>}

            {!!props.isSearchResultsPage && <Text styles={styles.filtersHeadingText} text='Filters' />}

            {!!props.isSearchResultsPage && <View style={isLocationAutoCompleteShowingSuggestions ? styles.locationViewHeight : styles.locationView}><LocationAutoComplete
                    name={'locationText'}
                    placeholder={t('Placeholders.Where')}
                    styles={styles.inputField}
                    selectedItem={props.searchParam?.selectedLocation}
                    value={props.searchParam?.locationText}
                    onChange={(
                        value: string,
                        name: string,
                        selectedItem?: IAutoComplete | null | undefined,
                    ) => {
                        props.handleLocationChange(
                            value,
                            'locationText',
                            'selectedLocation',
                            selectedItem,
                        );
                    }}
                    isFilterPage={true}
                    onOpen={() => { setIsLocationAutoCompleteShowingSuggestions(true); }}
                    onClose={() => { setIsLocationAutoCompleteShowingSuggestions(false); }}
                /></View>}

            <ScrollView style={{ flex: 1, ...styles.othersZIndex }} keyboardShouldPersistTaps={'always'}>
                <View style={styles.formWrapper}>
                    <KeyboardAvoidingView enabled>
                        {!props.isSearchResultsPage && <MilesAwaySlider
                            name={'radius'}
                            minimumValue={minimumSearchRadius}
                            maximumValue={maximumSearchRadius}
                            step={milesAwaySliderStep}
                            value={props.searchParam?.radius ?? searchDefaultRadius}
                            onValueChange={props.handleChange}
                        />}

                        {!props.isSearchResultsPage && <Button
                            styles={styles.btnFilter}
                            textStyles={styles.btnFilterTextStyle}
                            pressed={() => {
                                Keyboard.dismiss();
                                toggleFilters();
                            }}>
                            <View style={styles.buttonIcon}>
                                {/* <FontAwesomeIcon icon={faFilter} style={styles.faColor} /> */}
                                {/* <Text styles={styles.buttonTextDefault} text={`${!showFilters ? 'Show' : 'Hide'} Filters`} /> */}
                                <Text styles={styles.buttonTextDefault} text={`Filter`} />
                            </View>
                        </Button>}
                        {(!!props.isSearchResultsPage || !!showFilters) && (
                            <View style={!!props.isSearchResultsPage ? styles.collapsibleContainerSearResultsPage : styles.collapsibleContainer}>
                                <Collapsible isCollapsed={isSalaryAreaCollapsed} title='Salary' onCollapseChanged={() => { setIsSalaryAreaCollapsed(!isSalaryAreaCollapsed); }}>
                                    <View style={{ flex: 1, flexDirection: 'row' }}>
                                        <InputText
                                            name={'salaryFrom'}
                                            inputType={InputType.Numeric}
                                            placeholder={'Salary From'}
                                            styles={styles.inputField}
                                            value={props.searchParam?.salaryFrom}
                                            onChangeText={props.handleChange}
                                            icon={faPoundSign}
                                            inputIconSize={18}
                                            height={50}
                                        />
                                    </View>
                                    <View style={{ flex: 1, flexDirection: 'row' }}>
                                        <InputText
                                            name={'salaryTo'}
                                            inputType={InputType.Numeric}
                                            placeholder={'Salary To'}
                                            styles={styles.inputField}
                                            value={props.searchParam?.salaryTo}
                                            onChangeText={props.handleChange}
                                            icon={faPoundSign}
                                            inputIconSize={18}
                                            height={50}
                                        />
                                    </View>
                                    <View style={{ flex: 1, flexDirection: 'row' }}>
                                        <DropDown<IEnum>
                                            dataSource={props.salaryPerDataSource}
                                            mode={'dialog'}
                                            name={'salaryPer'}
                                            onChange={(
                                                value: IEnum | number | string | null,
                                                name: string,
                                            ) => {
                                                let salaryPer: IEnum | null = null;
                                                if (
                                                    typeof value !== 'number' &&
                                                    typeof value !== 'string'
                                                ) {
                                                    salaryPer = value;
                                                }

                                                props.handleChange(salaryPer, name);
                                            }}
                                            placeholder={'Salary Per'}
                                            value={props.searchParam?.salaryPer ?? -1}
                                            displayFeild={'Description'}
                                            valueFeild={'Id'}
                                        />
                                    </View>
                                </Collapsible>
                                <Collapsible title={'Job Type'} isCollapsed={isJobTypeAreaCollapsed} onCollapseChanged={() => { setIsJobTypeAreaCollapsed(!isJobTypeAreaCollapsed) }}>
                                    {!!props.jobTypes &&
                                        props.jobTypes.length > 0 &&
                                        getChunks(props.jobTypes, 2).map((items: IEnum[]) => {
                                            let item1: IEnum | undefined = undefined;
                                            let item2: IEnum | undefined = undefined;
                                            item1 = items[0];

                                            if (items.length > 1) {
                                                item2 = items[1];
                                            }

                                            return (
                                                <View
                                                    key={`items_${item1.Id}`}
                                                    style={{ flex: 1, flexDirection: 'column' }}>
                                                    <View style={{ flex: 1, flexDirection: 'row' }}>
                                                        {!!item1 && (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                }}>
                                                                <Checkbox
                                                                    isChecked={
                                                                        selectedJobTypes.indexOf(item1.Id) >= 0
                                                                    }
                                                                    onChange={(isChecked: boolean) => {
                                                                        handleJobTypesChange(item1?.Id ?? 0);
                                                                    }}
                                                                    text={item1.Description}
                                                                />
                                                            </View>
                                                        )}
                                                        {!!item2 && (
                                                            <View style={{ flex: 1 }}>
                                                                <Checkbox
                                                                    isChecked={
                                                                        selectedJobTypes.indexOf(item2.Id) >= 0
                                                                    }
                                                                    onChange={(isChecked: boolean) => {
                                                                        handleJobTypesChange(item2?.Id ?? 0);
                                                                    }}
                                                                    text={item2.Description}
                                                                />
                                                            </View>
                                                        )}
                                                    </View>
                                                </View>
                                            );
                                        })}
                                </Collapsible>

                                <Collapsible title={'Job Status'} isCollapsed={isJobStatusAreaCollapsed} onCollapseChanged={() => { setIsJobStatusAreaCollapsed(!isJobStatusAreaCollapsed) }}>
                                    {!!props.jobStatuses &&
                                        props.jobStatuses.length > 0 &&
                                        getChunks(props.jobStatuses, 2).map((items: IEnum[]) => {
                                            let item1: IEnum | undefined = undefined;
                                            let item2: IEnum | undefined = undefined;
                                            item1 = items[0];

                                            if (items.length > 1) {
                                                item2 = items[1];
                                            }

                                            return (
                                                <View
                                                    key={`items_${item1.Id}`}
                                                    style={{ flex: 1, flexDirection: 'column' }}>
                                                    <View style={{ flex: 1, flexDirection: 'row' }}>
                                                        {!!item1 && (
                                                            <View
                                                                style={{
                                                                    flex: 1,
                                                                }}>
                                                                <Checkbox
                                                                    isChecked={selectedJobStatuses.includes(
                                                                        item1.Id,
                                                                    )}
                                                                    onChange={(isChecked: boolean) => {
                                                                        handleJobStatusesChange(item1?.Id ?? 0);
                                                                    }}
                                                                    text={item1.Description}
                                                                />
                                                            </View>
                                                        )}
                                                        {!!item2 && (
                                                            <View style={{ flex: 1 }}>
                                                                <Checkbox
                                                                    isChecked={selectedJobStatuses.includes(
                                                                        item2.Id,
                                                                    )}
                                                                    onChange={(isChecked: boolean) => {
                                                                        handleJobStatusesChange(item2?.Id ?? 0);
                                                                    }}
                                                                    text={item2.Description}
                                                                />
                                                            </View>
                                                        )}
                                                    </View>
                                                </View>
                                            );
                                        })}
                                </Collapsible>
                                <Collapsible title='Industries' isCollapsed={isIndustriesAreaCollapsed} onCollapseChanged={() => { setIsIndustriesAreaCollapsed(!isIndustriesAreaCollapsed) }} >
                                    <View style={styles.inputView}>
                                        <DropDown<ISector>
                                            dataSource={props.sectorsDataSource}
                                            mode={'dialog'}
                                            name={'sector'}
                                            onChange={(
                                                value: ISector | number | string | null,
                                                name: string,
                                            ) => {
                                                let sector: ISector | null = null;
                                                if (
                                                    typeof value !== 'number' &&
                                                    typeof value !== 'string'
                                                ) {
                                                    sector = value;
                                                }

                                                props.handleChange(sector, name);
                                            }}
                                            placeholder={'Select Industry'}
                                            value={
                                                props.searchParam?.sector ?? dropdownPlaceholderValue
                                            }
                                            displayFeild={'SectorName'}
                                            valueFeild={'Id'}
                                        />
                                    </View>
                                </Collapsible>

                                <Collapsible title='Search Within Last' isCollapsed={isSearchWithinAreaCollapsed} onCollapseChanged={() => { setIsSearchWithinAreaCollapsed(!isSearchWithinAreaCollapsed) }}>
                                    <View style={styles.inputView}>
                                        <DropDown<IDropDownDataSource>
                                            dataSource={props.searchWithinDataSource}
                                            mode={'dialog'}
                                            name={'searchWithinDays'}
                                            onChange={(
                                                value: IDropDownDataSource | number | string | null,
                                                name: string,
                                            ) => {
                                                let searchWithIn: IDropDownDataSource | null = null;
                                                if (
                                                    typeof value !== 'number' &&
                                                    typeof value !== 'string'
                                                ) {
                                                    searchWithIn = value;
                                                }

                                                props.handleChange(searchWithIn, name);
                                            }}
                                            placeholder={'Search Within Last'}
                                            value={
                                                props.searchParam?.searchWithinDays ??
                                                dropdownPlaceholderValue
                                            }
                                            displayFeild={'label'}
                                            valueFeild={'value'}
                                        />
                                    </View>
                                </Collapsible>
                            </View>
                        )}

        {/* {!!props.isSearchResultsPage && <Button
                            styles={styles.btnFilter}
                            textStyles={styles.btnFilterTextStyle}
                            pressed={() => {
                                Keyboard.dismiss();
                                if(props.hideFiltersOnSearchResultsPage){
                                    props.hideFiltersOnSearchResultsPage();
                                }
                            }}>
                            <View style={styles.buttonIcon}>
                                <Text styles={styles.buttonTextDefault} text={`Hide Filters`} />
                            </View>
                        </Button>} */}

                        <Button
                            styles={styles.btnFindJobs}
                            textStyles={styles.othersZIndex}
                            pressed={() => {
                                Keyboard.dismiss();
                                props.findJobs();
                            }}
                            text={!props.isSearchResultsPage ? t('Buttons.Find_Jobs') : 'Apply Filters'}
                        />
                    </KeyboardAvoidingView>
                </View>
            </ScrollView>
        </>
    );
};

const SearchFiltersView = (props: IProps) => {
    return !!props.isSearchResultsPage ? <SearchFiltersViewContent {...props} /> : <MainLayout><SearchFiltersViewContent {...props} /></MainLayout>
}

export { SearchFiltersView };

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        viewPort: {
            height: '100%',
            width: '100%',
        },
        buttonTextDefault: {
            ...theme.typography.bold.small,
            color: theme.palette.primary,
            ...theme.zIndex(1),
        },
        container: {
            flexDirection: 'column',
            backgroundColor: '#edf1fc',
        },
        header2: {
            height: 60,
            width: '100%',
            flexDirection: 'row',
            backgroundColor: '#5178e1',
            flex: 1,
            justifyContent: 'center',
        },
        header2Icon: {
            width: '10%',
            padding: 8,
            margin: 10,
            fontSize: 24,
            //backgroundColor: 'red',
        },
        header2Text: {
            alignSelf: 'center',
        },
        formWrapper: {
            flex: 1,
            padding: 10,
            width: '100%',
            flexDirection: 'column',
            ...theme.zIndex(1),
        },
        keywordsView: {
            padding: theme.spacing(7.5),
            width: '100%',
            backgroundColor: theme.palette.primary,
            height: 170,
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
        },
        keywordsViewHeight: {
            padding: theme.spacing(7.5),
            width: '100%',
            backgroundColor: theme.palette.primary,
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
            height: '90%',
        },
        locationView: {
            padding: theme.spacing(7.5),
            paddingTop:5,
            width: '100%',
            //backgroundColor: theme.palette.primary,
            height: 80,
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
        },
        locationViewHeight: {
            padding: theme.spacing(7.5),
            width: '100%',
            //backgroundColor: theme.palette.primary,
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
            height: '100%',
        },
        inputView: {
            marginBottom: 10,
            flexDirection: 'row',
        },
        inputField: {
            borderRadius: 6,
        },
        faColor: {
            fontSize: 20,
            color: '#fff',
            marginRight: 5,
        },
        buttonIcon: {
            width: '100%',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'center',
            ...theme.zIndex(1),
        },
        btnFilter: {
            ...theme.buttons.secondary,
            width: '80%',
            alignSelf: 'center',
            backgroundColor: '#F5F5F5',
            borderWidth: 2,
            borderColor: theme.palette.primary,
            borderRadius: theme.borderRadius(7),
            ...theme.zIndex(1),
        },
        btnFilterTextStyle: {
            color: 'green',
            ...theme.zIndex(1),
        },
        btnFindJobs: {
            ...theme.buttons.secondary,
            width: '80%',
            alignSelf: 'center',
            borderRadius: theme.borderRadius(7),
            ...theme.zIndex(1),
        },
        othersZIndex: {
            ...theme.zIndex(1),
        },
        filtersHeadingText:{
            ...theme.typography.bold.small,
            color:theme.palette.primary,
            alignSelf:'center',
            marginTop:theme.spacing(10),
        },
        collapsibleContainer:{
            marginTop:20,
            marginBottom:10,
        },
        collapsibleContainerSearResultsPage:{
            marginBottom:10,
        }
    });

    const colors = { primaryColor: theme.palette.primary }
    return { ...styles, ...colors };
};
