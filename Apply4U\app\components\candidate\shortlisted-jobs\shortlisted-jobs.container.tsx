import React from 'react';
import { connect } from 'react-redux';
import { defaultPageSizeSearchResults, screens } from '../../../app.constant';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import { searchApi } from '../../../http';
import { IJobDetailParam, IJobSearchResponse, ILoadingIndicator, IUserDetail } from '../../../interfaces';
import { IApplicationState } from '../../../redux';
import { hideLoading, removeFromFavJobsAction, showLoading } from '../../../redux/actions';
import { IAction } from '../../../redux/actions/action.interface';
import { applyNow, easyApplyWithDefaultResume, navigate } from '../../../utility';
import { ShortlistedJobsView } from './shortlisted-jobs.view';

interface IProps {
    loginUser: IUserDetail | null;
    favJobs: number[];
    showLoading: (
        loadingText?: string,
        autoHide?: boolean,
        autoHideMilliseconds?: number,
    ) => IAction<ILoadingIndicator>;
    hideLoading: () => IAction<ILoadingIndicator>;
    removeFromFav: (payload: number) => IAction<number>;
}

const ShortlistedJobsContainer = (props: IProps) => {
    const [myJobsResults, setMyJobsResults] = React.useState<IJobSearchResponse[]>([]);
    const [nextJobIndex, setNextJobIndex] = React.useState<number>(0);
    const [noFavJob,setNoFavJob] = React.useState<boolean>(false);

    const [currentPageSize, setCurrentPageSize] = React.useState<number>(
        defaultPageSizeSearchResults,
    );

    const [loadingIsInProcess, setLoadingIsInProcess] =
        React.useState<boolean>(false);
    const [isMoreResultsExists, setIsMoreResultsExists] =
        React.useState<boolean>(true);
    const jobRemoveFromFav = (jobId: number,isFav:boolean) => {
        props.removeFromFav(jobId);
    };

    const handleGoToJobDetail = (jobId: number) => {
        navigate<IJobDetailParam>(screens.JobDetail, { jobId });
    };
    const handleUnHideJob = (jobId: number) => {};

    const handleLoadMore = () => {
        loadMyJobs();
    };

    const handleEasyApply = (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => {
        if (jobId > 0 && jobSourceId > 0 && !!jobTitle) {
          applyNow(jobId, jobSourceId, jobTitle, applicationUrl, isEasyApply ? internalEasyApply : undefined);
        } else {
          applyNow(undefined, 0, '', applicationUrl, isEasyApply ? internalEasyApply : undefined);
        }
      };

      const internalEasyApply = (jobId:number) => {
          props.showLoading();
        easyApplyWithDefaultResume(props?.loginUser?.Id ?? 0,jobId,(errorMessage:string) =>{
            props.hideLoading();
            showErrorMessage(errorMessage);
        },(successMessage:string) => {
            setMyJobsResults(oldResults => {
                return oldResults.map(m => {
                    if(m.Id == jobId){
                        m.IsApplied = true;
                    }
    
                    return m;
                });
            });
            props.hideLoading();
            showSuccessMessage(successMessage);
        });
      }

    const loadResults = (
        myJobIds: number[],
        onSuccess: () => void,
    ) => {
        if (!loadingIsInProcess) {
            setLoadingIsInProcess(true);
            props.showLoading();

            searchApi
                .getJobsByIds(
                    myJobIds,
                    props.loginUser?.Id,
                )
                .then((results: IJobSearchResponse[]) => {
                    if (!!results  && results.length > 0) {
                        const favMarkedResults = results.map(m =>
                            {
                                m.IsFav = true;
                                return m;
                            });
                        setMyJobsResults([...myJobsResults, ...favMarkedResults]);
                    }
                    onSuccess();
                })
                .catch((err: any) => {
                    props.hideLoading();
                })
                .finally(() => {
                    props.hideLoading();
                    setLoadingIsInProcess(false);
                });
        }
    };

    React.useEffect(() => {
        setMyJobsResults([]);
        setNextJobIndex(0);
        loadMyJobs();
    }, []);

      React.useEffect(() => {
          if(!!myJobsResults && myJobsResults.length > 0){
              const fJobs = !!props.favJobs ? props.favJobs : [];
              const filteredMyJobsResults = myJobsResults.filter(m => fJobs.indexOf(m.Id) >= 0);
              setMyJobsResults([...filteredMyJobsResults]);
          }

          setNoFavJob(!props.favJobs || props.favJobs.length <= 0);
      },[props.favJobs]);

    const loadMyJobs = () => {
        if (!!props.favJobs && props.favJobs.length > 0) {
            if (nextJobIndex < props.favJobs.length) {
                let lastJobIndex = (currentPageSize - 1) + nextJobIndex;

                if (lastJobIndex >= props.favJobs.length) {
                    lastJobIndex = props.favJobs.length - 1;
                }

                let jobIds = props.favJobs.slice(nextJobIndex, (lastJobIndex + 1));//adding 1 because end is not inclusive

                loadResults(jobIds, () => {
                    setNextJobIndex(lastJobIndex + 1);
                    setIsMoreResultsExists(lastJobIndex + 1 < props.favJobs.length);
                });
            }
        }else{
            setNoFavJob(true);
        }
    }
    return <ShortlistedJobsView
        myJobsResults={myJobsResults}
        loadMore={handleLoadMore}
        onHideJob={handleUnHideJob}
        jobRemoveFromFav={jobRemoveFromFav}
        goToJobDetail={handleGoToJobDetail}
        handleEasyApply={handleEasyApply}
        isMoreResultsExists={isMoreResultsExists}
        noFavJobs={noFavJob}
    />;
}

const mapStateToProps = (state: IApplicationState) => {
    return {
        favJobs: state.favJobs,
        loginUser: state.loginUser
    };
}

const mapDispatchToProps = {
    showLoading: showLoading,
    hideLoading: hideLoading,
    removeFromFav:removeFromFavJobsAction
};

const connectedShortlistedJobsContainer = connect(mapStateToProps, mapDispatchToProps)(ShortlistedJobsContainer);
export { connectedShortlistedJobsContainer as ShortlistedJobsContainer };