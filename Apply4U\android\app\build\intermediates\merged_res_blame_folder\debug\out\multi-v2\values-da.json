{"logs": [{"outputFile": "com.apply4u.app-mergeDebugResources-36:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13659b94c66de92d98d7f8af61234b95\\transformed\\core-1.9.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "12149", "endColumns": "100", "endOffsets": "12245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3627ca143232ec2ceb6e319eb18971d\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,11453", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,11528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3a8a24834f295d8078f18b8298041f7\\transformed\\material-1.9.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1072,1158,1231,1291,1378,1442,1504,1566,1634,1699,1755,1873,1931,1992,2048,2123,2249,2335,2415,2556,2634,2714,2800,2856,2907,2973,3041,3115,3204,3276,3354,3424,3497,3581,3658,3746,3835,3909,3982,4067,4116,4182,4262,4345,4407,4471,4534,4642,4737,4838,4933,4993,5048", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,85,55,50,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,59,54,79", "endOffsets": "267,345,421,499,596,676,776,925,1003,1067,1153,1226,1286,1373,1437,1499,1561,1629,1694,1750,1868,1926,1987,2043,2118,2244,2330,2410,2551,2629,2709,2795,2851,2902,2968,3036,3110,3199,3271,3349,3419,3492,3576,3653,3741,3830,3904,3977,4062,4111,4177,4257,4340,4402,4466,4529,4637,4732,4833,4928,4988,5043,5123"}, "to": {"startLines": "2,34,35,36,37,38,40,41,42,62,63,65,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3050,3128,3204,3282,3379,3544,3644,3793,6645,6709,6870,7097,7157,7244,7308,7370,7432,7500,7565,7621,7739,7797,7858,7914,7989,8266,8352,8432,8573,8651,8731,8817,8873,8924,8990,9058,9132,9221,9293,9371,9441,9514,9598,9675,9763,9852,9926,9999,10084,10133,10199,10279,10362,10424,10488,10551,10659,10754,10855,10950,11010,11065", "endLines": "5,34,35,36,37,38,40,41,42,62,63,65,68,69,70,71,72,73,74,75,76,77,78,79,80,81,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,77,75,77,96,79,99,148,77,63,85,72,59,86,63,61,61,67,64,55,117,57,60,55,74,125,85,79,140,77,79,85,55,50,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,59,54,79", "endOffsets": "317,3123,3199,3277,3374,3454,3639,3788,3866,6704,6790,6938,7152,7239,7303,7365,7427,7495,7560,7616,7734,7792,7853,7909,7984,8110,8347,8427,8568,8646,8726,8812,8868,8919,8985,9053,9127,9216,9288,9366,9436,9509,9593,9670,9758,9847,9921,9994,10079,10128,10194,10274,10357,10419,10483,10546,10654,10749,10850,10945,11005,11060,11140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,216,299,374,445,528,603,679,760,840,909,987,1066,1142,1222,1302,1379,1450,1520,1603,1677,1759", "endColumns": "75,84,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,211,294,369,440,523,598,674,755,835,904,982,1061,1137,1217,1297,1374,1445,1515,1598,1672,1754,1833"}, "to": {"startLines": "33,39,43,64,66,67,82,83,121,122,123,124,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2974,3459,3871,6795,6943,7014,8115,8190,11145,11226,11306,11375,11533,11612,11688,11768,11848,11925,11996,12066,12250,12324,12406", "endColumns": "75,84,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "3045,3539,3949,6865,7009,7092,8185,8261,11221,11301,11370,11448,11607,11683,11763,11843,11920,11991,12061,12144,12319,12401,12480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4343b2ae69420088cd050005b2aafd9c\\transformed\\jetified-play-services-basement-17.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "185", "endOffsets": "432"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5107", "endColumns": "185", "endOffsets": "5288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19ec269da49c6c76309942cfa013a60d\\transformed\\jetified-play-services-base-17.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,541,666,773,975,1098,1244,1374,1589,1693,1886,2011,2215,2419,2511,2599", "endColumns": "103,191,124,106,201,122,145,129,214,103,192,124,203,203,91,87,102", "endOffsets": "348,540,665,772,974,1097,1243,1373,1588,1692,1885,2010,2214,2418,2510,2598,2701"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3954,4062,4254,4383,4494,4696,4823,4973,5293,5508,5616,5809,5938,6142,6350,6446,6538", "endColumns": "107,191,128,110,201,126,149,133,214,107,192,128,203,207,95,91,106", "endOffsets": "4057,4249,4378,4489,4691,4818,4968,5102,5503,5611,5804,5933,6137,6345,6441,6533,6640"}}]}]}