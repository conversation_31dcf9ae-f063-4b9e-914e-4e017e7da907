# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 50ms
  [gap of 15ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 111ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 25ms]
generate_cxx_metadata completed in 40ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 41ms
  [gap of 12ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 14ms
  [gap of 10ms]
generate_cxx_metadata completed in 41ms

