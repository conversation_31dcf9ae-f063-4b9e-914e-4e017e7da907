import React from 'react';
import { showErrorMessage } from '../../../external/toaster';
import { loginApi, userApi } from '../../../http';
import {
  getParams,
  isNullOrEmpty,
  navigate,
} from '../../../utility';
import { connect } from 'react-redux';
import {
  hideLoading,
  setTokenAction,
  showLoading,
  setLoginUserDetailAction,
} from '../../../redux/actions';
import { IApplicationState } from '../../../redux';
import {
  ILoginParam,
  ITokenResult,
  IUserDetail,
} from '../../../interfaces';
import { useTranslation } from 'react-i18next';
import { screens } from '../../../app.constant';
import { NewLoginView } from './new-login.view';

interface IProps {
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  setToken: (result: ITokenResult | null) => void;
  setLoginUserDetail: (userDetail: IUserDetail) => void;
  hideLoading: () => void;
}

const NewLoginContainer = (props: IProps) => {
  const loginParam = getParams<ILoginParam>();
  let navigateTo: ILoginParam = {
    screenToNavigate: screens.Dashboard,
    params: null,
  };

  if (loginParam) {
    navigateTo = loginParam;
  }

  const { t } = useTranslation(['Login']);

  const login = async (email: string, password: string) => {
    props.showLoading();
    loginApi
      .login(email, password)
      .then(tokenResult => {
        if (!tokenResult || !isNullOrEmpty(tokenResult)) {
          let expireOn = new Date(
            Date.now() + (tokenResult.expires_in as number) * 1000,
          );

          tokenResult = { ...tokenResult, expire_on: expireOn };
          userApi
            .getUserDetail(email)
            .then(loginUserDetail => {
              if (
                (loginUserDetail?.Id ?? 0) > 0 &&
                (loginUserDetail?.UserProfile?.Id ?? 0) > 0
              ) {
                props.setToken(tokenResult);
                props.setLoginUserDetail({
                  ...loginUserDetail,
                  LoginName: email,
                  Password: password,
                });
                navigate(navigateTo.screenToNavigate, navigateTo.params);
              } else {
                showErrorMessage(t('Messages.Invalid_Username_Password'));
              }
            })
            .catch(() => {
              showErrorMessage(t('Messages.Invalid_Username_Password'));
            })
            .finally(() => {
              props.hideLoading();
            });
        } else {
          showErrorMessage(t('Messages.Invalid_Username_Password'));
        }
      })
      .catch(() => {
        props.hideLoading();
        showErrorMessage(t('Messages.Invalid_Username_Password'));
      });
  };

  const registerNow = () => {
    navigate(screens.CreateAccount);
  };

  const forgotPassword = () => {
    navigate(screens.NewForgotPassword);
  }

  return (
    <NewLoginView
      login={login}
      registerNow={registerNow}
      forgotPassword={forgotPassword}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const mapDispatchToProps = {
  setToken: setTokenAction,
  setLoginUserDetail: setLoginUserDetailAction,
  showLoading,
  hideLoading,
};

const connectedNewLoginContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(NewLoginContainer);

export { connectedNewLoginContainer as NewLoginContainer };


