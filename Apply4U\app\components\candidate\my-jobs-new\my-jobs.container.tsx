import React from 'react';
import { MyJobsView } from './my-jobs.view';
import { connect, useSelector } from 'react-redux';
import { IApplicationState } from '../../../redux';
import {
  addRecentSearchAction,
  addToFavJobsAction,
  hideJobAction,
  hideLoading,
  removeFromFavJobsAction,
  showLoading,
} from '../../../redux/actions';
import {
  IAttbJobAlert,
  IAutoSearch,
  IBrowserParam,
  IJobDetailParam,
  IJobGateJobAlert,
  IJobSearchResponse,
  IJobSearchResult,
  ILoadingIndicator,
  ISearchParam,
  IUserDetail,
} from '../../../interfaces';
import { IAction } from '../../../redux/actions/action.interface';
import { jobAlertApi, searchApi, userApi } from '../../../http';
import {
  createSavedSearchWebUrl,
  defaultPageNumberJobSearch,
  defaultPageSizeSearchResults,
  editSavedSearchWebUrl,
  screens,
} from '../../../app.constant';
import { applyNow, easyApplyWithDefaultResume, encodeBase64String, navigate } from '../../../utility';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import { Linking } from 'react-native';

interface IProps {
  loginUser: IUserDetail | null;
  hiddenJobs: number[];
  favJobs: number[];
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => IAction<ILoadingIndicator>;
  hideLoading: () => IAction<ILoadingIndicator>;
  hideJob:(payload:number) => IAction<number>;
  addToFav: (payload: number) => IAction<number>;
  removeFromFav: (payload: number) => IAction<number>;
  addRecentSearch:(payload:string) => IAction<string>;
}
const MyJobsContainer = (props: IProps) => {
  const [currentPageNumber, setCurrentPageNumber] = React.useState<number>(
    defaultPageNumberJobSearch,
  );

  const [currentPageSize, setCurrentPageSize] = React.useState<number>(
    defaultPageSizeSearchResults,
  );

  const [isKeywordsTextCropped,setIsKeywordsTextCropped] = React.useState<boolean>(false);

  const [isTopJobsActive,setIsTopJobsActive] = React.useState<boolean>(true);

  const [searchResults, setSearchResults] = React.useState<IJobSearchResponse[]>([]);
  const [loadingIsInProcess, setLoadingIsInProcess] =
    React.useState<boolean>(false);
  const [isMoreResultsExists, setIsMoreResultsExists] =
    React.useState<boolean>(true);
  const [totalResults,setTotalResults] = React.useState<number>(0);

  const [searchParam,setSearchParam] = React.useState<ISearchParam | null>(null);
  const [selectedAutoSearch,setSelectedAutoSearch] = React.useState<IAutoSearch>();
  const [defaultAutoSearch,setDefaultAutoSearch] = React.useState<IAutoSearch>();
  const [autoSearches,setAutoSearches] = React.useState<IAutoSearch[]>([]);
  const [showFullText,setShowFullText] = React.useState<boolean>(false);
  const loadResults = (
    pageNumber: number,
    pageSize: number,
    addResults: boolean,
    showLoadingIndicator: boolean,
  ) => {
    if (!loadingIsInProcess) {
      setLoadingIsInProcess(true);
      if (showLoadingIndicator) {
        props.showLoading();
      }

      searchApi
        .getJobResults(
          searchParam,
          props.hiddenJobs,
          props.loginUser?.Id,
          pageNumber,
          pageSize,
        )
        .then((results: IJobSearchResult) => {
          if (!!results && !!results.Response && results.Response.length > 0) {
            if (!!addResults) {
              markFavAndSetResults([...searchResults, ...results.Response]);
            } else {
              markFavAndSetResults([...results.Response]);
            }

            setCurrentPageNumber(pageNumber);
            setIsMoreResultsExists(((currentPageNumber * currentPageSize) < results.TotalRecords));
            setTotalResults(results.TotalRecords);
          } else {
            setIsMoreResultsExists(false);
          }
        })
        .catch((err: any) => {
          props.hideLoading();
        })
        .finally(() => {
          props.hideLoading();
          setLoadingIsInProcess(false);
        });
    }
  };

  const favJobsAddOrRemove = (jobId: number, isFav: boolean) => {
    if (isFav === true) {
      props.removeFromFav(jobId);
    } else {
      props.addToFav(jobId);
    }
  };

  const handleGoToJobDetail = (jobId: number) => {
    navigate<IJobDetailParam>(screens.JobDetail, { jobId });
  };

  const handleHideJob = (jobId: number) => {
    if (jobId > 0) {
      props.hideJob(jobId);
    }
  };
  const toggleFullTextVisibility = () => {
    setShowFullText(state => !state);
  }

  const handleLoadMore = () => {
    const pageNumber = currentPageNumber + 1;
    loadResults(pageNumber, currentPageSize, true, true);
  };

  const handleEasyApply = (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => {
    if (jobId > 0 && jobSourceId > 0 && !!jobTitle) {
      applyNow(jobId, jobSourceId, jobTitle, applicationUrl, isEasyApply ? internalEasyApply : undefined);
    } else {
      applyNow(undefined, 0, '', applicationUrl, isEasyApply ? internalEasyApply : undefined);
    }
  };

  const internalEasyApply = (jobId:number) => {
      props.showLoading();
    easyApplyWithDefaultResume(props?.loginUser?.Id ?? 0,jobId,(errorMessage:string) =>{
        props.hideLoading();
        showErrorMessage(errorMessage);
    },(successMessage:string) => {
        setSearchResults(oldResults => {
            return oldResults.map(m => {
                if(m.Id == jobId){
                    m.IsApplied = true;
                }

                return m;
            });
        });
        props.hideLoading();
        showSuccessMessage(successMessage);
    });
  }

  const markFavAndSetResults = (resultsToMark:IJobSearchResponse[]) => {
    if(!!resultsToMark && resultsToMark.length > 0){
      if (!!props.favJobs && props.favJobs.length > 0){
        const resultsWithFavMark = resultsToMark.map(m => {
          m.IsFav = props.favJobs.indexOf(m.Id) >= 0;
          return m;
        });
  
        setSearchResults([...resultsWithFavMark]);
      }else{
        const resultsWithNoFavMark = resultsToMark.map(m => {
          m.IsFav = false;
          return m;
        });
  
        setSearchResults([...resultsWithNoFavMark]);
      }

    }else{
      setSearchResults([...resultsToMark]);
    }
  }

  const getDefaultAutoSearch = (userId:number) => {
    if(!defaultAutoSearch){
        userApi.getDefaultAutoSearch(userId)
        .then((autoSearch:IAutoSearch) => {
            if(!!autoSearch){
                setDefaultAutoSearch({...autoSearch});
                setSelectedAutoSearch({...autoSearch});
            }else{
              if(isTopJobsActive){
                setSearchParam({keywords:'',locationText:''});
              }
            }
        })
        .catch((e) => {
          if(isTopJobsActive){
            setSearchParam({keywords:'',locationText:''});
          }
        })
        .finally(() => {
        });
    }
  }

  const getAutoSearches = (userId:number) => {
    if(!autoSearches || autoSearches.length <= 0){
        userApi.getAutoSearches(userId)
        .then((autoSearches:IAutoSearch[]) => {
            if(!!autoSearches){
                setAutoSearches([...autoSearches]);
            }
        })
        .catch(() => {})
        .finally(() => {
        });
    }
  }

  React.useEffect(() => {
    getDefaultAutoSearch(props.loginUser?.Id ?? 0);
    getAutoSearches(props.loginUser?.Id ?? 0);
  },[props.loginUser]);

  React.useEffect(() => {
    if(!!selectedAutoSearch && !!selectedAutoSearch.Id && selectedAutoSearch.Id > 0){
        const params = {
            ...searchParam
        };
        params.applyFilters = selectedAutoSearch.IsFilterApplyed;
        params.keywords = selectedAutoSearch.KeyWords;
        params.locationId = selectedAutoSearch.LocationId;
        params.locationText = selectedAutoSearch.LocationText;
        params.radius = selectedAutoSearch.Radius;
        params.salaryFrom = selectedAutoSearch.SalaryFrom;
        params.salaryPer = selectedAutoSearch.SalaryDuration;
        params.salaryTo = selectedAutoSearch.SalaryTo;
        params.searchWithinDays = selectedAutoSearch.SearchWithInLast;
        params.sector = {...selectedAutoSearch.Sector};

        setSearchParam({...params});
    }
  },[selectedAutoSearch]);

  const handleAutoSearchChange = (aSearch:IAutoSearch) => {
      setSelectedAutoSearch({...aSearch});
  }

  React.useEffect(() => {
      setSearchResults([]);
      setTotalResults(0);

      if(!!searchParam){
        loadResults(
            defaultPageNumberJobSearch,
            defaultPageSizeSearchResults,
            false,
            true,
          );
      }
  }, [searchParam]);

  React.useEffect(() => {
    markFavAndSetResults(searchResults);
  }, [props.favJobs]);

  React.useEffect(() => {
    if(!!props.hiddenJobs && props.hiddenJobs.length > 0 && !!searchResults && searchResults.length > 0){
      const filteredSearchResults = searchResults.filter(m => props.hiddenJobs.indexOf(m.Id) < 0);
        setSearchResults([...filteredSearchResults]);
    }
  },[props.hiddenJobs]);

  const setTopJobsActive = () => {
      setSearchResults([])
      setIsTopJobsActive(true);
      setSelectedAutoSearch({...defaultAutoSearch});
  }

  const setAutoSearchActive = (autoSearch:IAutoSearch|undefined) => {
      setSearchResults([]);
      setIsTopJobsActive(false);
      setSelectedAutoSearch({...autoSearch});
  }

  const editMySavedSearch = () => {
    const encodedLoginName = encodeBase64String(
      props.loginUser?.LoginName ?? '',
    );
    const encodedLoginPassword = encodeBase64String(
      props.loginUser?.Password ?? '',
    );

    const url = editSavedSearchWebUrl(encodedLoginName,encodedLoginPassword,selectedAutoSearch?.Id ?? 0);
    //Linking.openURL(url).catch(err => {});
    navigate<IBrowserParam>(screens.Browser,{URL:url});
  }

  const createMySavedSearch = () => {
    const encodedLoginName = encodeBase64String(
      props.loginUser?.LoginName ?? '',
    );
    const encodedLoginPassword = encodeBase64String(
      props.loginUser?.Password ?? '',
    );

    const url = createSavedSearchWebUrl(encodedLoginName,encodedLoginPassword);
    //Linking.openURL(url).catch(err => {});
    navigate<IBrowserParam>(screens.Browser,{URL:url});
  }

  return (
    <MyJobsView
      searchResults={searchResults}
      searchParam={searchParam}
      loadMore={handleLoadMore}
      onHideJob={handleHideJob}
      favJobsAddOrRemove={favJobsAddOrRemove}
      goToJobDetail={handleGoToJobDetail}
      handleEasyApply={handleEasyApply}
      isMoreResultsExists={isMoreResultsExists}
      totalResults={totalResults}
      isLoading={loadingIsInProcess}
      isTopJobsActive={isTopJobsActive}
      setTopJobsActive={setTopJobsActive}
      setAutoSearchActive={setAutoSearchActive}
      autoSearches={autoSearches}
      handleAutoSearchChange={handleAutoSearchChange}
      selectedAutoSearch={selectedAutoSearch}
      showFullText={showFullText}
      toggleTextVisibility={toggleFullTextVisibility}
      isKeywordsTextCropped={isKeywordsTextCropped}
      setIsKeywordsTextCropped={setIsKeywordsTextCropped}
      editMySavedSearch={editMySavedSearch}
      createMySavedSearch={createMySavedSearch}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser: state.loginUser,
    hiddenJobs: state.hiddenJobs,
    favJobs: state.favJobs
  };
};

const mapDispatchToProps = {
  showLoading: showLoading,
  hideLoading: hideLoading,
  hideJob:hideJobAction,
  addToFav: addToFavJobsAction,
  removeFromFav: removeFromFavJobsAction,
  addRecentSearch:addRecentSearchAction,
};

const connectedMyJobsContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(MyJobsContainer);
export { connectedMyJobsContainer as MyJobsContainer };
