import React from 'react';
import {StyleSheet} from 'react-native';
import Toast, {BaseToast} from 'react-native-toast-message'; //https://www.npmjs.com/package/react-native-toast-message
import {cancelIcon, infoIcon, successIcon, warningIcon} from '../../assets';
import {ITheme, useCurrentTheme} from '../../theme';

const ToasterComponent = () => {
  const styles = useCurrentTheme(createStyles);
  const onClosePress = () => {
    Toast.hide();
  };

  const toastConfig = {
    success: ({text1, text2, ...rest}: any) => (
      <BaseToast
        {...rest}
        style={styles.successBorder}
        contentContainerStyle={styles.successConainter}
        text1Style={styles.titleText}
        text2Style={styles.messageText}
        text1={text1}
        text2={text2}
        trailingIconContainerStyle={styles.successIconContainerStyle}
        leadingIconContainerStyle={styles.successIconContainerStyle}
        onTrailingIconPress={() => {
          onClosePress();
        }}
        text1NumberOfLines={5}
        trailingIcon={cancelIcon}
        trailingIconStyle={styles.trailingIconStyle}
        leadingIconStyle={styles.leadingIconStyle}
        leadingIcon={successIcon}
      />
    ),
    info: ({text1, text2, ...rest}: any) => (
      <BaseToast
        {...rest}
        style={styles.infoBorder}
        contentContainerStyle={styles.infoConainter}
        text1Style={styles.titleText}
        text2Style={styles.messageText}
        text1={text1}
        text2={text2}
        trailingIconContainerStyle={styles.infoIconContainerStyle}
        leadingIconContainerStyle={styles.infoIconContainerStyle}
        onTrailingIconPress={() => {
          onClosePress();
        }}
        text1NumberOfLines={5}
        trailingIcon={cancelIcon}
        trailingIconStyle={styles.trailingIconStyle}
        leadingIconStyle={styles.leadingIconStyle}
        leadingIcon={infoIcon}
      />
    ),
    error: ({text1, text2, ...rest}: any) => (
      <BaseToast
        {...rest}
        style={styles.errorBorder}
        contentContainerStyle={styles.errorConainter}
        text1Style={styles.titleText}
        text2Style={styles.messageText}
        text1={text1}
        text2={text2}
        trailingIconContainerStyle={styles.errorIconContainerStyle}
        leadingIconContainerStyle={styles.errorIconContainerStyle}
        onTrailingIconPress={() => {
          onClosePress();
        }}
        text1NumberOfLines={5}
        trailingIcon={cancelIcon}
        trailingIconStyle={styles.trailingIconStyle}
        leadingIconStyle={styles.leadingIconStyle}
        leadingIcon={warningIcon}
      />
    ),
  };

  return <Toast config={toastConfig} />;
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    titleText: {
      ...theme.typography.bold.small,
    },
    messageText: {
      ...theme.typography.normal.small,
      color: theme.palette.black,
    },
    trailingIconStyle: {
      width: 14,
      height: 14,
    },
    leadingIconStyle: {
      width: 18,
      height: 18,
    },
    successIconContainerStyle: {
      backgroundColor: theme.palette.success,
    },
    infoIconContainerStyle: {
      backgroundColor: theme.palette.info,
    },
    errorIconContainerStyle: {
      backgroundColor: theme.palette.error,
    },
    successConainter: {
      backgroundColor: theme.palette.success,
    },
    successBorder: {
      borderLeftColor: theme.palette.success,
    },

    infoConainter: {
      backgroundColor: theme.palette.info,
    },
    infoBorder: {
      borderLeftColor: theme.palette.info,
    },
    errorConainter: {
      backgroundColor: theme.palette.error,
    },
    errorBorder: {
      borderLeftColor: theme.palette.error,
    },
  });

  return styles;
};
export {ToasterComponent};
