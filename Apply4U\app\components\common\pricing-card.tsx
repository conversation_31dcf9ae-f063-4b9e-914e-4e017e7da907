import React from 'react';
import {StyleSheet, View, KeyboardAvoidingView, Keyboard} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faShoppingCart,
  faCheck,
  faTimes,
} from '@fortawesome/free-solid-svg-icons';
import {Text, Button} from '../common';
import {useTranslation} from 'react-i18next';

interface IProps {
  free: string;
  price: string;
  popular: string;
  total: string;
  feature1: string;
  feature2: string;
  feature3: string;
  feature4: string;
  feature5: string;
  feature6: string;
  feature7: string;
  feature8: string;
  feature9: string;
  feature10: string;
  feature11: string;
  feature12: string;
  feature13: string;
  feature14: string;
  feature15: string;
}

export const PriceCard = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const {t, i18n} = useTranslation(['PricingView']);
  return (
    <View style={styles.pricingContainer}>
      <KeyboardAvoidingView enabled>
        <View>
          <Text styles={styles.mostPopular} text={props.popular} />
          <Text styles={styles.mainHeader} text={props.free} />
          <Text styles={styles.price} text={props.price} />
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature1} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature2} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faTimes} style={styles.listIcon2} />
            <Text styles={styles.listItemText} text={props.feature3} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature4} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature5} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faTimes} style={styles.listIcon2} />
            <Text styles={styles.listItemText} text={props.feature6} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faTimes} style={styles.listIcon2} />
            <Text styles={styles.listItemText} text={props.feature7} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faTimes} style={styles.listIcon2} />
            <Text styles={styles.listItemText} text={props.feature8} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature9} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faTimes} style={styles.listIcon2} />
            <Text styles={styles.listItemText} text={props.feature10} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature11} />
          </View>
          <View style={styles.listItemWrapper}>
            <FontAwesomeIcon icon={faCheck} style={styles.listIcon} />
            <Text styles={styles.listItemText} text={props.feature12} />
          </View>
        </View>

        <Text styles={styles.price} text={props.total} />

        <Button
          styles={styles.btnRadius}
          pressed={() => {
            Keyboard.dismiss();
          }}>
          <View style={styles.buttonIcon}>
            <FontAwesomeIcon icon={faShoppingCart} style={styles.faColor} />
            <Text text={t('Buttons.Select')} />
          </View>
        </Button>
      </KeyboardAvoidingView>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    pricingContainer: {
      margin: 10,
      padding: 10,
      borderRadius: 6,
      flexDirection: 'column',
      backgroundColor: '#fff',
    },
    mostPopular: {
      //width: '100%',
      padding: 10,
      fontSize: 18,
      color: '#4f40d1',
      marginTop: -10,
      marginBottom: 15,
      fontWeight: 'bold',
      alignSelf: 'center',
      alignItems: 'center',
      textAlign: 'center',
      justifyContent: 'center',
      backgroundColor: '#edf1fc',
      borderBottomLeftRadius: 6,
      borderBottomRightRadius: 6,
      //display: 'none',
    },
    mainHeader: {
      width: '100%',
      fontSize: 24,
      color: '#fff',
      borderRadius: 6,
      alignSelf: 'center',
      fontWeight: 'bold',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      backgroundColor: '#43b7b8',
    },
    price: {
      width: '100%',
      fontSize: 18,
      color: '#333',
      marginTop: 10,
      alignSelf: 'center',
      fontWeight: 'bold',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
    },
    listItemWrapper: {
      height: 30,
      borderBottomColor: '#d4dffe',
      borderBottomWidth: 2,
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      alignSelf: 'center',
    },
    listItemText: {
      fontSize: 13,
      color: '#333',
    },
    listIcon: {
      fontSize: 20,
      color: '#46a4d0',
      marginRight: 5,
    },
    listIcon2: {
      fontSize: 20,
      color: '#bb1c1c',
      marginRight: 5,
    },
    faColor: {
      fontSize: 20,
      color: '#fff',
      marginRight: 5,
    },
    btnRadius: {
      borderRadius: 6,
    },
    buttonIcon: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
    },
  });
  return styles;
};
