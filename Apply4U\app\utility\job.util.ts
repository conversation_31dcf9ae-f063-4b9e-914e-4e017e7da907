
export const getFormattedJobDetailUrlText = (jobTitle: string, location: string): string => {
    let formattedString = "";
    if (!!jobTitle && jobTitle.trim() != '') {
      formattedString = jobTitle;

      if (!!location && location.trim() != "") {
        formattedString = formattedString + "-jobs"
        formattedString = formattedString + "-in-" + location;
      } else {
        formattedString = formattedString.trim() + "-jobs"
      }

      formattedString = getSeoFormattedUrlText(formattedString);
      formattedString = formattedString.toLowerCase();
    } else {
      formattedString = "job"
    }

    formattedString = formattedString.trim();

    return formattedString;

  }

  export const getSeoFormattedUrlText = (urlText: string):string => {
    urlText = urlText.replace(/&nbsp;/g, ' ');
    urlText = urlText.replace(/&amp;/g, ' ');
    urlText = urlText.replace(/&ndash;/g, ' ');
    urlText = urlText.replace(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/g, " ");
    urlText = urlText.replace(/\s+/g, ' ');
    urlText = urlText.replace(/\s/g, "-");

    return urlText;
  }