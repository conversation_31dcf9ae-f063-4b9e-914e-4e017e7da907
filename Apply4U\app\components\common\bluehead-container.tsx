import React from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { Text, TouchableRipple } from 'react-native-paper';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faChevronLeft, faHome } from '@fortawesome/free-solid-svg-icons';
import { ITheme, useCurrentTheme } from '../../theme';
import { canGoBack, goBack, navigate } from '../../utility';
import { screens } from '../../app.constant';

interface BlueheadContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  onBackPress?: () => void;
  onHomePress?: () => void;
}

export const BlueheadContainer = ({
  children,
  title,
  subtitle,
  showBackButton = true,
  showHomeButton = true,
  onBackPress,
  onHomePress,
}: BlueheadContainerProps) => {
  const styles = useCurrentTheme(createStyles);

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else if (canGoBack()) {
      goBack();
    }
  };

  const handleHomePress = () => {
    if (onHomePress) {
      onHomePress();
    } else {
      navigate(screens.Dashboard);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.headerContainer}>
          <View style={styles.headerNavigation}>
            {showBackButton && canGoBack() && (
              <TouchableRipple
                onPress={handleBackPress}
                style={styles.headerButton}
                rippleColor="rgba(255, 255, 255, 0.2)"
                borderless
              >
                <FontAwesomeIcon icon={faChevronLeft} size={24} color="#FFFFFF" />
              </TouchableRipple>
            )}
            <View style={styles.headerSpacer} />
            {showHomeButton && (
              <TouchableRipple
                onPress={handleHomePress}
                style={styles.headerButton}
                rippleColor="rgba(255, 255, 255, 0.2)"
                borderless
              >
                <FontAwesomeIcon icon={faHome} size={24} color="#FFFFFF" />
              </TouchableRipple>
            )}
          </View>

          {title && (
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>{title}</Text>
              {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>}
            </View>
          )}
        </View>

        <View style={styles.backContentContainer}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.contentContainer}
          >
            {children}
          </KeyboardAvoidingView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme: ITheme) => {
  const primaryColor = theme.palette.primary || '#0E1C5D';

  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    scrollContent: {
      flexGrow: 1,
    },
    headerContainer: {
      backgroundColor: primaryColor,
      padding: 20,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
    headerNavigation: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerButton: {
      padding: 8,
      borderRadius: 20,
      overflow: 'hidden',
    },
    headerSpacer: {
      flex: 1,
    },
    headerTextContainer: {
      alignItems: 'center',
    },
    headerTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: '#FFFFFF',
      textAlign: 'center',
      marginBottom: 8,
      paddingHorizontal: 20,
    },
    headerSubtitle: {
      fontSize: 16,
      color: '#FFFFFF',
      textAlign: 'center',
      opacity: 0.9,
    },
    contentContainer: {
      flex: 1,
      padding: 5,
      paddingTop: 40,
      backgroundColor: '#FFFFFF',
      borderTopLeftRadius: 30,
      borderTopRightRadius: 30,
    },
    backContentContainer: {
      flex: 1,
      backgroundColor: primaryColor,
    },
  });

  return styles;
};
