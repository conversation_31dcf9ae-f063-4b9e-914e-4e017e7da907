# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 84ms
    create-ARM64_V8A-model 15ms
    create-X86-model 19ms
    create-X86_64-model 14ms
    create-module-model 14ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 16ms
    create-X86-model 29ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 319ms
  [gap of 125ms]
create_cxx_tasks completed in 454ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 32ms
    create-variant-model 19ms
    create-ARMEABI_V7A-model 102ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 15ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 17ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 293ms
  [gap of 81ms]
create_cxx_tasks completed in 384ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 70ms
    [gap of 10ms]
    create-variant-model 29ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 22ms
    create-X86-model 21ms
    create-X86_64-model 22ms
    create-module-model 16ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 16ms
    create-X86-model 17ms
    create-X86_64-model 30ms
  create-initial-cxx-model completed in 386ms
  [gap of 192ms]
create_cxx_tasks completed in 594ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 33ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 12ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 23ms
    create-ARM64_V8A-model 16ms
    create-X86-model 13ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 351ms
  [gap of 145ms]
create_cxx_tasks completed in 511ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    [gap of 11ms]
    create-variant-model 34ms
    create-ARMEABI_V7A-model 69ms
    create-ARM64_V8A-model 15ms
    create-X86-model 14ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 289ms
  [gap of 69ms]
create_cxx_tasks completed in 370ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 50ms
    create-variant-model 57ms
    create-ARMEABI_V7A-model 71ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 14ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 16ms
    create-X86-model 13ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 324ms
  [gap of 108ms]
create_cxx_tasks completed in 444ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 69ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 18ms
    create-module-model 20ms
    create-variant-model 21ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 303ms
  [gap of 79ms]
create_cxx_tasks completed in 395ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 45ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 59ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 14ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 12ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 257ms
  [gap of 102ms]
create_cxx_tasks completed in 369ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 72ms
    create-variant-model 53ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 16ms
    create-X86-model 15ms
    create-X86_64-model 17ms
    create-module-model 10ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 25ms
    create-X86-model 15ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 354ms
  [gap of 144ms]
create_cxx_tasks completed in 514ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 28ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 53ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 18ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 241ms
  [gap of 87ms]
create_cxx_tasks completed in 338ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 41ms
    create-variant-model 57ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 18ms
    create-X86-model 14ms
    create-X86_64-model 12ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 15ms
    create-X86-model 13ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 310ms
  [gap of 96ms]
create_cxx_tasks completed in 417ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 72ms
    create-variant-model 56ms
    create-ARMEABI_V7A-model 85ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 15ms
    create-module-model 18ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 373ms
  [gap of 133ms]
create_cxx_tasks completed in 520ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 81ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 265ms
  [gap of 96ms]
create_cxx_tasks completed in 372ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 38ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 66ms
    create-ARM64_V8A-model 15ms
    create-X86-model 12ms
    create-X86_64-model 18ms
    [gap of 11ms]
    create-variant-model 30ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 18ms
    create-X86-model 13ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 301ms
  [gap of 75ms]
create_cxx_tasks completed in 388ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 53ms
    create-variant-model 52ms
    create-ARMEABI_V7A-model 74ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 26ms
    create-module-model 11ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 17ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 325ms
  [gap of 86ms]
create_cxx_tasks completed in 424ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 52ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 69ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 258ms
  [gap of 94ms]
create_cxx_tasks completed in 363ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 38ms
    create-variant-model 44ms
    create-ARMEABI_V7A-model 50ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 240ms
  [gap of 90ms]
create_cxx_tasks completed in 339ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 161ms
    [gap of 23ms]
    create-variant-model 57ms
    create-ARMEABI_V7A-model 144ms
    create-ARM64_V8A-model 22ms
    create-X86-model 71ms
    create-X86_64-model 45ms
    create-module-model 14ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 32ms
    create-ARM64_V8A-model 17ms
    create-X86-model 25ms
    create-X86_64-model 20ms
  create-initial-cxx-model completed in 660ms
  [gap of 216ms]
create_cxx_tasks completed in 896ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 108ms
    [gap of 14ms]
    create-variant-model 45ms
    create-ARMEABI_V7A-model 68ms
    create-ARM64_V8A-model 21ms
    create-X86-model 15ms
    create-X86_64-model 14ms
    create-module-model 10ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 34ms
    create-ARM64_V8A-model 17ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 416ms
  [gap of 134ms]
create_cxx_tasks completed in 574ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 68ms
    [gap of 10ms]
    create-variant-model 35ms
    create-ARMEABI_V7A-model 95ms
    create-ARM64_V8A-model 18ms
    create-X86-model 18ms
    create-X86_64-model 30ms
    create-module-model 13ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 18ms
    create-X86-model 16ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 365ms
  [gap of 99ms]
create_cxx_tasks completed in 482ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 29ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 45ms
    create-ARM64_V8A-model 10ms
    [gap of 63ms]
  create-initial-cxx-model completed in 174ms
  [gap of 46ms]
create_cxx_tasks completed in 228ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 56ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 69ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 15ms
    create-module-model 13ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 26ms
    create-ARM64_V8A-model 20ms
    create-X86-model 17ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 309ms
  [gap of 137ms]
create_cxx_tasks completed in 457ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 63ms
    [gap of 11ms]
    create-variant-model 49ms
    create-ARMEABI_V7A-model 110ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 20ms
    create-module-model 21ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 14ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 406ms
  [gap of 110ms]
create_cxx_tasks completed in 530ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 80ms
    create-ARM64_V8A-model 17ms
    create-X86-model 15ms
    create-X86_64-model 34ms
    create-module-model 16ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 358ms
  [gap of 154ms]
create_cxx_tasks completed in 527ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    [gap of 17ms]
    create-variant-model 14ms
    create-ARMEABI_V7A-model 11ms
    [gap of 20ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 130ms
  [gap of 17ms]
create_cxx_tasks completed in 147ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    [gap of 12ms]
    create-variant-model 37ms
    create-ARMEABI_V7A-model 113ms
    create-ARM64_V8A-model 22ms
    create-X86-model 49ms
    create-X86_64-model 44ms
    create-module-model 17ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 443ms
  [gap of 106ms]
create_cxx_tasks completed in 566ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    create-variant-model 58ms
    create-ARMEABI_V7A-model 62ms
    create-ARM64_V8A-model 21ms
    create-X86-model 12ms
    create-X86_64-model 23ms
    create-module-model 12ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 319ms
  [gap of 84ms]
create_cxx_tasks completed in 414ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 55ms
    create-variant-model 43ms
    create-ARMEABI_V7A-model 59ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 275ms
  [gap of 111ms]
create_cxx_tasks completed in 403ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 77ms
    [gap of 47ms]
    create-variant-model 43ms
    create-ARMEABI_V7A-model 99ms
    create-ARM64_V8A-model 16ms
    create-X86-model 23ms
    create-X86_64-model 18ms
    create-module-model 14ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 31ms
    create-X86-model 22ms
    create-X86_64-model 24ms
  create-initial-cxx-model completed in 461ms
  [gap of 189ms]
create_cxx_tasks completed in 672ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    [gap of 12ms]
    create-variant-model 25ms
    create-ARMEABI_V7A-model 64ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 12ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 266ms
  [gap of 74ms]
create_cxx_tasks completed in 353ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 47ms
    create-variant-model 41ms
    create-ARMEABI_V7A-model 53ms
    create-X86-model 10ms
    [gap of 25ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    [gap of 16ms]
  create-initial-cxx-model completed in 232ms
  [gap of 94ms]
create_cxx_tasks completed in 335ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 40ms
    create-variant-model 41ms
    create-ARMEABI_V7A-model 51ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    [gap of 15ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 239ms
  [gap of 102ms]
create_cxx_tasks completed in 351ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 61ms
    [gap of 11ms]
    create-variant-model 28ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 17ms
    create-X86-model 15ms
    create-X86_64-model 17ms
    create-module-model 16ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 23ms
    create-X86-model 15ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 342ms
  [gap of 298ms]
create_cxx_tasks completed in 656ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 64ms
    create-ARMEABI_V7A-model 95ms
    create-ARM64_V8A-model 24ms
    create-X86-model 27ms
    create-X86_64-model 24ms
    create-module-model 13ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 17ms
    create-X86-model 13ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 395ms
  [gap of 119ms]
create_cxx_tasks completed in 525ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 46ms
    [gap of 15ms]
    create-variant-model 64ms
    create-ARMEABI_V7A-model 66ms
    create-ARM64_V8A-model 18ms
    create-X86-model 14ms
    create-X86_64-model 19ms
    create-module-model 10ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 329ms
  [gap of 117ms]
create_cxx_tasks completed in 460ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 54ms
    create-variant-model 66ms
    create-ARMEABI_V7A-model 88ms
    create-ARM64_V8A-model 19ms
    create-X86-model 17ms
    create-X86_64-model 25ms
    create-module-model 19ms
    create-variant-model 18ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 390ms
  [gap of 116ms]
create_cxx_tasks completed in 520ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 48ms
    [gap of 13ms]
    create-variant-model 70ms
    create-ARMEABI_V7A-model 78ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 22ms
    create-module-model 18ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 19ms
    create-ARM64_V8A-model 16ms
    create-X86-model 21ms
    create-X86_64-model 27ms
  create-initial-cxx-model completed in 397ms
  [gap of 144ms]
create_cxx_tasks completed in 563ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 57ms
    create-variant-model 31ms
    create-ARMEABI_V7A-model 75ms
    create-ARM64_V8A-model 22ms
    create-X86-model 17ms
    create-X86_64-model 18ms
    create-module-model 13ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 327ms
  [gap of 79ms]
create_cxx_tasks completed in 418ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 39ms
    create-variant-model 49ms
    create-ARMEABI_V7A-model 47ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 13ms
    [gap of 17ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 42ms
    create-X86-model 13ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 285ms
  [gap of 84ms]
create_cxx_tasks completed in 380ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 35ms
    create-variant-model 20ms
    create-ARMEABI_V7A-model 48ms
    [gap of 17ms]
    create-X86_64-model 10ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 10ms
    [gap of 24ms]
  create-initial-cxx-model completed in 198ms
  [gap of 74ms]
create_cxx_tasks completed in 281ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 69ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 65ms
    create-ARM64_V8A-model 15ms
    create-X86-model 16ms
    create-X86_64-model 22ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 15ms
    create-X86-model 15ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 314ms
  [gap of 144ms]
create_cxx_tasks completed in 477ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 64ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 68ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 17ms
    [gap of 52ms]
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 312ms
  [gap of 99ms]
create_cxx_tasks completed in 431ms

