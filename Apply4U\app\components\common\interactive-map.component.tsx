import React, { useRef, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Animated,
  PanResponder,
  TouchableOpacity,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import Slider from '@react-native-community/slider';
import { ITheme, useCurrentTheme } from '../../theme';
import { Text } from './text.component';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface IProps {
  initialRadius?: number;
  minRadius?: number;
  maxRadius?: number;
  onRadiusChange?: (radius: number) => void;
  centerLabel?: string;
  radiusUnit?: 'miles' | 'km';
  mapSize?: {
    width: number;
    height: number;
  };
  showGrid?: boolean;
  showSlider?: boolean;
}

export const InteractiveMap = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  
  // Default values
  const initialRadius = props.initialRadius || 20;
  const minRadius = props.minRadius || 1;
  const maxRadius = props.maxRadius || 50;
  const radiusUnit = props.radiusUnit || 'miles';
  const mapSize = props.mapSize || { width: screenWidth * 1.5, height: screenHeight * 0.8 };
  const showGrid = props.showGrid !== false; // default true
  const showSlider = props.showSlider !== false; // default true
  
  // State
  const [radius, setRadius] = useState<number>(initialRadius);
  const [mapCenter, setMapCenter] = useState({
    x: mapSize.width / 2,
    y: mapSize.height / 2,
  });
  
  // Animation values
  const radiusAnimation = useRef(new Animated.Value(initialRadius)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  
  // Convert radius value to pixels for visual representation
  const radiusToPixels = useCallback((radiusValue: number) => {
    // Scale factor: 1 mile/km = 4 pixels (adjustable)
    return radiusValue * 4;
  }, []);
  
  // Handle radius change from slider
  const handleSliderChange = useCallback((value: number) => {
    setRadius(value);
    Animated.timing(radiusAnimation, {
      toValue: value,
      duration: 200,
      useNativeDriver: false,
    }).start();
    
    if (props.onRadiusChange) {
      props.onRadiusChange(value);
    }
  }, [props.onRadiusChange, radiusAnimation]);
  
  // Pan responder for dragging the radius circle handle
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: (evt) => {
        // Start drag - capture initial position
      },
      onPanResponderMove: (evt, gestureState) => {
        // Calculate distance from center to current touch position
        const touchX = evt.nativeEvent.pageX;
        const touchY = evt.nativeEvent.pageY;

        // Get the center position relative to the screen
        // This is a simplified calculation - in a real implementation you'd want to
        // get the actual screen coordinates of the map center
        const centerScreenX = screenWidth / 2;
        const centerScreenY = screenHeight / 2;

        const deltaX = touchX - centerScreenX;
        const deltaY = touchY - centerScreenY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

        // Convert pixel distance to radius value
        const newRadius = Math.max(minRadius, Math.min(maxRadius, distance / 4));

        if (Math.abs(newRadius - radius) > 0.5) { // Threshold to prevent jittery updates
          setRadius(newRadius);
          radiusAnimation.setValue(newRadius);

          if (props.onRadiusChange) {
            props.onRadiusChange(newRadius);
          }
        }
      },
      onPanResponderRelease: () => {
        // End drag - could add haptic feedback here
      },
    })
  ).current;
  
  // Render grid lines
  const renderGrid = () => {
    if (!showGrid) return null;
    
    const gridLines = [];
    const gridSpacing = 40;
    
    // Vertical lines
    for (let x = 0; x < mapSize.width; x += gridSpacing) {
      gridLines.push(
        <View
          key={`v-${x}`}
          style={[
            styles.gridLine,
            {
              left: x,
              top: 0,
              width: 1,
              height: mapSize.height,
            },
          ]}
        />
      );
    }
    
    // Horizontal lines
    for (let y = 0; y < mapSize.height; y += gridSpacing) {
      gridLines.push(
        <View
          key={`h-${y}`}
          style={[
            styles.gridLine,
            {
              left: 0,
              top: y,
              width: mapSize.width,
              height: 1,
            },
          ]}
        />
      );
    }
    
    return gridLines;
  };
  
  return (
    <View style={styles.container}>
      {/* Map Area */}
      <View style={styles.mapContainer}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={[
            styles.scrollContent,
            { width: mapSize.width, height: mapSize.height },
          ]}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={true}
          bouncesZoom={true}
          minimumZoomScale={0.5}
          maximumZoomScale={2}
          contentOffset={{
            x: mapSize.width / 2 - screenWidth / 2,
            y: mapSize.height / 2 - screenHeight / 4,
          }}
        >
          {/* Grid */}
          {renderGrid()}
          
          {/* Center Point */}
          <View
            style={[
              styles.centerPoint,
              {
                left: mapCenter.x - 10,
                top: mapCenter.y - 10,
              },
            ]}
          >
            <View style={styles.centerCrosshair} />
            <View style={[styles.centerCrosshair, styles.centerCrosshairVertical]} />
          </View>
          
          {/* Center Label */}
          {props.centerLabel && (
            <View
              style={[
                styles.centerLabel,
                {
                  left: mapCenter.x - 50,
                  top: mapCenter.y + 20,
                },
              ]}
            >
              <Text styles={styles.centerLabelText} text={props.centerLabel} />
            </View>
          )}
          
          {/* Radius Circle */}
          <Animated.View
            style={[
              styles.radiusCircle,
              {
                left: mapCenter.x - radiusToPixels(radius),
                top: mapCenter.y - radiusToPixels(radius),
                width: radiusToPixels(radius) * 2,
                height: radiusToPixels(radius) * 2,
              },
            ]}
          >
            {/* Draggable handle on circle edge */}
            <TouchableOpacity
              style={[
                styles.radiusHandleContainer,
                {
                  right: -16,
                  top: radiusToPixels(radius) - 16,
                },
              ]}
              activeOpacity={0.8}
              {...panResponder.panHandlers}
            >
              <View style={styles.radiusHandle} />
            </TouchableOpacity>

            {/* Additional handles for better UX */}
            <TouchableOpacity
              style={[
                styles.radiusHandleContainer,
                {
                  left: -16,
                  top: radiusToPixels(radius) - 16,
                },
              ]}
              activeOpacity={0.8}
              {...panResponder.panHandlers}
            >
              <View style={styles.radiusHandle} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.radiusHandleContainer,
                {
                  left: radiusToPixels(radius) - 16,
                  top: -16,
                },
              ]}
              activeOpacity={0.8}
              {...panResponder.panHandlers}
            >
              <View style={styles.radiusHandle} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.radiusHandleContainer,
                {
                  left: radiusToPixels(radius) - 16,
                  bottom: -16,
                },
              ]}
              activeOpacity={0.8}
              {...panResponder.panHandlers}
            >
              <View style={styles.radiusHandle} />
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </View>
      
      {/* Controls */}
      <View style={styles.controlsContainer}>
        {/* Radius Display */}
        <View style={styles.radiusDisplay}>
          <Text
            styles={styles.radiusText}
            text={`Search Radius: ${Math.round(radius)} ${radiusUnit}`}
          />
        </View>
        
        {/* Slider Control */}
        {showSlider && (
          <View style={styles.sliderContainer}>
            <Text styles={styles.sliderLabel} text={`${minRadius} ${radiusUnit}`} />
            <Slider
              style={styles.slider}
              minimumValue={minRadius}
              maximumValue={maxRadius}
              step={1}
              value={radius}
              onValueChange={handleSliderChange}
              minimumTrackTintColor={styles.primaryColor}
              maximumTrackTintColor={styles.secondaryColor}
              thumbTintColor={styles.primaryColor}
              tapToSeek={true}
            />
            <Text styles={styles.sliderLabel} text={`${maxRadius} ${radiusUnit}`} />
          </View>
        )}
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.palette.white,
    },
    mapContainer: {
      flex: 1,
      backgroundColor: '#f8f9fa',
      borderRadius: theme.borderRadius(2),
      margin: theme.spacing(2),
      overflow: 'hidden',
      ...theme.zIndex(1),
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      position: 'relative',
    },
    gridLine: {
      position: 'absolute',
      backgroundColor: '#e9ecef',
      opacity: 0.6,
    },
    centerPoint: {
      position: 'absolute',
      width: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
      ...theme.zIndex(3),
    },
    centerCrosshair: {
      position: 'absolute',
      backgroundColor: theme.palette.primary,
      width: 20,
      height: 2,
    },
    centerCrosshairVertical: {
      width: 2,
      height: 20,
    },
    centerLabel: {
      position: 'absolute',
      backgroundColor: theme.palette.white,
      paddingHorizontal: theme.spacing(2),
      paddingVertical: theme.spacing(1),
      borderRadius: theme.borderRadius(1),
      borderWidth: 1,
      borderColor: theme.palette.lightGray,
      width: 100,
      alignItems: 'center',
      ...theme.zIndex(2),
    },
    centerLabelText: {
      ...theme.typography.normal.small,
      color: theme.palette.primary,
      textAlign: 'center',
    },
    radiusCircle: {
      position: 'absolute',
      borderWidth: 2,
      borderColor: theme.palette.secondary,
      borderRadius: 1000, // Large value to ensure perfect circle
      backgroundColor: 'rgba(71, 159, 253, 0.1)', // Semi-transparent fill
      ...theme.zIndex(2),
    },
    radiusHandleContainer: {
      position: 'absolute',
      width: 32,
      height: 32,
      justifyContent: 'center',
      alignItems: 'center',
      ...theme.zIndex(4),
    },
    radiusHandle: {
      width: 16,
      height: 16,
      borderRadius: 8,
      backgroundColor: theme.palette.secondary,
      borderWidth: 2,
      borderColor: theme.palette.white,
      shadowColor: theme.palette.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    controlsContainer: {
      backgroundColor: theme.palette.white,
      paddingHorizontal: theme.spacing(4),
      paddingVertical: theme.spacing(3),
      borderTopWidth: 1,
      borderTopColor: theme.palette.lightGray,
    },
    radiusDisplay: {
      alignItems: 'center',
      marginBottom: theme.spacing(3),
    },
    radiusText: {
      ...theme.typography.bold.medium,
      color: theme.palette.primary,
      fontSize: 18,
    },
    sliderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing(2),
    },
    slider: {
      flex: 1,
      height: 40,
      marginHorizontal: theme.spacing(3),
    },
    sliderLabel: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
      minWidth: 50,
      textAlign: 'center',
    },
  });

  const colors = {
    primaryColor: theme.palette.primary,
    secondaryColor: theme.palette.secondary,
  };

  return { ...styles, ...colors };
};
