# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 41ms
  [gap of 16ms]
generate_cxx_metadata completed in 75ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 48ms
  [gap of 25ms]
generate_cxx_metadata completed in 99ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 31ms
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 45ms
  [gap of 12ms]
  write-metadata-json-to-file 22ms
generate_cxx_metadata completed in 101ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 29ms
  [gap of 13ms]
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 39ms
  [gap of 14ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 85ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 21ms
  [gap of 11ms]
generate_cxx_metadata completed in 56ms

