import {logsEnabled} from '../app.constant';
import {IHaveId} from '../interfaces';
import {<PERSON><PERSON><PERSON>} from 'buffer';

export const isNullOrEmpty = (obj: any) => {
  return !obj || Object.keys(obj).length <= 0;
};

export const isNullOrUndefined = (value: any): boolean => {
  return value === undefined || value == null;
};

export const isNullOrWhitespace = (
  value: string | null | undefined,
): boolean => {
  let isEmpty: boolean = false;
  if (!value || value.trim() == '') {
    isEmpty = true;
  }

  return isEmpty;
};

export const isNullOrZero = (value?: number): boolean => {
  return value === undefined || value == null || value == 0;
};

export const greaterThanZero = (value?: any): boolean => {
  return (
    value !== undefined &&
    value != null &&
    typeof value === 'number' &&
    Number(value) > 0
  );
};

export const concatParam = (
  params: string,
  value: any,
  paramName: string,
): string => {
  if (!isNullOrUndefined(value)) {
    params += isNullOrWhitespace(params) ? '?' : '&';
    params += paramName + '=' + value;
  }

  return String(params);
};

export const formatNumberUptoSevenDigit = (num: number): string => {
  let numStr = '';
  if (!isNullOrUndefined(num)) {
    numStr = num.toString();
  }

  let numLen = numStr.length;
  let remainingZeroLen = 7 - numLen;

  for (let index = remainingZeroLen; index > 0; index--) {
    numStr = '0' + numStr;
  }

  return numStr;
};

export const getClientDateTime = (utcDateTime: Date) => {
  if (utcDateTime !== undefined && utcDateTime != null) {
    let dated: Date = new Date(utcDateTime);
    let offset = dated.getTimezoneOffset() * -1;
    dated.setMinutes(dated.getMinutes() + offset);
    utcDateTime = dated;
  }

  return utcDateTime;
};

export const isSpecialCharacterExist = (value: string): boolean => {
  let regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/;
  return regex.test(value);
};

export const hasNumber = (myString: string) => {
  return /\d/.test(myString);
};

export const isNumberExists = (str: string): boolean => {
  let isExists = false;
  let regex = /\d+/;

  if (!isNullOrWhitespace(str) && str.match(regex)) {
    isExists = true;
  }

  return isExists;
};

export const hasSpecialCharacters = (value: any) => {
  let regex = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return (
    !isNullOrUndefined(value) && !isNullOrWhitespace(value) && regex.test(value)
  );
};

export const isNumber = (value: any): boolean => {
  let regex = /^\d+$/;
  return (
    !isNullOrUndefined(value) && !isNullOrWhitespace(value) && regex.test(value)
  );
};

export const isValidURL = (url: string): boolean => {
  let isValid: boolean = false;

  if (url !== undefined && url != null && url.trim() != '') {
    let regex =
      /(http|https):\/\/(\w+:{0,1}\w*)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%!\-\/]))?/;
    isValid = regex.test(url);
  }

  return isValid;
};

export const isValidWebAddress = (webAddressString: string): boolean => {
  let isValid: boolean = false;

  if (!isNullOrWhitespace(webAddressString)) {
    let regex =
      /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/;
    isValid = regex.test(webAddressString);
  }

  return isValid;
};

export const isValidEmail = (emailString: string): boolean => {
  let isValid: boolean = false;

  if (!isNullOrWhitespace(emailString)) {
    emailString = emailString.trim();
    let regex =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    isValid = regex.test(String(emailString).toLowerCase());
  }

  return isValid;
};

export const hasValues = (items: any[]) => {
  return !isNullOrUndefined(items) && items.length > 0;
};

export const isNew = (item: IHaveId) => {
  return isNullOrZero(item.Id);
};

export const getMethods = (obj: any) =>
  Object.getOwnPropertyNames(obj).filter(
    item => typeof obj[item] === 'function',
  );

export const getPropertyNames = (obj: any) =>
  Object.getOwnPropertyNames(obj).filter(
    item => typeof obj[item] !== 'function',
  );

export const getKeys = (obj: any) => Object.keys(obj);

export const getQueryParams = (params: any) => {
  let queryParams = '';
  if (params) {
    let keys = getPropertyNames(params);
    if (keys && keys.length > 0) {
      keys.forEach(key => {
        if (key && !isNullOrUndefined(params[key])) {
          queryParams += `${
            isNullOrEmpty(queryParams) ? '?' : '&'
          }${key}=${encodeURIComponent(params[key])}`;
        }
      });
    }
  }

  return queryParams;
};

export const getChunks = <T extends any>(arr: T[], size: number) => {
  let newArray: T[][] = [];
  let i: number,
    j: number,
    temporary: T[],
    chunk: number = size;
  for (i = 0, j = arr.length; i < j; i += chunk) {
    temporary = arr.slice(i, i + chunk);
    newArray = [...newArray, temporary];
  }

  return newArray;
};

export const getCommaSeparatedString = (values: number[] | string[]) => {
  let returnValue: string = '';

  if (hasValues(values)) {
    values.forEach(value => {
      returnValue = `${returnValue}${
        isNullOrWhitespace(returnValue) ? '' : ','
      }${value}`;
    });
  }

  return returnValue;
};

export const isHtml = (value: string) => {
  return /<[a-z/][\s\S]*>/i.test(value);
};

export const getExtensionFromFileName = (fileName: string | null) => {
  if (!!fileName && fileName.trim() != '' && fileName.indexOf('.') >= 0) {
    const extension = fileName.substring(fileName.lastIndexOf('.'));
    return extension;
  }

  return '';
};

export const decodeBase64String = (base64String: string): string => {
  return Buffer.from(base64String, 'base64').toString();
};

export const encodeBase64String = (stringToEncode: string): string => {
  let buff = Buffer.from(stringToEncode);
  return buff.toString('base64');
};

export const getThousandsSeparated = (val:number) => {
  return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const arraysEqual = (a:any[], b:any[]) => {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (a.length !== b.length) return false;

  // If you don't care about the order of the elements inside
  // the array, you should sort both arrays here.
  // Please note that calling sort on an array will modify that array.
  // you might want to clone your array first.

  for (var i = 0; i < a.length; ++i) {
    if (a[i] !== b[i]) return false;
  }
  
  return true;
}