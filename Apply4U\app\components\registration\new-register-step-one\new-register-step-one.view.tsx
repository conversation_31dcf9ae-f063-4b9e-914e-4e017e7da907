import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { TextInput, Text, TouchableRipple, HelperText } from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent, A4UHuma } from '../../common';
import { a4uHuma } from '../../../assets';

const countries = [
  { label: 'United Kingdom', value: 'United Kingdom' },
  { label: 'United States', value: 'United States' },
  { label: 'Canada', value: 'Canada' },
  { label: 'Australia', value: 'Australia' }
];

export const NewRegisterStepOneView = () => {
  const styles = useCurrentTheme(createStyles);
  const [firstName, setFirstName] = useState('Irfan');
  const [lastName, setLastName] = useState('Hussain');
  const [contactNumber, setContactNumber] = useState('9851111111');
  const [country, setCountry] = useState('United Kingdom');
  const [city, setCity] = useState('London');
  const [postCode, setPostCode] = useState('N1 9AG');
  const [showDropdown, setShowDropdown] = useState(false);

  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [contactNumberError, setContactNumberError] = useState('');
  const [countryError, setCountryError] = useState('');
  const [cityError, setCityError] = useState('');
  const [postCodeError, setPostCodeError] = useState('');

  const [touched, setTouched] = useState({
    firstName: false,
    lastName: false,
    contactNumber: false,
    country: false,
    city: false,
    postCode: false,
  });

  const validateFirstName = (text: string): boolean => {
    if (!text.trim()) {
      setFirstNameError('First name is required');
      return false;
    } else {
      setFirstNameError('');
      return true;
    }
  };

  const validateLastName = (text: string): boolean => {
    if (!text.trim()) {
      setLastNameError('Last name is required');
      return false;
    } else {
      setLastNameError('');
      return true;
    }
  };

  const validateContactNumber = (text: string): boolean => {
    const phoneRegex = /^[0-9]{10,15}$/;
    if (!text.trim()) {
      setContactNumberError('Contact number is required');
      return false;
    } else if (!phoneRegex.test(text.replace(/\s/g, ''))) {
      setContactNumberError('Please enter a valid contact number');
      return false;
    } else {
      setContactNumberError('');
      return true;
    }
  };

  const validateCountry = (text: string): boolean => {
    if (!text.trim()) {
      setCountryError('Country is required');
      return false;
    } else {
      setCountryError('');
      return true;
    }
  };

  const validateCity = (text: string): boolean => {
    if (!text.trim()) {
      setCityError('City is required');
      return false;
    } else {
      setCityError('');
      return true;
    }
  };

  const validatePostCode = (text: string): boolean => {
    if (!text.trim()) {
      setPostCodeError('Post code is required');
      return false;
    } else {
      setPostCodeError('');
      return true;
    }
  };

  const handleContinue = () => {
    Keyboard.dismiss();

    const isFirstNameValid = validateFirstName(firstName);
    const isLastNameValid = validateLastName(lastName);
    const isContactNumberValid = validateContactNumber(contactNumber);
    const isCountryValid = validateCountry(country);
    const isCityValid = validateCity(city);
    const isPostCodeValid = validatePostCode(postCode);

    setTouched({
      firstName: true,
      lastName: true,
      contactNumber: true,
      country: true,
      city: true,
      postCode: true,
    });

    if (isFirstNameValid && isLastNameValid && isContactNumberValid &&
        isCountryValid && isCityValid && isPostCodeValid) {
      navigate(screens.NewRegisterStepTwo);
    }
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepTwo);
  };

  const handleGoBack = () => {
    navigate(screens.CvUpload);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  return (
    <RegistrationStepComponent
      title="Personal Details"
      currentStep={1}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <A4UHuma
        image={a4uHuma}
        text="I have extracted personal details form your CV, please review before continuing"
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />
      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="First Name"
          placeholder="Enter First Name"
          value={firstName}
          onChangeText={(text) => {
            setFirstName(text);
            if (touched.firstName) {
              validateFirstName(text);
            }
          }}
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!firstNameError && touched.firstName && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!firstNameError && touched.firstName ? "#FF3B30" : "#39608F"}
          error={!!firstNameError && touched.firstName}
          onBlur={() => {
            setTouched({ ...touched, firstName: true });
            validateFirstName(firstName);
          }}
        />
        {!!firstNameError && touched.firstName && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {firstNameError}
          </HelperText>
        )}
      </View>

      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Last Name"
          placeholder="Enter Last Name"
          value={lastName}
          onChangeText={(text) => {
            setLastName(text);
            if (touched.lastName) {
              validateLastName(text);
            }
          }}
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!lastNameError && touched.lastName && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!lastNameError && touched.lastName ? "#FF3B30" : "#39608F"}
          error={!!lastNameError && touched.lastName}
          onBlur={() => {
            setTouched({ ...touched, lastName: true });
            validateLastName(lastName);
          }}
        />
        {!!lastNameError && touched.lastName && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {lastNameError}
          </HelperText>
        )}
      </View>
      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Contact Number"
          value={contactNumber}
          onChangeText={(text) => {
            setContactNumber(text);
            if (touched.contactNumber) {
              validateContactNumber(text);
            }
          }}
          placeholder="e.g 9851111111"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!contactNumberError && touched.contactNumber && styles.errorInputOutline
          ]}
          keyboardType="phone-pad"
          activeOutlineColor="#0E1C5D"
          outlineColor={!!contactNumberError && touched.contactNumber ? "#FF3B30" : "#39608F"}
          error={!!contactNumberError && touched.contactNumber}
          onBlur={() => {
            setTouched({ ...touched, contactNumber: true });
            validateContactNumber(contactNumber);
          }}
        />
        {!!contactNumberError && touched.contactNumber && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {contactNumberError}
          </HelperText>
        )}
      </View>

      <View style={styles.inputGroup}>
        <View>
          <TextInput
            mode="outlined"
            label="Country"
            value={country}
            placeholder="Select country"
            style={styles.input}
            outlineStyle={[
              styles.inputOutline,
              !!countryError && touched.country && styles.errorInputOutline
            ]}
            right={<TextInput.Icon icon="chevron-down" onPress={() => setShowDropdown(true)} />}
            activeOutlineColor="#0E1C5D"
            outlineColor={!!countryError && touched.country ? "#FF3B30" : "#39608F"}
            error={!!countryError && touched.country}
            editable={false}
            onPressIn={() => {
              setShowDropdown(true);
              setTouched({ ...touched, country: true });
              validateCountry(country);
            }}
          />
          {!!countryError && touched.country && (
            <HelperText type="error" visible={true} style={styles.helperText}>
              {countryError}
            </HelperText>
          )}
        </View>

        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowDropdown(false)}
          >
            <View style={styles.dropdownContainer}>
              <FlatList
                data={countries}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                  <TouchableRipple
                    style={styles.dropdownItem}
                    onPress={() => {
                      setCountry(item.value);
                      setShowDropdown(false);
                    }}
                    rippleColor="rgba(14, 28, 93, 0.1)"
                  >
                    <Text style={styles.dropdownItemText}>{item.label}</Text>
                  </TouchableRipple>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>

      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Town/City"
          value={city}
          onChangeText={(text) => {
            setCity(text);
            if (touched.city) {
              validateCity(text);
            }
          }}
          placeholder="Enter Town/City"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!cityError && touched.city && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!cityError && touched.city ? "#FF3B30" : "#39608F"}
          error={!!cityError && touched.city}
          onBlur={() => {
            setTouched({ ...touched, city: true });
            validateCity(city);
          }}
        />
        {!!cityError && touched.city && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {cityError}
          </HelperText>
        )}
      </View>

      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Post Code"
          value={postCode}
          onChangeText={(text) => {
            setPostCode(text);
            if (touched.postCode) {
              validatePostCode(text);
            }
          }}
          placeholder="Enter Post Code"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!postCodeError && touched.postCode && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!postCodeError && touched.postCode ? "#FF3B30" : "#39608F"}
          error={!!postCodeError && touched.postCode}
          onBlur={() => {
            setTouched({ ...touched, postCode: true });
            validatePostCode(postCode);
          }}
        />
        {!!postCodeError && touched.postCode && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {postCodeError}
          </HelperText>
        )}
      </View>
    </RegistrationStepComponent>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    humaContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 15,
      paddingHorizontal: 0,
      width: '100%',
    },
    humaImage: {
      width: 60,
      height: 60,
      marginRight: 16,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
      flex: 1,
      marginLeft: 10,
      lineHeight: 20,
    },
    customComma: {
      fontFamily: 'Poppins-SemiBold',
      color: '#007CFF',
      fontSize: 14,
    },
    inputGroup: {
      marginBottom: 15,
    },
    input: {
      backgroundColor: '#FFFFFF',
      height: 50,
      fontSize: 16,
      fontFamily: 'Poppins-Regular',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    inputOutline: {
      borderRadius: 8,
      borderColor: '#39608F',
      borderWidth: 2,
    },
    errorInputOutline: {
      borderRadius: 8,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    dropdownContainer: {
      width: '90%',
      maxHeight: 250,
      backgroundColor: '#FFFFFF',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#39608F',
      overflow: 'hidden',
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    dropdownItem: {
      paddingVertical: 0,
      paddingHorizontal: 0,
    },
    dropdownItemText: {
      fontFamily: 'Poppins-Regular',
      fontSize: 16,
      color: '#000000',
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    separator: {
      height: 1,
      backgroundColor: '#E0E0E0',
      width: '100%',
    },
  });

  return styles;
};
