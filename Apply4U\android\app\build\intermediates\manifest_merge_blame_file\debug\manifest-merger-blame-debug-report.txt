1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.apply4u"
4    android:versionCode="3005"
5    android:versionName="1.5" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:25:9-84
12-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:25:26-81
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:26:5-81
13-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:26:22-78
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:27:5-78
14-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:27:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:28:5-80
15-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:28:22-78
16    <!--
17    This manifest file is used only by Gradle to configure debug-only capabilities
18    for React Native Apps.
19    -->
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->[com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:5-78
20-->[com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:22-75
21    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
21-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
21-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
22    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
22-->[com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:23:5-82
22-->[com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:23:22-79
23
24    <permission
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
25        android:name="com.apply4u.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.apply4u.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
29    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
29-->[com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:5-110
29-->[com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:22-107
30
31    <application
31-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:5:5-24:19
32        android:name="com.apply4u.MainApplication"
32-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:6:7-38
33        android:allowBackup="false"
33-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:10:7-34
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
35        android:debuggable="true"
36        android:extractNativeLibs="true"
37        android:icon="@mipmap/ic_launcher"
37-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:8:7-41
38        android:label="@string/app_name"
38-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:7:7-39
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:9:7-52
40        android:theme="@style/AppTheme"
40-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:11:7-38
41        android:usesCleartextTraffic="true" >
41-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:6:9-44
42        <activity
42-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:12:7-23:18
43            android:name="com.apply4u.MainActivity"
43-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:13:9-37
44            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
44-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:15:9-118
45            android:exported="true"
45-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:18:9-32
46            android:label="@string/app_name"
46-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:14:9-41
47            android:launchMode="singleTask"
47-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:16:9-40
48            android:windowSoftInputMode="adjustResize" >
48-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:17:9-51
49            <intent-filter>
49-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:19:9-22:25
50                <action android:name="android.intent.action.MAIN" />
50-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:20:13-65
50-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:20:21-62
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:21:13-73
52-->D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:21:23-70
53            </intent-filter>
54        </activity>
55
56        <provider
56-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
57            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
57-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-83
58            android:authorities="com.apply4u.fileprovider"
58-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
59            android:exported="false"
59-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
60            android:grantUriPermissions="true" >
60-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
61            <meta-data
61-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
62                android:name="android.support.FILE_PROVIDER_PATHS"
62-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
63                android:resource="@xml/file_provider_paths" />
63-->[:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
64        </provider>
65
66        <activity
66-->[com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:19:9-21:40
67            android:name="com.facebook.react.devsupport.DevSettingsActivity"
67-->[com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:20:13-77
68            android:exported="false" />
68-->[com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:21:13-37
69        <activity
69-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:23:9-26:75
70            android:name="com.google.android.gms.common.api.GoogleApiActivity"
70-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:24:13-79
71            android:exported="false"
71-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:25:13-37
72            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
72-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:26:13-72
73
74        <meta-data
74-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:23:9-25:69
75            android:name="com.google.android.gms.version"
75-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:24:13-58
76            android:value="@integer/google_play_services_version" />
76-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:25:13-66
77
78        <provider
78-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
80            android:authorities="com.apply4u.androidx-startup"
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
88        </provider>
89
90        <meta-data
90-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
91            android:name="com.facebook.soloader.enabled"
91-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
92            android:value="false" />
92-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
93    </application>
94
95</manifest>
