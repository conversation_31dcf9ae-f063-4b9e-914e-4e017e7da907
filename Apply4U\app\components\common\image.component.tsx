import React from 'react';
import {
  ImageSourcePropType,
  Image as RNImage,
  ImageStyle,
  StyleProp,
} from 'react-native';

interface IProps {
  source: ImageSourcePropType;
  defaultSource?: ImageSourcePropType;
  style?: StyleProp<ImageStyle>;
  onError?: (e?: any) => void;
}
export const Image = (props: IProps) => {
  const [imageSource, setImageSource] = React.useState<ImageSourcePropType>(
    props.source,
  );

  const onImageLoadingError = (e?: any) => {
    if (props.defaultSource) {
      setImageSource(props.defaultSource);
    }

    if (props.onError) {
      props.onError(e);
    }
  };

  return (
    <RNImage
      source={imageSource}
      style={props.style}
      onError={onImageLoadingError}
    />
  );
};
