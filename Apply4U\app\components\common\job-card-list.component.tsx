import React from 'react';
import { FlatList } from 'react-native';
import { JobCard } from './new-job-card';
import { jobCardHeight } from '../../app.constant';
import { IJobSearchResponse } from '../../interfaces';
import {formatedDate} from '../../utility';

interface IProps {
  jobResults: IJobSearchResponse[];
  isMoreResultsExists: boolean;
  isSwipeable: boolean;
  isSwipeLeftEnabled: boolean;
  isSwipeRightEnabled: boolean;
  swipeRightText: string;
  showFavIcon: boolean;
  showShareIcon: boolean;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement<any, string | React.JSXElementConstructor<any>> | null | undefined;
  EmptyListComponent?: React.ComponentType<any> | React.ReactElement<any, string | React.JSXElementConstructor<any>> | null | undefined;
  favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
  goToJobDetail: (jobId: number) => void;
  loadMore: () => void;
  onSwipeRight: (jobId: number) => void;
  handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
}



export const JobCardList = (props: IProps) => {
  const goToJobDetail = React.useCallback(props.goToJobDetail, []);
  const favJobsAddOrRemove = React.useCallback(props.favJobsAddOrRemove, []);
  const swipeRight = React.useCallback(props.onSwipeRight, []);
  const easyApply = React.useCallback(props.handleEasyApply, []);

  const getItemLayout = React.useCallback(
    (data: any, index: any) => ({
      length: jobCardHeight,
      offset: jobCardHeight * index,
      index,
    }),
    [],
  );

  const keyExtractor = React.useCallback(
    (item: IJobSearchResponse, index: number) => `list_item_${item.Id}_${item.Title.replace(' ', '_')}`,
    [],
  );

  const onEndReached = (info: any) => {
    if (props.isMoreResultsExists) {
      props.loadMore();
    }
  };

  const listItem = (itemInfo: any) => (

    <JobCard
      jobId={itemInfo.item.Id}
      key={`${itemInfo.item.Id}_${itemInfo.item.Title.replace(' ', '_')}`}
      companyLogoUrl={itemInfo.item.LogoUrl}
      jobTitle={itemInfo.item.Title}
      companyName={itemInfo.item.CompanyName}
      jobCategory={itemInfo.item.JobCategory}
      locationText={itemInfo.item.LocalityName ?? itemInfo.item.AreaName ?? itemInfo.item.RegionName ?? itemInfo.item.LocationText}
      salaryRange={itemInfo.item.Salary}
      jobType={itemInfo.item.JobType}
      jobDetails={itemInfo.item.Description}
      showFavIcon={props.showFavIcon}
      showShareIcon={props.showShareIcon}
      isSwipeable={props.isSwipeable}
      isAlreadyApplied={itemInfo.item.IsApplied}
      applicationUrl={itemInfo.item.ApplicationUrl}
      jobSourceId={itemInfo.item.JobSourceId}
      isSwipeLeftEnabled={props.isSwipeLeftEnabled}
      isSwipeRightEnabled={props.isSwipeRightEnabled}
      swipeRightText={props.swipeRightText}
      isFav={itemInfo.item.IsFav ?? false}
      favJobsAddOrRemove={favJobsAddOrRemove}
      goToJobDetail={goToJobDetail}
      onSwipeRight={swipeRight}
      easyApply={easyApply}
      postedOnText={formatedDate(itemInfo.item.DisplayStartDate,'DD/MM')}
    />
  );

  return <FlatList
      data={props.jobResults}
      keyExtractor={keyExtractor}
      onEndReached={onEndReached}
      getItemLayout={getItemLayout}
      onEndReachedThreshold={0.01}
      renderItem={listItem}
      initialNumToRender={10}
      maxToRenderPerBatch={20}
      updateCellsBatchingPeriod={30}
      removeClippedSubviews={true}
      windowSize={21}
      ListFooterComponent={props.ListFooterComponent}
      ListEmptyComponent={props.EmptyListComponent}
    />;
};