import {UserAppVisitActionConstants} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {initialState} from '../initial-state';

export const isFirstVisitReducer = (
  state: boolean = initialState.isFirstVisit,
  action: IAction<boolean>,
) => {
  switch (action.type) {
    case UserAppVisitActionConstants.SET_IS_FIRST_VISIT:
      let newState: boolean = action.payload as boolean;
      return newState;
    default:
      return state;
  }
};
