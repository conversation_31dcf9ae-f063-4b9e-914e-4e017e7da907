import {getMethods, hasValues, isNullOrEmpty, isNullOrUndefined} from '.';
import {
  IValidationRules,
  IValidationResult,
  ValidationRules,
  ValidateAllResult,
  ValidationResults,
} from '../interfaces/validation';
import {
  getPropertyN<PERSON>s,
  greaterThanZero,
  hasSpecialCharacters,
  isNumber,
  isValidEmail,
} from './app.util';

export const validate = (
  value: any,
  validations?: IValidationRules,
): IValidationResult[] => {
  let validationResults: IValidationResult[] = [];

  if (validations && !isNullOrUndefined(validations)) {
    if (validations.required) {
      let required = validations.required;
      if (required && required.isRequired === true) {
        if (isNullOrEmpty(value)) {
          let validationMessage = 'Required';
          if (required.message && !isNullOrEmpty(required.message)) {
            validationMessage = required.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.minLength && !isNullOrEmpty(value)) {
      let minLength = validations.minLength;
      if (minLength.minLength && greaterThanZero(minLength.minLength)) {
        if (value.toString().length < minLength.minLength) {
          let validationMessage = `Value must be atleast ${minLength.minLength} characters long`;
          if (minLength.message && !isNullOrEmpty(minLength.message)) {
            validationMessage = minLength.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.maxLength && !isNullOrEmpty(value)) {
      let maxLength = validations.maxLength;
      if (maxLength.maxLength && greaterThanZero(maxLength.maxLength)) {
        if (value.toString().length > maxLength.maxLength) {
          let validationMessage = `Value must not be greater than ${maxLength.maxLength} characters`;
          if (maxLength.message && !isNullOrEmpty(maxLength.message)) {
            validationMessage = maxLength.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.numberOnly && !isNullOrEmpty(value)) {
      let numberOnly = validations.numberOnly;
      if (numberOnly.allowNumbersOnly && numberOnly.allowNumbersOnly === true) {
        if (!isNumber(value)) {
          let validationMessage = `Value can contains number only`;
          if (numberOnly.message && !isNullOrEmpty(numberOnly.message)) {
            validationMessage = numberOnly.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.specialCharacters && !isNullOrEmpty(value)) {
      let specialCharacters = validations.specialCharacters;
      if (specialCharacters.allow === false) {
        if (hasSpecialCharacters(value)) {
          let validationMessage = `Value can't contains special characters`;
          if (
            specialCharacters.message &&
            !isNullOrEmpty(specialCharacters.message)
          ) {
            validationMessage = specialCharacters.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.validEmail && !isNullOrEmpty(value)) {
      let validEmail = validations.validEmail;
      if (validEmail.checkValidEmail && validEmail.checkValidEmail === true) {
        if (!isValidEmail(value)) {
          let validationMessage = `Invalid email address`;
          if (validEmail.message && !isNullOrEmpty(validEmail.message)) {
            validationMessage = validEmail.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: validationMessage},
          ];
        }
      }
    }

    if (validations.minValue && !isNaN(value) && !isNullOrEmpty(value)) {
      let minValueVaidation = validations.minValue;

      if (minValueVaidation && minValueVaidation.minValue) {
        if (Number(value) < Number(minValueVaidation.minValue)) {
          let minValueMessage = `Value must be greater than ${minValueVaidation.minValue}`;

          if (
            minValueVaidation.message &&
            !isNullOrEmpty(minValueVaidation.message)
          ) {
            minValueMessage = minValueVaidation.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: minValueMessage},
          ];
        }
      }
    }

    if (validations.maxValue && !isNaN(value) && !isNullOrEmpty(value)) {
      let maxValueVaidation = validations.maxValue;

      if (maxValueVaidation && maxValueVaidation.maxValue) {
        if (Number(value) > Number(maxValueVaidation.maxValue)) {
          let maxValueMessage = `Value must be less than ${maxValueVaidation.maxValue}`;

          if (
            maxValueVaidation.message &&
            !isNullOrEmpty(maxValueVaidation.message)
          ) {
            maxValueMessage = maxValueVaidation.message;
          }

          validationResults = [
            ...validationResults,
            {isValid: false, message: maxValueMessage},
          ];
        }
      }
    }

    if (validations.validate && !isNullOrEmpty(value)) {
      let validationMethodNames = getMethods(validations.validate);
      if (validationMethodNames && hasValues(validationMethodNames)) {
        let customValidationMessage: string = '';
        for (let i = 0; i < validationMethodNames.length; i++) {
          let validationMethodName = validationMethodNames[i];
          let validationMethod = validations.validate[validationMethodName];
          let result = validationMethod(value);

          if (typeof result === 'string') {
            customValidationMessage = result as string;
            validationResults = [
              ...validationResults,
              {isValid: false, message: customValidationMessage},
            ];
          }
        }
      }
    }
  }

  return validationResults;
};

export function validateAll<T>(
  validations: ValidationRules<T>,
  state: T,
): ValidateAllResult<T> {
  let validateAllResults: ValidateAllResult<T> = {
    isValid: true,
  };

  if (validations && state) {
    let propertyNamesToValidate = getPropertyNames(validations);

    if (propertyNamesToValidate && hasValues(propertyNamesToValidate)) {
      let initialResults: any = {};
      propertyNamesToValidate.forEach(
        p => (initialResults[p] = {validationResults: []}),
      );

      validateAllResults = {
        ...validateAllResults,
        validationResults: initialResults as ValidationResults<T>,
      };

      propertyNamesToValidate.forEach(propertyName => {
        let validationRules =
          validations[propertyName as keyof T].validationRules;
        let value = state[propertyName as keyof T];

        if (validationRules) {
          let validationResult = validate(value, validationRules);

          if (
            validationResult &&
            hasValues(validationResult) &&
            validateAllResults.validationResults
          ) {
            let fieldValidationResults = {
              ...validateAllResults.validationResults,
            };
            fieldValidationResults[propertyName as keyof T].validationResults =
              [...validationResult];
            validateAllResults = {
              ...validateAllResults,
              isValid: false,
              validationResults: {...fieldValidationResults},
            };
          }
        }
      });
    }
  }

  return validateAllResults;
}
