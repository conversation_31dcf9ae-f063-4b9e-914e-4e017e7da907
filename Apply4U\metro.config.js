const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    extraNodeModules: {
      'deprecated-react-native-prop-types': path.resolve(__dirname, 'node_modules/deprecated-react-native-prop-types'),
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
