import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  ScrollView,
} from 'react-native';
import {
  Text,
  Checkbox,
} from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent,A4UHuma } from '../../common';
import { successBubbles } from '../../../assets';
import { a4uHuma } from '../../../assets';

export const NewRegisterStepFiveView = () => {
  const styles = useCurrentTheme(createStyles);
  const [applyOnBehalf, setApplyOnBehalf] = useState(false);
  const [reviewCV, setReviewCV] = useState(false);
  const [sendMatchingJobs, setSendMatchingJobs] = useState(true);
  const [distributeCV, setDistributeCV] = useState(true);

  const handleContinue = () => {
    Keyboard.dismiss();
    navigate(screens.SuccessScreen, {
      title: 'Congratulation !!!',
      message: 'You have successfully completed your registration!',
      message2: 'Your profile is now ready to help you find the perfect job.',
      buttonText: 'Dashboard',
      navigateTo: screens.Dashboard,
      customImage: successBubbles,
      checkmarkPosition: 'center'
    });
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.SuccessScreen, {
      title: 'Congratulation !!!',
      message: 'You have successfully completed your registration!',
      message2: 'Your profile is now ready to help you find the perfect job.',
      buttonText: 'Dashboard',
      navigateTo: screens.Dashboard,
      customImage: successBubbles,
      checkmarkPosition: 'center'
    });
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterStepFour);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  const handleTermsPress = () => {
  };

  const handlePrivacyPress = () => {
  };

  return (
    <RegistrationStepComponent
      title="Application Preferences"
      currentStep={5}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <ScrollView style={styles.scrollView}>
      <A4UHuma
        image={a4uHuma}
        text="Would you like me to further help you with your job search? Please see options below"
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />

        <View style={styles.checkboxContainer}>
          <View style={styles.checkboxRow}>
            <Checkbox.Item
              label="Apply on my behalf to relevant jobs"
              status={applyOnBehalf ? 'checked' : 'unchecked'}
              onPress={() => setApplyOnBehalf(!applyOnBehalf)}
              labelStyle={styles.checkboxLabel}
              style={styles.checkbox}
              color="#0E1C5D"
              position='leading'
              uncheckedColor="#333333"
            />
          </View>

          <View style={styles.checkboxRow}>
            <Checkbox.Item
              label="Professionally review my CV for free (Top CV)"
              status={reviewCV ? 'checked' : 'unchecked'}
              onPress={() => setReviewCV(!reviewCV)}
              labelStyle={styles.checkboxLabel}
              style={styles.checkbox}
              color="#0E1C5D"
              position='leading'
              uncheckedColor="#333333"
            />
          </View>

          <View style={styles.checkboxRow}>
            <Checkbox.Item
              label="Send me matching jobs via email"
              status={sendMatchingJobs ? 'checked' : 'unchecked'}
              onPress={() => setSendMatchingJobs(!sendMatchingJobs)}
              labelStyle={styles.checkboxLabel}
              style={styles.checkbox}
              color="#0E1C5D"
              position='leading'
              uncheckedColor="#333333"
            />
          </View>

          <View style={styles.checkboxRow}>
            <Checkbox.Item
              label="Distribute my CV to UK's top employers & CV Library"
              status={distributeCV ? 'checked' : 'unchecked'}
              onPress={() => setDistributeCV(!distributeCV)}
              labelStyle={styles.checkboxLabel}
              style={styles.checkbox}
              color="#0E1C5D"
              position='leading'
              uncheckedColor="#333333"
            />
          </View>
        </View>

        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            By clicking Join Now, you agree to Apply4U's{' '}
            <Text style={styles.linkText} onPress={handleTermsPress}>
              Terms & Conditions
            </Text>{' '}
            and you acknowledge that you have read Apply4U's{' '}
            <Text style={styles.linkText} onPress={handlePrivacyPress}>
              Privacy policy
            </Text>{' '}
            which will apply to the processing of your personal data in the provision of our services Join Now
          </Text>
        </View>
      </ScrollView>
    </RegistrationStepComponent>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    scrollView: {
      flex: 1,
      width: '100%',
      paddingHorizontal: 20,
    },
    humaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 25,
      paddingHorizontal: 0,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
    },
    checkboxContainer: {
      marginBottom: 20,
    },
    checkboxRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 5,
    },
    checkbox: {
      paddingVertical: 8,
      marginBottom: 5,
      borderRadius: 8,
      paddingRight: 0,
      paddingLeft: 0,
      width: '100%',
    },
    checkboxLabel: {
      fontSize: 14,
      fontFamily: 'Poppins-Regular',
      color: '#333333',
      marginLeft: 8,
      flex: 1,
      textAlign: 'left',
    },
    termsContainer: {
      marginBottom: 20,
      marginTop: 20,
    },
    termsText: {
      fontSize: 12,
      fontFamily: 'Poppins-Regular',
      color: '#555555',
      lineHeight: 18,
      textAlign: 'left',
    },
    linkText: {
      color: '#14C59C',
      textDecorationLine: 'underline',
      fontFamily: 'Poppins-Medium',
    },
  });

  return styles;
};
