import React from 'react';
import { StyleSheet, View } from 'react-native';
import { jobCardHeight } from '../../../app.constant';
import { IJobSearchResponse } from '../../../interfaces';
import { ITheme, useCurrentTheme } from '../../../theme';
import { JobCardList, MainLayout, Text } from '../../common';

interface IProps {
    myJobsResults: IJobSearchResponse[];
    isMoreResultsExists: boolean;
    noFavJobs:boolean,
    jobRemoveFromFav: (jobId: number,isFav:boolean) => void;
    goToJobDetail: (jobId: number) => void;
    loadMore: () => void;
    onHideJob: (jobId: number) => void;
    handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
}

export const ShortlistedJobsView = (props: IProps) => {
    const styles = useCurrentTheme(createStyles);
    return (
        <MainLayout>
            <View style={styles.header}>
                <Text
                    styles={styles.headerText}
                    text={`Shortlisted Jobs`}
                    numberOfLines={2}
                />
            </View>

            <View style={styles.container}>
                {!props.noFavJobs && <JobCardList
                    jobResults={props.myJobsResults}
                    isMoreResultsExists={props.isMoreResultsExists}
                    isSwipeable={true}
                    isSwipeLeftEnabled={true}
                    isSwipeRightEnabled={false}
                    swipeRightText='Hide'
                    showFavIcon={true}
                    showShareIcon={true}
                    goToJobDetail={props.goToJobDetail}
                    handleEasyApply={props.handleEasyApply}
                    favJobsAddOrRemove={props.jobRemoveFromFav}
                    loadMore={props.loadMore}
                    onSwipeRight={props.onHideJob}
                />}
                {!!props.noFavJobs && props.noFavJobs == true && <View style={styles.noResultsMessageContainer}>
                    <Text styles={styles.noResultsMessageText} text='You have not added any job to your jobs list yet.' />
                </View>}
            </View>
        </MainLayout>
    );
}

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        container: {
            flex: 1,
            marginBottom: 10,
        },
        header: {
            height: 60,
            width: '100%',
            flexDirection: 'row',
            backgroundColor: theme.palette.primary,
            justifyContent: 'center',
            alignItems: 'center'
        },
        headerText: {
            ...theme.typography.bold.medium,
            color: '#fff',
            textAlign: 'center',
        },
        noResultsMessageContainer:{
            backgroundColor:theme.palette.white,
            borderRadius:theme.borderRadius(5),
            height:jobCardHeight,
            marginTop:theme.spacing(100),
            width:'95%',
            alignSelf:'center',
            justifyContent:'center',
        },
        noResultsMessageText:{
            ...theme.typography.bold.large,
            color:'#333333',
            alignSelf:'center',
        }
    });

    return styles;
};