import React from 'react';
import { OtpVerificationView } from './otp-verification.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

interface IProps {
  route?: {
    params?: {
      email?: string;
    };
  };
}

const OtpVerificationContainer = (props: IProps) => {

  const email = props.route?.params?.email || '<EMAIL>';
  
  return <OtpVerificationView email={email} />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedOtpVerificationContainer = connect(
  mapStateToProps,
  null,
)(OtpVerificationContainer);

export { connectedOtpVerificationContainer as OtpVerificationContainer };
