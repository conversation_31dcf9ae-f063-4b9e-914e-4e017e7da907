export interface IUserProfile {
  UserId: number;
  Id: number;
  Emails: any;
  NationalityId: any;
  WebAddresses: any;
  PostionTypeId: any;
  Addresses: any;
  MaritalStatusId: any;
  Qualifications: any;
  DrivingLicenceTypeId: any;
  Experiences: any;
  CandidateTypeId: any;
  Phones: any;
  PositionLevelId: any;
  UserLanguages: any;
  CurrencyId: any;
  UserSkills: any;
  SalaryDurationId: any;
  UserJobPreference: any;
  GenderId: any;
  UserOtherInformation: any;
  EducationLevelId: any;
  ResumeInformation: any;
  UserNumber: any;
  InitialScreening: any;
  FirstName: string;
  EducationLevel: any;
  LastName: string;
  SystemUser: any;
  DefaultAddress: any;
  MiddleName: any;
  PositionLevel: any;
  CurrentCompany: any;
  DateOfBirth: any;
  Duration: any;
  DefaultEmailAddress: any;
  OtherInformation: any;
  CandidateType: any;
  DefaultPhoneNumber: any;
  ExecutiveBrief: any;
  PositionType: any;
  ProfileView: any;
  MinimumSalary: any;
  CreditConsumedHistory: any;
  MaximumSalary: any;
  IsCandidatesNamesAllowed: any;
  TotalYearsOfExperience: any;
  CvLibraryRegistration: any;
  TotalYearsManagementExperience: any;
  FmCvRegistration: any;
  CVNowStatus: any;
  WillingToRelocate: boolean;
  AttbJobAlert: any;
  IHaveCar: any;
  SectorsDisplayString: any;
  IHaveLicence: any;
  EmploymentStatus: any;
  EligibleToWorkInUk: boolean;
  IsExistInShortlist: any;
  ProfileImagePath: string;
  IsNotesExist: any;
  ProfileCoverPath: string;
  CompanyName: any;
  CvScore: any;
  CompanyId: any;
  IsRecentInternal: boolean;
  UserConnection: any;
  SelfRatingScore: any;
  UserFollowing: any;
  IsProfilePrivate: boolean;
  JobApplications: any;
  PrivateNotes: any;
  ActionDateTime: any;
  IsConnected: any;
  ConsultantNotes: any;
  UserProfiles: any;
  FullName: string;
  IsSearchable: boolean;
  ProfileTitleType: any;
  JobTitleCollection: any;
  IsCvSecure: boolean;
  ProfileVerificationType: any;
  SkillsCollection: any;
  IsCandidate: boolean;
  AgeRange: any;
  QualificationCollection: any;
  IsRecruiter: boolean;
  SocialActivity: any;
  IndustriesCollection: any;
  EmploymentStatusId: any;
  ProfileVerification: any;
  IsShowCompanyInfo: boolean;
  IsNetworker: any;
  IsYobDataExists: boolean;
  UserJobPreferences: any;
  DashboardTypeId: number;
  UserBadges: any;
  Country: any;
  ProfileTitleTypeId: any;
  SavedSearches: any;
  AgeRangeId: any;
  MatchingEmailFrequencyId: number;
  VerificationTypeId: any;
}
