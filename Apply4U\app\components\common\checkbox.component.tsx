import React from 'react';
import {StyleSheet} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import BouncyCheckbox from 'react-native-bouncy-checkbox';

interface IProps {
  isChecked: boolean;
  text?: string;
  disabled?: boolean;
  onChange: (isChecked: boolean) => void;
}

export const Checkbox = (props: IProps) => {
  const {styles, colors} = useCurrentTheme(createStyles);
  return (
    <BouncyCheckbox
      size={25}
      fillColor={colors.primary}
      unFillColor={colors.white}
      disableText={!props.text}
      innerIconStyle={{borderRadius:5}}
      text={props.text}
      iconStyle={styles.iconStyle}
      textStyle={styles.textStyle}
      isChecked={props.isChecked}
      onPress={(isChecked?: boolean) => {
        if (props.onChange) {
          let isCheckedValue: boolean = false;
          if (isChecked && isChecked === true) {
            isCheckedValue = true;
          }

          props.onChange(isCheckedValue);
        }
      }}
      style={{padding: 10}}
    />
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    iconStyle: {
      borderColor: theme.palette.primary,
      borderRadius: 5,
      borderWidth: 2,
    },
    textStyle: {
      ...theme.typography.normal.small,
      textDecorationLine: 'none',
      color: theme.palette.black,
    },
  });

  const colors = {
    primary: theme.palette.primary,
    secondary: theme.palette.secondary,
    white: theme.palette.white,
  };

  return {styles, colors};
};
