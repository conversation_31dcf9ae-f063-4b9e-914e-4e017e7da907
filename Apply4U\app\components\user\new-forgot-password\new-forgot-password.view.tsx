import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  HelperText,
} from 'react-native-paper';
import { a4uLogo } from '../../../assets';
import { ITheme, useCurrentTheme } from '../../../theme';

interface IProps {
  resetPassword: (email: string) => void;
  backToSignIn: () => void;
  isResetEmailSent: boolean;
}

export const NewForgotPasswordView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [touched, setTouched] = useState({ email: false });

  const {
    resetPassword,
    backToSignIn,
    isResetEmailSent
  } = props;

  // Email validation function
  const validateEmail = (text: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!text) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(text)) {
      setEmailError('Please enter a valid email address');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };

  const handleResetPassword = () => {
    Keyboard.dismiss();

    const isEmailValid = validateEmail(email);

    if (isEmailValid) {
      resetPassword(email);
    } else {
      setTouched({ email: true });
    }
  };

  if (isResetEmailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}>
          <View style={styles.mainContainer}>
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              keyboardShouldPersistTaps="handled">
              <View style={styles.contentContainer}>
                <View style={styles.logoContainer}>
                  <Image source={a4uLogo} style={styles.logo} resizeMode="contain" />
                </View>

                <Text style={styles.title}>Reset Email Sent!</Text>
                <Text style={styles.subtitle}>
                  We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
                </Text>
              </View>
            </ScrollView>

            <View style={styles.bottomContainer}>
              <Button
                mode="contained"
                onPress={backToSignIn}
                style={styles.resetButton}
                labelStyle={styles.resetButtonText}>
                Back to Sign In
              </Button>

              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Remember your password? </Text>
                <Text
                  style={styles.loginLink}
                  onPress={backToSignIn}>
                  Sign In Now
                </Text>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}>
        <View style={styles.mainContainer}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View style={styles.contentContainer}>
              <View style={styles.logoContainer}>
                <Image source={a4uLogo} style={styles.logo} resizeMode="contain" />
              </View>

              <Text style={styles.title}>Forgot Password?</Text>
              <Text style={styles.subtitle}>
                Enter your email address and we'll send you a link to reset your password.
              </Text>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Email"
                  placeholder="Enter Email"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (touched.email) {
                      validateEmail(text);
                    }
                  }}
                  style={styles.input}
                  outlineStyle={styles.inputOutline}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  activeOutlineColor="#0E1C5D"
                  error={!!emailError && touched.email}
                  onBlur={() => {
                    setTouched({ email: true });
                    validateEmail(email);
                  }}
                />
                {!!emailError && touched.email && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {emailError}
                  </HelperText>
                )}
              </View>
            </View>
          </ScrollView>

          <View style={styles.bottomContainer}>
            <Button
              mode="contained"
              onPress={handleResetPassword}
              style={styles.resetButton}
              labelStyle={styles.resetButtonText}>
              Reset Password
            </Button>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Remember your password? </Text>
              <Text
                style={styles.loginLink}
                onPress={backToSignIn}>
                Sign In Now
              </Text>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (_theme: ITheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'white',
    },
    mainContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    contentContainer: {
      width: '100%',
      paddingTop: 40,
    },
    logoContainer: {
      alignItems: 'center',
    },
    logo: {
      width: 160,
      height: 160,
    },
    title: {
      fontSize: 30,
      fontWeight: 'bold',
      color: '#0E1C5D',
      textAlign: 'center',
      marginBottom: 15,
      fontFamily: 'Poppins-Bold',
    },
    subtitle: {
      fontSize: 14,
      fontFamily: 'Poppins-Regular',
      color: '#666666',
      textAlign: 'center',
      marginBottom: 30,
      lineHeight: 20,
    },
    inputContainer: {
      marginBottom: 20,
    },
    input: {
      backgroundColor: 'white',
      height: 50,
    },
    inputOutline: {
      borderRadius: 5,
      borderColor: '#0E1C5D',
      borderWidth: 1,
    },
    bottomContainer: {
      width: '100%',
      paddingHorizontal: 20,
      paddingBottom: 30,
      marginTop: 20,
    },
    resetButton: {
      borderRadius: 5,
      height: 50,
      justifyContent: 'center',
      marginBottom: 15,
      backgroundColor: '#0E1C5D',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    resetButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      color: 'white',
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 10,
      gap: 5,
    },
    loginText: {
      color: '#333',
      fontFamily: 'Poppins-SemiBold',
      fontSize: 14,
    },
    loginLink: {
      color: '#00C851',
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      fontSize: 14,
      paddingBottom: 5,
      textDecorationLine: 'underline',
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
  });
};
