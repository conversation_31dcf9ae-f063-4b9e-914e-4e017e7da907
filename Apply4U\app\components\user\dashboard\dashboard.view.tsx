import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Pressable,
} from 'react-native';
import { IJobSearchResponse, IUserPackage, IUserStat } from '../../../interfaces';

import {
  ProfileCompletionCard,
  ActivityCard,
  SubscriptionBanner,
  NextJobBanner,
  Text,
  MainLayout,
  JobCardList,
} from '../../common';

interface IProps {
  topJobs: IJobSearchResponse[];
  userPackage?: IUserPackage;
  userStat?: IUserStat;
  profileCompletedPerent: number;
  totalShortlistedJobs: number;
  goToTopJobDetail: (jobId: number) => void;
  onUpgradePackageClick?: () => void;
  onNextJobLearnMoreClick?: () => void;
  onViewProfileClick: () => void;
  onViewAllTopJobsClick: () => void;
  favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
  onSwipeRight: (jobId: number) => void;
  handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply: boolean) => void;
}
const DashboardView = (props: IProps) => {
  const FooterComponent = () => (<React.Fragment><SubscriptionBanner
    userPackage={props.userPackage}
    onUpgradeClick={props.onUpgradePackageClick}
  />

    <NextJobBanner onLearnMoreClick={props.onNextJobLearnMoreClick} /></React.Fragment>);
  return (
    <MainLayout>
      <ProfileCompletionCard
        percentCompleted={props.profileCompletedPerent}
        onViewProfileClick={props.onViewProfileClick}
      />
      <View style={styles.activityContainer}>
        <View style={styles.headerWrapper}>
          <Text styles={styles.activityHeadingStyle} text={'My Activity'} />
        </View>
      </View>

      <View>
        <ScrollView style={{ marginLeft: 10 }} showsHorizontalScrollIndicator={false} horizontal={true}>
          <ActivityCard
            activityCount={
              props.userStat?.ProfileViews ? props.userStat.ProfileViews : 0
            }
            activityTitle={'Views'}
          />

          <ActivityCard
            activityCount={
              props.userStat?.JobApplied ? props.userStat.JobApplied : 0
            }
            activityTitle={'Applied For'}
          />

          <ActivityCard
            activityCount={props.userStat?.CvViews ? props.userStat.CvViews : 0}
            activityTitle={'CV Views'}
          />

          <ActivityCard
            activityCount={
              props.totalShortlistedJobs
            }
            activityTitle={'Shortlist'}
          />
        </ScrollView>
      </View>
      <View style={styles.topJobsWrapper}>
        {!!props.topJobs && props.topJobs.length > 0 && <View style={styles.twoColumn}>
          <Text styles={styles.topJobsHeadingStyle} text={'Top Jobs'} />
        </View>}
        {!!props.topJobs && props.topJobs.length > 0 && <View style={styles.twoColumn}>
          <Pressable onPress={props.onViewAllTopJobsClick}>
            <Text styles={styles.linkStyle} text={'View All'} />
          </Pressable>
        </View>}</View>
      {
        <JobCardList
          jobResults={props.topJobs}
          isMoreResultsExists={false}
          isSwipeLeftEnabled={true}
          isSwipeRightEnabled={true}
          swipeRightText='Hide'
          isSwipeable={true}
          showFavIcon={true}
          showShareIcon={true}
          goToJobDetail={props.goToTopJobDetail}
          handleEasyApply={props.handleEasyApply}
          favJobsAddOrRemove={props.favJobsAddOrRemove}
          loadMore={() => { }}
          onSwipeRight={props.onSwipeRight}
          ListFooterComponent={FooterComponent}
        />}
    </MainLayout>
  );
};

const styles = StyleSheet.create({
  viewPort: {
    height: '100%',
    width: '100%',
  },
  container: {
    flexDirection: 'column', // inner items will be added vertically
    backgroundColor: '#edf1fc',
  },

  activityContainer: {
    width: '100%',
    flexWrap: 'wrap',
    paddingTop: 0,
    paddingRight: 5,
    paddingBottom: 0,
    paddingLeft: 5,
  },
  headerWrapper: {
    width: '100%',
  },
  activityHeadingStyle: {
    marginLeft: 5,
    marginBottom: 5,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    color: '#333',
  },
  label: {
    fontSize: 13,
    color: '#fff',
    letterSpacing: 0.5,
  },

  topJobsWrapper: {
    width: '100%',
    paddingTop: 0,
    paddingRight: 5,
    paddingBottom: 0,
    paddingLeft: 5,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  twoColumn: {
    width: '50%',
    padding: 5,
  },
  topJobsHeadingStyle: {
    marginLeft: 5,
    fontWeight: 'bold',
    letterSpacing: 0.5,
    color: '#333',
  },
  linkStyle: {
    color: '#4f40d1',
    fontWeight: 'bold',
    letterSpacing: 0.5,
    textAlign: 'right',
  },
});

export { DashboardView };
