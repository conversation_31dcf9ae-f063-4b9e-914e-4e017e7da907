import React from 'react';
import {StyleSheet, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import Slider from '@react-native-community/slider';
import {Text} from '.';
import { background_1 } from '../../assets';

interface IProps {
  name: string;
  minimumValue: number;
  maximumValue: number;
  step: number;
  value: number;
  onValueChange: (value: number, name: string) => void;
}
export const MilesAwaySlider = (props: IProps) => {
  const {styles, colors} = useCurrentTheme(createStyles);
  const [sliderValue, setSliderValue] = React.useState<number>(props.value);

  return (
    <View style={styles.container}>
      <View style={styles.contentContainer}>
        <View style={styles.titleContainer}>
          <Text styles={styles.textStyle} text={'Distance (Miles)'} />
        </View>
        <View style={styles.sliderContainer}>
          <Slider
            style={styles.slider}
            minimumValue={props.minimumValue}
            maximumValue={props.maximumValue}
            step={props.step}
            minimumTrackTintColor={colors.primary}
            maximumTrackTintColor={colors.secondary}
            thumbTintColor={colors.primary}
            tapToSeek={true}
            value={sliderValue}
            onSlidingComplete={(value: number) => {
              setSliderValue(value);
            }}
            onValueChange={(value: number) => {
              props.onValueChange(value, props.name);
            }}
          />
        </View>
        <View style={styles.valueContainer}>
          <Text styles={styles.valueTextStyle} text={`${props.value} miles`} />
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      height: 110,
    },
    contentContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    titleContainer: {
      flex: 1,
      flexDirection: 'column',
      paddingLeft: theme.spacing(5),
      justifyContent: 'flex-end',
    },
    sliderContainer: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-end',
    },
    valueContainer: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-start',
    },
    slider: {
      width: '100%',
    },
    textStyle: {
      ...theme.typography.normal.small,
      color: theme.palette.black,
    },
    valueTextStyle: {
      ...theme.typography.normal.small,
      color: theme.palette.black,
      alignSelf: 'flex-end',
      paddingRight: theme.spacing(7),
    },
  });

  const colors = {
    primary: theme.palette.primary,
    secondary: theme.palette.secondary,
  };

  return {styles, colors};
};
