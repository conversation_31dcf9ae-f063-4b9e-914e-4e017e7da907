import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  HelperText,
} from 'react-native-paper';
import { a4uLogo } from '../../../assets';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { ITheme, useCurrentTheme } from '../../../theme';

export const CreateAccountView = () => {
  const styles = useCurrentTheme(createStyles);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [touched, setTouched] = useState({
    email: false,
    password: false,
    confirmPassword: false
  });

  const validateEmail = (text: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!text) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(text)) {
      setEmailError('Please enter a valid email address');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };

  const validatePassword = (text: string): boolean => {
    if (!text) {
      setPasswordError('Password is required');
      return false;
    } else if (text.length < 3) {
      setPasswordError('Password must be at least 3 characters');
      return false;
    } else {
      setPasswordError('');
      return true;
    }
  };

  const validateConfirmPassword = (text: string): boolean => {
    if (!text) {
      setConfirmPasswordError('Please confirm your password');
      return false;
    } else if (text !== password) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    } else {
      setConfirmPasswordError('');
      return true;
    }
  };

  const handleSignUp = () => {
    Keyboard.dismiss();
    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);
    const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);
    setTouched({
      email: true,
      password: true,
      confirmPassword: true
    });

    if (isEmailValid && isPasswordValid && isConfirmPasswordValid) {
      navigate(screens.OtpVerification, { email });
    }
  };

  const goToLogin = () => {
    navigate(screens.Login);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}>
        <View style={styles.mainContainer}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View style={styles.contentContainer}>
              <View style={styles.logoContainer}>
                <Image
                  source={a4uLogo}
                  style={styles.logo}
                  resizeMode="contain"
                />
              </View>

              <Text style={styles.title}>Create Your Account</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Email"
                  placeholder="Enter Email"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (touched.email) {
                      validateEmail(text);
                    }
                  }}
                  style={styles.input}
                  outlineStyle={[
                    styles.inputOutline,
                    !!emailError && touched.email && styles.errorInputOutline
                  ]}
                  activeOutlineColor="#0E1C5D"
                  outlineColor={!!emailError && touched.email ? "#FF3B30" : "#0E1C5D"}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={!!emailError && touched.email}
                  onBlur={() => {
                    setTouched({ ...touched, email: true });
                    validateEmail(email);
                  }}
                />
                {!!emailError && touched.email && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {emailError}
                  </HelperText>
                )}
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Password"
                  placeholder="Enter Password"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (touched.password) {
                      validatePassword(text);
                      if (touched.confirmPassword) {
                        validateConfirmPassword(confirmPassword);
                      }
                    }
                  }}
                  style={styles.input}
                  outlineStyle={[
                    styles.inputOutline,
                    !!passwordError && touched.password && styles.errorInputOutline
                  ]}
                  activeOutlineColor="#0E1C5D"
                  outlineColor={!!passwordError && touched.password ? "#FF3B30" : "#0E1C5D"}
                  secureTextEntry={!passwordVisible}
                  error={!!passwordError && touched.password}
                  onBlur={() => {
                    setTouched({ ...touched, password: true });
                    validatePassword(password);
                  }}
                  right={
                    <TextInput.Icon
                      icon={passwordVisible ? "eye-off" : "eye"}
                      onPress={() => setPasswordVisible(!passwordVisible)}
                    />
                  }
                />
                {!!passwordError && touched.password && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {passwordError}
                  </HelperText>
                )}
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Confirm Password"
                  placeholder="Confirm Password"
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    if (touched.confirmPassword) {
                      validateConfirmPassword(text);
                    }
                  }}
                  style={styles.input}
                  outlineStyle={[
                    styles.inputOutline,
                    !!confirmPasswordError && touched.confirmPassword && styles.errorInputOutline
                  ]}
                  activeOutlineColor="#0E1C5D"
                  outlineColor={!!confirmPasswordError && touched.confirmPassword ? "#FF3B30" : "#0E1C5D"}
                  secureTextEntry={!confirmPasswordVisible}
                  error={!!confirmPasswordError && touched.confirmPassword}
                  onBlur={() => {
                    setTouched({ ...touched, confirmPassword: true });
                    validateConfirmPassword(confirmPassword);
                  }}
                  right={
                    <TextInput.Icon
                      icon={confirmPasswordVisible ? "eye-off" : "eye"}
                      onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
                    />
                  }
                />
                {!!confirmPasswordError && touched.confirmPassword && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {confirmPasswordError}
                  </HelperText>
                )}
              </View>
            </View>
          </ScrollView>

          {/*/////////////// Bottom section with buttons///////////////// */}
          <View style={styles.bottomContainer}>
            <Button
              mode="contained"
              onPress={() => {
                Keyboard.dismiss();
                handleSignUp();
              }}
              style={styles.signUpButton}
              labelStyle={styles.signUpButtonText}>
              Next
            </Button>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Already a member? </Text>
              <Text
                style={styles.loginLink}
                onPress={goToLogin}>
                Sign In Now
              </Text>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (_theme: ITheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'white',
    },
    mainContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    logoContainer: {
      alignItems: 'center',
    },
    logo: {
      width: 160,
      height: 160,
    },
    contentContainer: {
      width: '100%',
      paddingTop: 40,
    },
    bottomContainer: {
      width: '100%',
      paddingHorizontal: 20,
      paddingBottom: 30,
      marginTop: 20,
    },
    title: {
      fontSize: 30,
      fontWeight: 'bold',
      color: '#0E1C5D',
      textAlign: 'center',
      marginBottom: 30,
      fontFamily: 'Poppins-Bold',
    },
    inputContainer: {
      marginBottom: 20,
    },
    input: {
      backgroundColor: 'white',
      height: 50,
    },
    inputOutline: {
      borderRadius: 5,
      borderColor: '#0E1C5D',
      borderWidth: 1,
    },
    errorInputOutline: {
      borderRadius: 5,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },


    signUpButton: {
      borderRadius: 5,
      height: 50,
      justifyContent: 'center',
      marginBottom: 15,
      backgroundColor: '#0E1C5D',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    signUpButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      color: 'white',
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 10,
      gap:5
    },
    loginText: {
      color: '#333',
      fontFamily: 'Poppins-SemiBold',
      fontSize: 14,
    },
    loginLink: {
      color: '#00C851',
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      fontSize: 14,
      paddingBottom: 5,
      textDecorationLine: 'underline',
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
  });
};
