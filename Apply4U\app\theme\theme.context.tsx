import React from 'react';
import {LightTheme} from './theme-variant/light.theme';
import {ITheme} from './theme.interface';

interface IProvidedValue {
  theme: ITheme;
  setTheme: (theme: ITheme) => void;
}

interface IProps {
  initial: ITheme;
  children?: React.ReactNode;
}

const Context = React.createContext<IProvidedValue>({
  theme: LightTheme,
  setTheme: () => {},
});

export const ThemeProvider = React.memo<IProps>(props => {
  const [theme, setTheme] = React.useState<ITheme>(props.initial);

  const setThemeCallback = React.useCallback((newTheme: ITheme) => {
    setTheme((currentTheme: ITheme) => {
      if (currentTheme.id === newTheme.id) {
        return currentTheme;
      }

      return newTheme;
    });
  }, []);

  const MemoizedValue = React.useMemo(() => {
    const value: IProvidedValue = {
      theme,
      setTheme: setThemeCallback,
    };
    return value;
  }, [theme, setThemeCallback]);

  return (
    <Context.Provider value={MemoizedValue}>{props.children}</Context.Provider>
  );
});

export const useTheme = () => React.useContext(Context);
