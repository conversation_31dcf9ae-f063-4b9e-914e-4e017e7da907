import { Platform } from 'react-native';
import { ITheme, ITypographyMain, IButtons } from './theme.interface';
const fontFamily: string = [
  'Arial',
  '-apple-system',
  'BlinkMacSystemFont',
  '"Segoe UI"',
  'Roboto',
  '"Helvetica Neue"',
  'sans-serif',
  '"Apple Color Emoji"',
  '"Segoe UI Emoji"',
  '"Segoe UI Symbol"',
].join(',');

const createTheme = (
  baseSpacing: number,
  baseBorderRadius: number,
  themeId: string,
): ITheme => {
  const spacing = (factor: number) => factor * baseSpacing;
  const borderRadius = (factor: number) => factor * baseBorderRadius;
  const zIndex = (index: number) => {
    return Platform.select({
      ios: { zIndex: index },
      android: { elevation: index }
    });
  };
  const palette = {
    primary: '#0E1C5D',
    secondary: '#479ffd',
    info: '#2196f3',
    success: '#4caf50',
    error: '#f44336',
    warning: '#ff9800',
    white: '#FFFFFF',
    gray: '#808080',
    lightGray: '#cccccc',
    blue: '#4f40d1',
    black: '#000000',
  };

  const typography: ITypographyMain = {
    bold: {
      extraSmall: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 12,
        color: palette.white,
      },

      small: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 14,
        color: palette.white,
      },
      medium: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 16,
        color: palette.white,
      },

      large: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: 'bold',
        fontSize: 18,
        color: palette.white,
      },
    },
    normal: {
      extraSmall: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 11,
        color: palette.white,
      },

      small: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 14,
        color: palette.white,
      },
      medium: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 16,
        color: palette.white,
      },

      large: {
        fontFamily: fontFamily,
        fontStyle: 'normal',
        fontWeight: '400',
        fontSize: 18,
        color: palette.white,
      },
    },
  };

  const buttons: IButtons = {
    primary: {
      height: 40,
      marginTop: spacing(5),
      marginLeft: spacing(0),
      marginRight: spacing(0),
      marginBottom: spacing(0),
      borderWidth: 0,
      alignItems: 'center',
      justifyContent: 'center',
      color: palette.primary,
      borderRadius: borderRadius(15),
      borderColor: palette.primary,
      backgroundColor: palette.primary,
      ...typography.bold.medium,
    },
    secondary: {
      height: 40,
      marginTop: spacing(5),
      marginLeft: spacing(1),
      marginRight: spacing(1),
      marginBottom: spacing(1),
      borderWidth: 0,
      alignItems: 'center',
      justifyContent: 'center',
      color: palette.secondary,
      borderRadius: borderRadius(15),
      borderColor: palette.secondary,
      backgroundColor: palette.secondary,
      ...typography.bold.medium,
    },
    info: {
      height: 40,
      marginTop: spacing(5),
      marginLeft: spacing(1),
      marginRight: spacing(1),
      marginBottom: spacing(1),
      borderWidth: 0,
      alignItems: 'center',
      justifyContent: 'center',
      color: palette.info,
      borderRadius: borderRadius(15),
      borderColor: palette.info,
      backgroundColor: palette.info,
      ...typography.bold.medium,
    },
    warning: {
      height: 40,
      marginTop: spacing(5),
      marginLeft: spacing(1),
      marginRight: spacing(1),
      marginBottom: spacing(1),
      borderWidth: 0,
      alignItems: 'center',
      justifyContent: 'center',
      color: palette.warning,
      borderRadius: borderRadius(15),
      borderColor: palette.warning,
      backgroundColor: palette.warning,
      ...typography.bold.medium,
    },
    danger: {
      height: 40,
      marginTop: spacing(5),
      marginLeft: spacing(1),
      marginRight: spacing(1),
      marginBottom: spacing(1),
      borderWidth: 0,
      alignItems: 'center',
      justifyContent: 'center',
      color: palette.error,
      borderRadius: borderRadius(15),
      borderColor: palette.error,
      backgroundColor: palette.error,
      ...typography.bold.medium,
    },
  };

  const backgroundPalette = {
    primary: '#edf1fc',
    secondary: '#edf1fc',
  };

  let theme: ITheme = {
    id: themeId,
    palette,
    spacing,
    borderRadius,
    typography,
    zIndex,
    buttons,
    backgroundPalette,
  };

  return theme;
};

export { createTheme };
