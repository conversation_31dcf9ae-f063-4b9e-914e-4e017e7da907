import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';

interface IProps {
  activityCount: number;
  activityTitle: string;
}

export const ActivityCard = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  return (
    <View style={styles.activityBox}>
      <View style={styles.inner}>
        <Text style={styles.number}>{props.activityCount}</Text>
        <Text style={styles.label}>{props.activityTitle}</Text>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    activityBox: {
      width: 110,
      height: 100,
      padding: theme.spacing(3),
    },
    inner: {
      flex: 1,
      borderRadius: theme.borderRadius(10),
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.palette.primary,
    },
    number: {
      ...theme.typography.bold.large,
      fontSize:24,
      color: '#fff',
    },
    label: {
      ...theme.typography.normal.extraSmall,
      color: '#fff',
      marginTop:theme.spacing(4)
    },
  });
  return styles;
};
