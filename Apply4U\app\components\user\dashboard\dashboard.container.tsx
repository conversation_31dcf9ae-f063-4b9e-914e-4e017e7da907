import React from 'react';
import {Linking} from 'react-native';
import {connect} from 'react-redux';
import {blogsWebUrl, editProfileWebUrl, screens, upgradePackageWebUrl} from '../../../app.constant';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import {searchApi, userApi} from '../../../http';
import {
  IAutoSearch,
  IBrowserParam,
  IJobDetailParam,
  IJobSearchResponse,
  ILoadingIndicator,
  ISearchParam,
  IShareJobModalProps,
  IUserDetail,
  IUserPackage,
  IUserStat,
} from '../../../interfaces';
import {IApplicationState} from '../../../redux';
import { addToFavJobsAction, hideJobAction, hideLoading, removeFromFavJobsAction, setSearchParamAction, showLoading } from '../../../redux/actions';
import { IAction } from '../../../redux/actions/action.interface';
import {applyNow, easyApplyWithDefaultResume, encodeBase64String, hasValues, navigate} from '../../../utility';
import {DashboardView} from './dashboard.view';

interface IProps {
  loginUser: IUserDetail | null;
  hiddenJobs:number[],
  favJobs: number[],
  setSearchParam: (
    payload: ISearchParam | null,
  ) => IAction<ISearchParam>;
  addToFav: (payload: number) => IAction<number>;
  removeFromFav: (payload: number) => IAction<number>;
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
) => IAction<ILoadingIndicator>;
hideLoading: () => IAction<ILoadingIndicator>;
hideJob: (payload: number) => IAction<number>;
}
const DashboardContainer = (props: IProps) => {

  const [totalShortlistedJobs,setTotalShortlistedJobs] = React.useState<number>(0);

  const getTopJobs = () => {
          userApi.getDefaultAutoSearch(props.loginUser?.Id??0)
          .then((autoSearch:IAutoSearch) => {
              if(!!autoSearch){
                if(!!autoSearch && !!autoSearch.Id && autoSearch.Id > 0){
                  const params:ISearchParam = {};
                  params.applyFilters = autoSearch.IsFilterApplyed;
                  params.keywords = autoSearch.KeyWords;
                  params.locationId = autoSearch.LocationId;
                  params.locationText = autoSearch.LocationText;
                  params.radius = autoSearch.Radius;
                  params.salaryFrom = autoSearch.SalaryFrom;
                  params.salaryPer = autoSearch.SalaryDuration;
                  params.salaryTo = autoSearch.SalaryTo;
                  params.searchWithinDays = autoSearch.SearchWithInLast;
                  params.sector = {...autoSearch.Sector};
          
                  topJobsApiCall({...params});
              }else{
                topJobsApiCall({keywords:'',locationText:''});
              }
            }
          })
          .catch((e) => {
            topJobsApiCall({keywords:'',locationText:''});
          })
          .finally(() => {
          });
  };

  const topJobsApiCall = (searchParam:ISearchParam) => {
    searchApi
    .getJobResults(searchParam, props.hiddenJobs,props.loginUser?.Id, 1, 10)
    .then(topJobsResult => {
      if (topJobsResult && hasValues(topJobsResult.Response)) {
        markFavAndSetResults([...topJobsResult.Response]);
      }
    })
    .catch(error => {});
  }

  const getUserPackage = () => {
    userApi
      .getUserPackage(
        props?.loginUser?.Id ?? 0,
        props?.loginUser?.UserProfile?.IsCandidate ?? true,
      )
      .then(result => {
        setUserPackage({...result});
      })
      .catch(e => {});
  };

  const getUserStat = () => {
    userApi
      .getUserStat(props?.loginUser?.Id ?? 0)
      .then(result => {
        setUserStat({...result});
      })
      .catch(e => {});
  };

  const getUserProfileCompletedPercentage = () => {
    userApi
      .getUserProfileCompletionPercentage(props?.loginUser?.Id ?? 0)
      .then(result => {
        setUserProfileCompletedPercentage(result);
      })
      .catch(e => {});
  };

  const favJobsAddOrRemove = (jobId: number, isFav: boolean) => {
    if (isFav == true) {
      props.removeFromFav(jobId);
    } else {
      props.addToFav(jobId);
    }
  };

  const markFavAndSetResults = (resultsToMark:IJobSearchResponse[]) => {
    if(!!resultsToMark && resultsToMark.length > 0){
      if (!!props.favJobs && props.favJobs.length > 0){
        const resultsWithFavMark = resultsToMark.map(m => {
          m.IsFav = props.favJobs.indexOf(m.Id) >= 0;
          return m;
        });
  
        setTopJobs([...resultsWithFavMark]);
      }else{
        const resultsWithNoFavMark = resultsToMark.map(m => {
          m.IsFav = false;
          return m;
        });
  
        setTopJobs([...resultsWithNoFavMark]);
      }

    }else{
      setTopJobs([...resultsToMark]);
    }
  }

  React.useEffect(() => {
    markFavAndSetResults([...topJobs]);
    setTotalShortlistedJobs(!!props.favJobs ? props.favJobs.length : 0);
  }, [props.favJobs]);

  React.useEffect(() => {
    if(!!props.hiddenJobs && props.hiddenJobs.length > 0){
        setTopJobs(oldResults => {
          if(!!oldResults && oldResults.length <= 6){
            getTopJobs();
            return oldResults.filter(m => props.hiddenJobs.indexOf(m.Id) < 0);
          }
          else
          {
            return oldResults.filter(m => props.hiddenJobs.indexOf(m.Id) < 0);
          }
        });
    }
  },[props.hiddenJobs]);

  const onUpgradePackageClick = () => {
    const encodedLoginName = encodeBase64String(
      props.loginUser?.LoginName ?? '',
    );
    const encodedLoginPassword = encodeBase64String(
      props.loginUser?.Password ?? '',
    );

    const url = `${upgradePackageWebUrl}&authu=${encodedLoginName}&authkey=${encodedLoginPassword}`;
    navigate<IBrowserParam>(screens.Browser,{URL:url});
    //Linking.openURL(url).catch(err => {});
  };

  const onNextJobLearnMoreClick = () => {
    const encodedLoginName = encodeBase64String(
      props.loginUser?.LoginName ?? '',
    );
    const encodedLoginPassword = encodeBase64String(
      props.loginUser?.Password ?? '',
    );

    const url = `${blogsWebUrl}?authu=${encodedLoginName}&authkey=${encodedLoginPassword}`;
    //Linking.openURL(url).catch(err => {});
    navigate<IBrowserParam>(screens.Browser,{URL:url});
  };

  const onViewProfileClick = () => {
    const encodedLoginName = encodeBase64String(
      props.loginUser?.LoginName ?? '',
    );
    const encodedLoginPassword = encodeBase64String(
      props.loginUser?.Password ?? '',
    );

    const url = `${editProfileWebUrl}${props.loginUser?.Id}?authu=${encodedLoginName}&authkey=${encodedLoginPassword}`;
    //Linking.openURL(url).catch(err => {});
    navigate<IBrowserParam>(screens.Browser,{URL:url});
  };

  const onViewAllTopJobsClick = () => {
    navigate(screens.MyJobs);
  };

  const goToTopJobDetail = (jobId: number) => {
    navigate<IJobDetailParam>(screens.JobDetail, {jobId});
  };

  const handleEasyApply = (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => {
    if (jobId > 0 && jobSourceId > 0 && !!jobTitle) {
      applyNow(jobId, jobSourceId, jobTitle, applicationUrl, isEasyApply ? internalEasyApply : undefined);
    } else {
      applyNow(undefined, 0, '', applicationUrl, isEasyApply ? internalEasyApply : undefined);
    }
  };

  const internalEasyApply = (jobId:number) => {
      props.showLoading();
    easyApplyWithDefaultResume(props?.loginUser?.Id ?? 0,jobId,(errorMessage:string) =>{
        props.hideLoading();
        showErrorMessage(errorMessage);
    },(successMessage:string) => {
        setTopJobs(oldResults => {
            return oldResults.map(m => {
                if(m.Id == jobId){
                    m.IsApplied = true;
                }

                return m;
            });
        });
        props.hideLoading();
        showSuccessMessage(successMessage);
    });
  }

  const handleHideJob = (jobId: number) => {
    if (jobId > 0) {
      props.hideJob(jobId);
    }
  };

  const [topJobs, setTopJobs] = React.useState<IJobSearchResponse[]>([]);
  const [userPackage, setUserPackage] = React.useState<IUserPackage>({});
  const [userStat, setUserStat] = React.useState<IUserStat>({});
  const [userProfileCompletedPercentage, setUserProfileCompletedPercentage] =
    React.useState<number>(0);

  React.useEffect(() => {
    getTopJobs();
    getUserPackage();
    getUserStat();
    getUserProfileCompletedPercentage();
  }, [props?.loginUser?.Id]);
  return (
    <DashboardView
      topJobs={topJobs}
      favJobsAddOrRemove={favJobsAddOrRemove}
      goToTopJobDetail={goToTopJobDetail}
      userPackage={userPackage}
      onUpgradePackageClick={onUpgradePackageClick}
      onNextJobLearnMoreClick={onNextJobLearnMoreClick}
      userStat={userStat}
      profileCompletedPerent={userProfileCompletedPercentage}
      onViewProfileClick={onViewProfileClick}
      onViewAllTopJobsClick={onViewAllTopJobsClick}
      totalShortlistedJobs={totalShortlistedJobs}
      handleEasyApply={handleEasyApply}
      onSwipeRight={handleHideJob}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser: state.loginUser,
    hiddenJobs:state.hiddenJobs,
    favJobs: state.favJobs
  };
};

const mapDispatchToProps = {
  setSearchParam: setSearchParamAction,
  addToFav: addToFavJobsAction,
  removeFromFav: removeFromFavJobsAction,
  showLoading: showLoading,
  hideLoading: hideLoading,
  hideJob:hideJobAction,
};

const connectedDashboardContainer =
  connect(mapStateToProps,mapDispatchToProps)(DashboardContainer);
export {connectedDashboardContainer as DashboardContainer};
