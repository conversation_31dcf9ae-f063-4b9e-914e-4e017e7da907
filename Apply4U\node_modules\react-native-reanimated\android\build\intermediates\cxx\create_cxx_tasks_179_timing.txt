# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-module-model 10ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 166ms
  [gap of 37ms]
create_cxx_tasks completed in 204ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 33ms]
    create-variant-model 19ms
    create-ARMEABI_V7A-model 10ms
    [gap of 24ms]
  create-initial-cxx-model completed in 110ms
  [gap of 11ms]
create_cxx_tasks completed in 121ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 13ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 152ms
  [gap of 12ms]
create_cxx_tasks completed in 165ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 51ms]
    create-variant-model 29ms
    create-ARM64_V8A-model 27ms
    [gap of 16ms]
  create-initial-cxx-model completed in 131ms
create_cxx_tasks completed in 140ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 47ms]
    create-variant-model 18ms
    create-ARMEABI_V7A-model 11ms
    [gap of 21ms]
  create-initial-cxx-model completed in 97ms
create_cxx_tasks completed in 105ms

