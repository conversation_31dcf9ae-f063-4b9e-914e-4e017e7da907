import moment from 'moment';
export const defaultDateTimeFormat: string = 'DD-MM-YYYY HH:mm:ss';
export const defaultDateFormat: string = 'DD-MM-YYYY';
export const formatedDate = (
  date: string | Date,
  format: string = defaultDateFormat,
) => moment(date).format(format);
export const formatedDateTime = (
  date: string | Date,
  format: string = defaultDateTimeFormat,
) => moment(date).format(format);

export const timeAgo = (date:string | Date) =>{
  return (moment(date).fromNow());
}
