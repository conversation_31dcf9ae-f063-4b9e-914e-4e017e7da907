{"logs": [{"outputFile": "com.apply4u.app-mergeDebugResources-36:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19ec269da49c6c76309942cfa013a60d\\transformed\\jetified-play-services-base-17.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,544,673,779,999,1130,1278,1413,1664,1771,1967,2097,2321,2504,2597,2692", "endColumns": "103,194,128,105,219,130,147,134,250,106,195,129,223,182,92,94,109", "endOffsets": "348,543,672,778,998,1129,1277,1412,1663,1770,1966,2096,2320,2503,2596,2691,2801"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4179,4287,4482,4615,4725,4945,5080,5232,5590,5841,5952,6148,6282,6506,6693,6790,6889", "endColumns": "107,194,132,109,219,134,151,138,250,110,195,133,223,186,96,98,113", "endOffsets": "4282,4477,4610,4720,4940,5075,5227,5366,5836,5947,6143,6277,6501,6688,6785,6884,6998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3627ca143232ec2ceb6e319eb18971d\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,12175", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,12253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,243,341,424,499,569,652,721,788,868,950,1037,1132,1204,1295,1381,1457,1540,1622,1697,1776,1851,1941,2014,2097,2173", "endColumns": "69,117,97,82,74,69,82,68,66,79,81,86,94,71,90,85,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,238,336,419,494,564,647,716,783,863,945,1032,1127,1199,1290,1376,1452,1535,1617,1692,1771,1846,1936,2009,2092,2168,2255"}, "to": {"startLines": "33,39,40,44,65,67,68,70,84,85,86,124,125,126,127,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3059,3573,3691,4096,7163,7319,7389,7535,8596,8663,8743,11830,11917,12012,12084,12258,12344,12420,12503,12585,12660,12739,12814,13005,13078,13161,13237", "endColumns": "69,117,97,82,74,69,82,68,66,79,81,86,94,71,90,85,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3124,3686,3784,4174,7233,7384,7467,7599,8658,8738,8820,11912,12007,12079,12170,12339,12415,12498,12580,12655,12734,12809,12899,13073,13156,13232,13319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4343b2ae69420088cd050005b2aafd9c\\transformed\\jetified-play-services-basement-17.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "218", "endOffsets": "465"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5371", "endColumns": "218", "endOffsets": "5585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13659b94c66de92d98d7f8af61234b95\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "12904", "endColumns": "100", "endOffsets": "13000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3a8a24834f295d8078f18b8298041f7\\transformed\\material-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2888,2944,3000,3066,3145,3227,3315,3389,3466,3536,3615,3699,3783,3875,3975,4049,4130,4232,4285,4352,4445,4534,4596,4660,4723,4836,4929,5033,5127,5187,5247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2883,2939,2995,3061,3140,3222,3310,3384,3461,3531,3610,3694,3778,3870,3970,4044,4125,4227,4280,4347,4440,4529,4591,4655,4718,4831,4924,5028,5122,5182,5242,5325"}, "to": {"startLines": "2,34,35,36,37,38,41,42,43,63,64,66,69,71,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3129,3210,3289,3376,3477,3789,3893,4015,7003,7068,7238,7472,7604,7693,7757,7826,7889,7963,8027,8083,8201,8259,8321,8377,8457,8825,8914,8996,9137,9218,9298,9388,9444,9500,9566,9645,9727,9815,9889,9966,10036,10115,10199,10283,10375,10475,10549,10630,10732,10785,10852,10945,11034,11096,11160,11223,11336,11429,11533,11627,11687,11747", "endLines": "5,34,35,36,37,38,41,42,43,63,64,66,69,71,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,89,55,55,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,59,59,82", "endOffsets": "323,3205,3284,3371,3472,3568,3888,4010,4091,7063,7158,7314,7530,7688,7752,7821,7884,7958,8022,8078,8196,8254,8316,8372,8452,8591,8909,8991,9132,9213,9293,9383,9439,9495,9561,9640,9722,9810,9884,9961,10031,10110,10194,10278,10370,10470,10544,10625,10727,10780,10847,10940,11029,11091,11155,11218,11331,11424,11528,11622,11682,11742,11825"}}]}]}