import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  TouchableOpacity,
  Modal,
  FlatList,
} from 'react-native';
import { TextInput, Text, TouchableRipple, HelperText } from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent,A4UHuma } from '../../common';
import { a4uHuma } from '../../../assets';

const radiusOptions = [
  { label: '5 Miles', value: '5 Miles' },
  { label: '10 Miles', value: '10 Miles' },
  { label: '20 Miles', value: '20 Miles' },
  { label: '50 Miles', value: '50 Miles' }
];

export const NewRegisterStepFourView = () => {
  const styles = useCurrentTheme(createStyles);
  const [location, setLocation] = useState('London');
  const [radius, setRadius] = useState('20 Miles');
  const [showDropdown, setShowDropdown] = useState(false);
  const [locationError, setLocationError] = useState('');
  const [radiusError, setRadiusError] = useState('');

  const [touched, setTouched] = useState({
    location: false,
    radius: false,
  });

  const validateLocation = (text: string): boolean => {
    if (!text.trim()) {
      setLocationError('Location is required');
      return false;
    } else {
      setLocationError('');
      return true;
    }
  };

  const validateRadius = (text: string): boolean => {
    if (!text.trim()) {
      setRadiusError('Radius is required');
      return false;
    } else {
      setRadiusError('');
      return true;
    }
  };

  const handleContinue = () => {
    Keyboard.dismiss();

    const isLocationValid = validateLocation(location);
    const isRadiusValid = validateRadius(radius);

    setTouched({
      location: true,
      radius: true,
    });

    if (isLocationValid && isRadiusValid) {
      navigate(screens.NewRegisterStepFive);
    }
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepFive);
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterStepThree);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  return (
    <RegistrationStepComponent
      title="Location Preferences"
      currentStep={4}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <A4UHuma
        image={a4uHuma}
        text="I have suggested the following job titles for you based on your CV, to help you with your job search. You may add or remove them to better suit your preferences"
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />
      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Desired Location"
          value={location}
          onChangeText={(text) => {
            setLocation(text);
            if (touched.location) {
              validateLocation(text);
            }
          }}
          placeholder="Enter location"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!locationError && touched.location && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!locationError && touched.location ? "#FF3B30" : "#39608F"}
          error={!!locationError && touched.location}
          onBlur={() => {
            setTouched({ ...touched, location: true });
            validateLocation(location);
          }}
        />
        {!!locationError && touched.location && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {locationError}
          </HelperText>
        )}
      </View>

      <View style={styles.inputGroup}>
        <View>
          <TextInput
            mode="outlined"
            label="Radius"
            value={radius}
            placeholder="Select radius"
            style={styles.input}
            outlineStyle={[
              styles.inputOutline,
              !!radiusError && touched.radius && styles.errorInputOutline
            ]}
            right={<TextInput.Icon icon="chevron-down" onPress={() => setShowDropdown(true)} />}
            activeOutlineColor="#0E1C5D"
            outlineColor={!!radiusError && touched.radius ? "#FF3B30" : "#39608F"}
            error={!!radiusError && touched.radius}
            editable={false}
            onPressIn={() => {
              setShowDropdown(true);
              setTouched({ ...touched, radius: true });
              validateRadius(radius);
            }}
          />
          {!!radiusError && touched.radius && (
            <HelperText type="error" visible={true} style={styles.helperText}>
              {radiusError}
            </HelperText>
          )}
        </View>

        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowDropdown(false)}
          >
            <View style={styles.dropdownContainer}>
              <FlatList
                data={radiusOptions}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                  <TouchableRipple
                    style={styles.dropdownItem}
                    onPress={() => {
                      setRadius(item.value);
                      setShowDropdown(false);
                    }}
                    rippleColor="rgba(14, 28, 93, 0.1)"
                  >
                    <Text style={styles.dropdownItemText}>{item.label}</Text>
                  </TouchableRipple>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </RegistrationStepComponent>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    humaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 25,
      paddingHorizontal: 0,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
    },
    inputGroup: {
      marginBottom: 25,
    },
    input: {
      backgroundColor: '#FFFFFF',
      height: 50,
      fontSize: 16,
      fontFamily: 'Poppins-Regular',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    inputOutline: {
      borderRadius: 8,
      borderColor: '#39608F',
      borderWidth: 1,
    },
    errorInputOutline: {
      borderRadius: 8,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    dropdownContainer: {
      width: '90%',
      maxHeight: 250,
      backgroundColor: '#FFFFFF',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#39608F',
      overflow: 'hidden',
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    dropdownItem: {
      paddingVertical: 0,
      paddingHorizontal: 0,
    },
    dropdownItemText: {
      fontFamily: 'Poppins-Regular',
      fontSize: 16,
      color: '#000000',
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    separator: {
      height: 1,
      backgroundColor: '#E0E0E0',
      width: '100%',
    },
  });

  return styles;
};
