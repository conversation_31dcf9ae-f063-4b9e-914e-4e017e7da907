com.apply4u.app-jetified-appcompat-resources-1.6.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\061d078e19c3ae7d999b8bf4f4fbd3f7\transformed\jetified-appcompat-resources-1.6.1\res
com.apply4u.app-jetified-lifecycle-viewmodel-savedstate-2.5.1-1 C:\Users\<USER>\.gradle\caches\transforms-3\0c17d1aa2ccc813649d410d5ca1839aa\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\res
com.apply4u.app-recyclerview-1.1.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\0fd1425c12b4ad547fe75169beca3740\transformed\recyclerview-1.1.0\res
com.apply4u.app-core-1.9.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\res
com.apply4u.app-jetified-play-services-base-17.5.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\res
com.apply4u.app-jetified-tracing-1.1.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\1b375b5e368b03c6f05fa335182ca0d8\transformed\jetified-tracing-1.1.0\res
com.apply4u.app-transition-1.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\345ebcb3e4094628456bcdee8e74a5da\transformed\transition-1.2.0\res
com.apply4u.app-jetified-autofill-1.1.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\38344280fe9342d918737701bd2e1811\transformed\jetified-autofill-1.1.0\res
com.apply4u.app-sqlite-framework-2.2.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\424bb7f5e8999507734fb924a0875fe1\transformed\sqlite-framework-2.2.0\res
com.apply4u.app-jetified-play-services-basement-17.5.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\res
com.apply4u.app-jetified-emoji2-views-helper-1.2.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\46d614963c22b23ab93f8be235bc125f\transformed\jetified-emoji2-views-helper-1.2.0\res
com.apply4u.app-sqlite-2.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\47b7a7c93b579e9c1ca6a01deaba4de3\transformed\sqlite-2.2.0\res
com.apply4u.app-jetified-startup-runtime-1.1.1-12 C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\res
com.apply4u.app-swiperefreshlayout-1.1.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\5b356190bdf4bb7065067fc153a69c6d\transformed\swiperefreshlayout-1.1.0\res
com.apply4u.app-lifecycle-livedata-core-2.5.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\605c5c983d653fbcbe978ef5aeb9a8b2\transformed\lifecycle-livedata-core-2.5.1\res
com.apply4u.app-cardview-1.0.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\6119c53d075509c7c00b74ccdeb41c3b\transformed\cardview-1.0.0\res
com.apply4u.app-jetified-savedstate-1.2.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\699d8495cfbcf29796d79737ecf7fe4f\transformed\jetified-savedstate-1.2.0\res
com.apply4u.app-jetified-core-ktx-1.9.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\6fdd42089cdfdb89a5f7c93d6af7471d\transformed\jetified-core-ktx-1.9.0\res
com.apply4u.app-jetified-lifecycle-process-2.4.1-18 C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\res
com.apply4u.app-jetified-viewpager2-1.0.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\933cca3d682df19ab6b85bbd4417e041\transformed\jetified-viewpager2-1.0.0\res
com.apply4u.app-jetified-flipper-0.201.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\res
com.apply4u.app-drawerlayout-1.1.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\9b7c687cf2ef0cdf849c0c1404623f4f\transformed\drawerlayout-1.1.1\res
com.apply4u.app-lifecycle-viewmodel-2.5.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\aa8b400e3fa62f6a4287be6e71a3821f\transformed\lifecycle-viewmodel-2.5.1\res
com.apply4u.app-appcompat-1.6.1-23 C:\Users\<USER>\.gradle\caches\transforms-3\b3627ca143232ec2ceb6e319eb18971d\transformed\appcompat-1.6.1\res
com.apply4u.app-fragment-1.3.6-24 C:\Users\<USER>\.gradle\caches\transforms-3\b3aae516de8ada04cd390a9558584616\transformed\fragment-1.3.6\res
com.apply4u.app-lifecycle-runtime-2.5.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\c2c67d6ef97116786f5e00cacbb4d36d\transformed\lifecycle-runtime-2.5.1\res
com.apply4u.app-material-1.9.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\res
com.apply4u.app-constraintlayout-2.0.1-27 C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\res
com.apply4u.app-jetified-react-android-0.73.6-debug-28 C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\res
com.apply4u.app-jetified-activity-1.6.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\e58f5a5a9361b8dc0ec210eacdd61c46\transformed\jetified-activity-1.6.0\res
com.apply4u.app-jetified-drawee-3.1.3-30 C:\Users\<USER>\.gradle\caches\transforms-3\ec5ffae71883a2ffe61d9bb0ecd6125b\transformed\jetified-drawee-3.1.3\res
com.apply4u.app-jetified-annotation-experimental-1.3.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\efce1b44886555346c024fcbf6fda611\transformed\jetified-annotation-experimental-1.3.0\res
com.apply4u.app-coordinatorlayout-1.2.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\f1ad4007c9d50652fdaa15828c92907b\transformed\coordinatorlayout-1.2.0\res
com.apply4u.app-jetified-emoji2-1.2.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\res
com.apply4u.app-pngs-34 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\generated\res\pngs\debug
com.apply4u.app-resValues-35 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\generated\res\resValues\debug
com.apply4u.app-packageDebugResources-36 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.apply4u.app-packageDebugResources-37 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.apply4u.app-merged_res-38 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\merged_res\debug
com.apply4u.app-debug-39 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\res
com.apply4u.app-main-40 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res
com.apply4u.app-packaged_res-41 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-42 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\masked-view\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-43 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\slider\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-44 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-picker\picker\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-45 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-device-info\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-46 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-document-picker\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-47 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-48 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-geolocation-service\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-49 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-50 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-navbar-color\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-51 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-52 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-53 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-54 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-55 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug
com.apply4u.app-packaged_res-56 D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug
