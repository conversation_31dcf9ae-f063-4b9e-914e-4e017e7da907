[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "com.apply4u.app-merged_res-38:/layout_launch_screen.xml.flat", "source": "com.apply4u.app-main-40:/layout/launch_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\drawable_rn_edit_text_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\drawable\\rn_edit_text_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-merged_res-38:\\layout_launch_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.3\\com.apply4u.app-main-40:\\layout\\launch_screen.xml"}]