import {ILoadingIndicator} from '../interfaces';
import {IApplicationState} from './application-state.interface';

const loadingSpinnerDefaults: ILoadingIndicator = {
  autoHide: false,
  autoHideAfterMilliseconds: 60000,
  show: false,
  text: '',
};

const initialState: IApplicationState = {
  token: null,
  loading: loadingSpinnerDefaults,
  loginUser: null,
  isFirstVisit: true,
  searchParam: {
    applyFilters:false,
    keywords:'',
    locationText:'',
    radius:20,
    sortBy:'Relevancy'
  },
  hiddenJobs: [],
  favJobs:[],
  recentSearches:[],
};

export {initialState};
