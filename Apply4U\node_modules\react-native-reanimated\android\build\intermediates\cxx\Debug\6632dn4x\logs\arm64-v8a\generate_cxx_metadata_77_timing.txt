# C/C++ build system timings
generate_cxx_metadata
  [gap of 43ms]
  create-invalidation-state 111ms
  [gap of 35ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 215ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 68ms]
  create-invalidation-state 114ms
  [gap of 45ms]
  write-metadata-json-to-file 33ms
generate_cxx_metadata completed in 261ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 56ms]
  create-invalidation-state 91ms
  [gap of 36ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 209ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 63ms]
  create-invalidation-state 191ms
  [gap of 97ms]
  write-metadata-json-to-file 62ms
generate_cxx_metadata completed in 417ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 91ms
  [gap of 31ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 156ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 58ms]
  create-invalidation-state 117ms
  [gap of 44ms]
  write-metadata-json-to-file 45ms
generate_cxx_metadata completed in 264ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 42ms]
  create-invalidation-state 102ms
  [gap of 46ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 220ms

