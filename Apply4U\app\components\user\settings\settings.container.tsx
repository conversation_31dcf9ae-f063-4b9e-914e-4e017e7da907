import React from 'react';
import {SettingsView} from './settings.view';
import { Alert } from 'react-native';
import {logout, navigate} from '../../../utility';
import { showLoading, hideLoading } from '../../../redux/actions';
import { IApplicationState, store } from '../../../redux';
import { userApi } from '../../../http';
import { useSelector } from 'react-redux';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import { screens } from '../../../app.constant';

export const SettingsContainer = () =>{
    const userId = useSelector((state:IApplicationState) => (state.loginUser?.Id ?? 0));
    const sLoading = () =>{
        store.dispatch(showLoading());
    }

    const hLoading = () => {
        store.dispatch(hideLoading());
    }
    const onDeleteClick = () =>{
        Alert.alert('Delete Account?', 'Are you sure you want to delete account?', [
            {
              text: 'Yes',
              onPress: () => {
                sLoading();
                userApi.deleteAccount(userId)
                .then(res => {
                    hLoading();
                    showSuccessMessage('Deleted Successfully');
                    navigate(screens.Login);
                    logout();
                })
                .catch(e => {
                    hLoading();
                    showErrorMessage('Something went wrong. Please contact support');
                })
              },
              style: 'destructive',
            },
            {text: 'No', style:'cancel'},
          ]);
    }

    return <SettingsView onDeleteClick={onDeleteClick} />
}