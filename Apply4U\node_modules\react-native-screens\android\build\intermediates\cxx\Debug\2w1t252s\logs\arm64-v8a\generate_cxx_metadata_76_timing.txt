# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 30ms
  [gap of 15ms]
  write-metadata-json-to-file 32ms
generate_cxx_metadata completed in 98ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 65ms]
  create-invalidation-state 66ms
  [gap of 19ms]
  write-metadata-json-to-file 26ms
generate_cxx_metadata completed in 177ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 34ms
  [gap of 15ms]
  write-metadata-json-to-file 29ms
generate_cxx_metadata completed in 93ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 30ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 72ms

