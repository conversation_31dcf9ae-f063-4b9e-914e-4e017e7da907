import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  TextInput,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  ImageBackground,
  SafeAreaView,
} from 'react-native';

import {transparentLogo, background_3} from '../../../assets';

const RegisterStepFiveView = () => {
  const handleSubmitPress = () => {};

  return (
    <ImageBackground source={background_3} style={styles.container}>
      <SafeAreaView>
        <ScrollView contentInsetAdjustmentBehavior="automatic">
          <View style={styles.logoContainer}>
            <Image
              source={transparentLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formBox}>
            <KeyboardAvoidingView enabled>
              <View style={styles.inputView}>
                <TextInput
                  style={styles.inputStyle}
                  placeholder="Desired Job Title, Keyword(s)"
                  placeholderTextColor="#8b9cb5"
                  autoCapitalize="none"
                  returnKeyType="next"
                  underlineColorAndroid="#f000"
                  blurOnSubmit={false}
                />
              </View>

              <View style={styles.inputView}>
                <TextInput
                  style={styles.inputStyle}
                  placeholder="Desired Location"
                  placeholderTextColor="#8b9cb5"
                  autoCapitalize="none"
                  returnKeyType="next"
                  underlineColorAndroid="#f000"
                  blurOnSubmit={false}
                />
              </View>

              <View style={styles.checkboxContainer}>
                <Text style={styles.label}>
                  I Want to relocate job alerts via email.
                </Text>
              </View>

              <TouchableOpacity
                style={styles.buttonPrimary}
                activeOpacity={0.8}
                onPress={handleSubmitPress}>
                <Text style={styles.buttonTextStylePrimary}>Finish</Text>
              </TouchableOpacity>
            </KeyboardAvoidingView>
          </View>
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundContainer: {
    flex: 1,
    height: '100%',
    width: '100%',
  },
  container: {
    flex: 1,
    flexDirection: 'column', // inner items will be added vertically
    flexGrow: 1, // all the available vertical space will be occupied by it
    justifyContent: 'space-between', // will create the gutter between body and footer
  },
  logoContainer: {
    alignItems: 'center',
  },
  appLogo: {
    height: 100,
    width: '50%',
    marginTop: 60,
    marginRight: 40,
    marginBottom: 40,
    marginLeft: 40,
    resizeMode: 'contain',
  },
  formBox: {
    flex: 1,
    margin: 20,
    padding: 15,
    height: '100%',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    backgroundColor: '#edf1fc',
  },
  inputView: {
    height: 40,
    marginBottom: 15,
    flexDirection: 'row',
  },
  inputStyle: {
    flex: 1,
    borderWidth: 1,
    color: '#5c5c5c',
    paddingLeft: 15,
    paddingRight: 15,
    borderRadius: 30,
    borderColor: '#cccccc',
    backgroundColor: '#ffffff',
  },
  buttonPrimary: {
    height: 40,
    marginTop: 10,
    borderWidth: 0,
    color: '#FFFFFF',
    borderRadius: 30,
    alignItems: 'center',
    borderColor: '#5f4cfd',
    backgroundColor: '#5f4cfd',
  },
  buttonTextStylePrimary: {
    fontSize: 16,
    color: '#FFFFFF',
    paddingBottom: 10,
    fontWeight: 'bold',
    paddingVertical: 10,
  },
  iconStyle: {
    color: '#949494',
    paddingVertical: 10,
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonStyleSecond: {
    height: 40,
    marginTop: 10,
    borderWidth: 0,
    borderRadius: 30,
    color: '#FFFFFF',
    alignItems: 'center',
    borderColor: '#fcbc4e',
    backgroundColor: '#fcbc4e',
  },
  buttonTextStyleSecondry: {
    fontSize: 16,
    color: '#000000',
    paddingBottom: 10,
    fontWeight: 'bold',
    paddingVertical: 10,
  },
  errorTextStyle: {
    color: 'red',
    textAlign: 'center',
    fontSize: 14,
  },
  memberTextStyle: {
    padding: 10,
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
    alignSelf: 'center',
  },
  checkboxContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  checkbox: {
    alignSelf: 'center',
  },
  label: {
    margin: 8,
  },
});

export {RegisterStepFiveView};
