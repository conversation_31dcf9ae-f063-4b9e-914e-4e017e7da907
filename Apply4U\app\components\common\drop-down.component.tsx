import React from 'react';
import {Picker} from '@react-native-picker/picker';
import {hasValues} from '../../utility';
import {ITheme, useCurrentTheme} from '../../theme';
import {StyleSheet, View, Text as RNText} from 'react-native';
import {IValidationResult} from '../../interfaces/validation';
import {Text} from '.';
import {dropdownPlaceholderValue} from '../../app.constant';

interface IProps<T> {
  value: T | null | number;
  name: string;
  dataSource: T[];
  displayFeild: keyof T;
  valueFeild: keyof T;
  validationResults?: IValidationResult[];
  styles?: any;
  placeholder?: string;
  enabled?: boolean;
  mode?: 'dialog' | 'dropdown';
  onFocus?: () => void;
  onBlur?: () => void;
  onChange?: (value: T | number | string | null, key: string) => void;
}
export const DropDown = <T extends {}>(props: IProps<T>) => {
  const styles = useCurrentTheme(createStyles);

  const [isValid, setIsValid] = React.useState<boolean>(true);

  React.useEffect(() => {
    if (props.validationResults && hasValues(props.validationResults)) {
      setIsValid(false);
    } else {
      setIsValid(true);
    }
  }, [props.validationResults]);

  return (
    <React.Fragment>
      <View
        style={[styles.container, !isValid ? styles.invalid : styles.valid]}>
        <Picker
          selectedValue={
            props.value !== undefined && props.value != null
              ? typeof props.value === 'number' ||
                typeof props.value === 'string'
                ? props.value
                : props.value[props.valueFeild]
              : dropdownPlaceholderValue
          }
          style={styles.picker}
          key={props.name}
          mode={props.mode ? props.mode : 'dialog'}
          onFocus={() => {
            if (props.onFocus) {
              props.onFocus();
            }
          }}
          onBlur={() => {
            if (props.onBlur) {
              props.onBlur();
            }
          }}
          enabled={props.enabled ? props.enabled : true}
          onValueChange={(itemValue, itemIndex) => {
            if (props.onChange) {
              let items = props.dataSource.filter(
                m => (m as any)[props.valueFeild] === itemValue,
              );
              let item: T | null = null;
              if (items && items.length > 0) {
                item = items[0];
              }

              props.onChange(item, props.name);
            }
          }}
          itemStyle={styles.item}
          >
          <Picker.Item
            key={`key_${-1}`}
            label={!!props.placeholder ? props.placeholder : 'Please Select'}
            value={-1}
            style={
              !isValid
                ? styles.invalidPlaceHolderItem
                : styles.validPlaceHolderItem
            }
          />
          {!!props.dataSource &&
            hasValues(props.dataSource) &&
            props.dataSource.map((item, index) => (
              <Picker.Item
                key={`key_${index}`}
                label={(item as any)[props.displayFeild] as string}
                value={item[props.valueFeild]}
                style={styles.item}
              />
            ))}
        </Picker>
        <RNText style={{width: '100%', height: 60, position: 'absolute', bottom: 0, left: 0}}>{' '}</RNText>
      </View>
      {!isValid &&
        !!props.validationResults &&
        hasValues(props.validationResults) &&
        props.validationResults.map(
          (result, index) =>
            result.isValid == false &&
            result.message && (
              <View key={index} style={styles.errorTextContainer}>
                <Text styles={styles.errorMessageText} text={result.message} />
              </View>
            ),
        )}
    </React.Fragment>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    item: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
      height: 40,
    },
    invalidPlaceHolderItem: {
      ...theme.typography.normal.small,
      height: 40,
      color: theme.palette.error,
    },
    validPlaceHolderItem: {
      ...theme.typography.normal.small,
      color: theme.palette.lightGray,
      height: 40,
    },
    picker: {
      height: 40,
    },
    invalid: {
      borderColor: theme.palette.error,
      color: theme.palette.error,
    },
    valid: {
      borderColor: theme.palette.lightGray,
      color: theme.palette.gray,
    },
    container: {
      width: '100%',
      justifyContent: 'center',
      marginTop: theme.spacing(5),
      borderWidth: 1,
      borderRadius: theme.borderRadius(2),
      backgroundColor: theme.palette.white,
      borderColor: theme.palette.lightGray,
      color: theme.palette.gray,
    },
    errorTextContainer: {
      width: '100%',
      marginLeft: theme.spacing(5),
      marginTop: theme.spacing(2),
      flexDirection: 'column',
    },
    errorMessageText: {
      ...theme.typography.normal.extraSmall,
      color: theme.palette.error,
    },
  });

  return {
    ...styles,
    validPlaceholderColor: theme.palette.lightGray,
    inValidPlaceholderColor: theme.palette.error,
  };
};
