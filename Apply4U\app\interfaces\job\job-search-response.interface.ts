import {IJobSector} from './job-sector.interface';
export interface IJobSearchResponse {
    Id: number;
    CompanyId: number;
    CompanyName: string;
    Title: string;
    Description: string;
    LogoUrl: string;
    PostedByUserId: number;
    JobCategory: string;
    JobSourceId: number;
    LocationId?: number;
    LocationText: string;
    DisplayStartDate: Date;
    DisplayEndDate: Date;
    ApplicationUrl: string;
    Salary: string;
    JobType: string;
    IsShortListed: boolean;
    IsApplied: boolean;
    IsViewed: boolean;
    JobSectors: IJobSector[];
    SalaryFrom: number;
    SalaryTo: number;
    LocalityName: string;
    CountyName: string;
    AreaName: string;
    RegionName: string;
    PostCode: string;
    SalaryDuration: string;
    IsFav?:boolean;
    PostedOn:Date;
}