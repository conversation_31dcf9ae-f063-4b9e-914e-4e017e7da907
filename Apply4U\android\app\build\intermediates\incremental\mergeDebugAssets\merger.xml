<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-navbar-color" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-navbar-color\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-geolocation-service" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-geolocation-service\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-fs" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-document-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-document-picker\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-device-info" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-device-info\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-picker\picker\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-community_masked-view" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\masked-view\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets"><file name="fonts/Poppins-Bold.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-Bold.ttf"/><file name="fonts/Poppins-ExtraBold.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-ExtraBold.ttf"/><file name="fonts/Poppins-Light.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-Light.ttf"/><file name="fonts/Poppins-Medium.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-Medium.ttf"/><file name="fonts/Poppins-Regular.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-Regular.ttf"/><file name="fonts/Poppins-SemiBold.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\assets\fonts\Poppins-SemiBold.ttf"/></source><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons"><file name="fonts/AntDesign.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\AntDesign.ttf"/><file name="fonts/Entypo.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Entypo.ttf"/><file name="fonts/EvilIcons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\EvilIcons.ttf"/><file name="fonts/Feather.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Feather.ttf"/><file name="fonts/FontAwesome.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome.ttf"/><file name="fonts/FontAwesome5_Brands.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Brands.ttf"/><file name="fonts/FontAwesome5_Regular.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Regular.ttf"/><file name="fonts/FontAwesome5_Solid.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome5_Solid.ttf"/><file name="fonts/FontAwesome6_Brands.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Brands.ttf"/><file name="fonts/FontAwesome6_Regular.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Regular.ttf"/><file name="fonts/FontAwesome6_Solid.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\FontAwesome6_Solid.ttf"/><file name="fonts/Fontisto.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Fontisto.ttf"/><file name="fonts/Foundation.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Foundation.ttf"/><file name="fonts/Ionicons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialIcons.ttf"/><file name="fonts/Octicons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Octicons.ttf"/><file name="fonts/SimpleLineIcons.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\SimpleLineIcons.ttf"/><file name="fonts/Zocial.ttf" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Zocial.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>