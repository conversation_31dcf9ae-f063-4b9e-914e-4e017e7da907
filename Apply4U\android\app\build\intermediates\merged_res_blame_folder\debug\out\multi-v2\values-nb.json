{"logs": [{"outputFile": "com.apply4u.app-mergeDebugResources-36:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,230,316,399,474,543,625,693,760,834,912,997,1077,1146,1224,1307,1383,1463,1543,1620,1690,1759,1844,1920,1995,2065", "endColumns": "69,104,85,82,74,68,81,67,66,73,77,84,79,68,77,82,75,79,79,76,69,68,84,75,74,69,77", "endOffsets": "120,225,311,394,469,538,620,688,755,829,907,992,1072,1141,1219,1302,1378,1458,1538,1615,1685,1754,1839,1915,1990,2060,2138"}, "to": {"startLines": "33,39,40,44,65,67,68,70,84,85,86,124,125,126,127,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2943,3418,3523,3909,6850,6993,7062,7204,8214,8281,8355,11290,11375,11455,11524,11682,11765,11841,11921,12001,12078,12148,12217,12403,12479,12554,12624", "endColumns": "69,104,85,82,74,68,81,67,66,73,77,84,79,68,77,82,75,79,79,76,69,68,84,75,74,69,77", "endOffsets": "3008,3518,3604,3987,6920,7057,7139,7267,8276,8350,8428,11370,11450,11519,11597,11760,11836,11916,11996,12073,12143,12212,12297,12474,12549,12619,12697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19ec269da49c6c76309942cfa013a60d\\transformed\\jetified-play-services-base-17.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,348,539,663,770,985,1113,1259,1387,1600,1705,1897,2023,2224,2426,2519,2607", "endColumns": "102,190,123,106,214,127,145,127,212,104,191,125,200,201,92,87,102", "endOffsets": "347,538,662,769,984,1112,1258,1386,1599,1704,1896,2022,2223,2425,2518,2606,2709"}, "to": {"startLines": "45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3992,4099,4290,4418,4529,4744,4876,5026,5347,5560,5669,5861,5991,6192,6398,6495,6587", "endColumns": "106,190,127,110,214,131,149,131,212,108,191,129,200,205,96,91,106", "endOffsets": "4094,4285,4413,4524,4739,4871,5021,5153,5555,5664,5856,5986,6187,6393,6490,6582,6689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3627ca143232ec2ceb6e319eb18971d\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,11602", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,11677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13659b94c66de92d98d7f8af61234b95\\transformed\\core-1.9.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "137", "startColumns": "4", "startOffsets": "12302", "endColumns": "100", "endOffsets": "12398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3a8a24834f295d8078f18b8298041f7\\transformed\\material-1.9.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1030,1122,1190,1250,1337,1401,1463,1527,1595,1660,1714,1823,1881,1943,1997,2072,2192,2274,2354,2488,2566,2646,2734,2788,2839,2905,2973,3047,3137,3208,3286,3356,3426,3515,3593,3681,3771,3843,3915,3999,4050,4116,4197,4280,4342,4406,4469,4569,4667,4760,4858,4916,4971", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "256,333,406,493,581,661,760,879,961,1025,1117,1185,1245,1332,1396,1458,1522,1590,1655,1709,1818,1876,1938,1992,2067,2187,2269,2349,2483,2561,2641,2729,2783,2834,2900,2968,3042,3132,3203,3281,3351,3421,3510,3588,3676,3766,3838,3910,3994,4045,4111,4192,4275,4337,4401,4464,4564,4662,4755,4853,4911,4966,5044"}, "to": {"startLines": "2,34,35,36,37,38,41,42,43,63,64,66,69,71,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3090,3163,3250,3338,3609,3708,3827,6694,6758,6925,7144,7272,7359,7423,7485,7549,7617,7682,7736,7845,7903,7965,8019,8094,8433,8515,8595,8729,8807,8887,8975,9029,9080,9146,9214,9288,9378,9449,9527,9597,9667,9756,9834,9922,10012,10084,10156,10240,10291,10357,10438,10521,10583,10647,10710,10810,10908,11001,11099,11157,11212", "endLines": "5,34,35,36,37,38,41,42,43,63,64,66,69,71,72,73,74,75,76,77,78,79,80,81,82,83,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "12,76,72,86,87,79,98,118,81,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,79,133,77,79,87,53,50,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,57,54,77", "endOffsets": "306,3085,3158,3245,3333,3413,3703,3822,3904,6753,6845,6988,7199,7354,7418,7480,7544,7612,7677,7731,7840,7898,7960,8014,8089,8209,8510,8590,8724,8802,8882,8970,9024,9075,9141,9209,9283,9373,9444,9522,9592,9662,9751,9829,9917,10007,10079,10151,10235,10286,10352,10433,10516,10578,10642,10705,10805,10903,10996,11094,11152,11207,11285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4343b2ae69420088cd050005b2aafd9c\\transformed\\jetified-play-services-basement-17.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "188", "endOffsets": "435"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "5158", "endColumns": "188", "endOffsets": "5342"}}]}]}