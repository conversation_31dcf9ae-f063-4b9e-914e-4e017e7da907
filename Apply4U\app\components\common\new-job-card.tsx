import React from 'react';
import { Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import { defaultCompanyLogo1 } from '../../assets';
import { ITheme, useCurrentTheme } from '../../theme';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import {
    faHeart,
    faMapMarkerAlt,
    faBriefcase,
    faPoundSign,
} from '@fortawesome/free-solid-svg-icons';
import Icon from 'react-native-vector-icons/FontAwesome';
import { Text, Image, Button } from '.';
import {isNullOrWhitespace } from '../../utility';
import { Swipeable } from 'react-native-gesture-handler';
import { jobCardHeight } from '../../app.constant';
import { faEyeSlash, faLocationArrow, faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { showInfoMessage } from '../../external/toaster';

interface IProps {
    jobId: number;
    jobTitle: string;
    locationText: string;
    salaryRange: string;
    jobType: string;
    jobCategory: string;
    companyName: string;
    companyLogoUrl: string;
    jobDetails: string;
    showFavIcon: boolean;
    showShareIcon: boolean;
    isSwipeable: boolean;
    isAlreadyApplied: boolean;
    applicationUrl: string;
    jobSourceId: number;
    swipeRightText: string;
    isSwipeLeftEnabled: boolean;
    isSwipeRightEnabled: boolean;
    isFav: boolean;
    postedOnText: string;
    favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
    goToJobDetail: (jobId: number) => void;
    onSwipeRight: (jobId: number) => void;
    easyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
}

const jobCardAreEqual = (prevProps: IProps, nextProps: IProps) => {
    return (
        prevProps.jobId == nextProps.jobId
        &&
        prevProps.jobTitle == nextProps.jobTitle
        &&
        prevProps.locationText == nextProps.locationText
        &&
        prevProps.salaryRange == nextProps.salaryRange
        &&
        prevProps.jobType == nextProps.jobType
        &&
        prevProps.jobCategory == nextProps.jobCategory
        &&
        prevProps.companyName == nextProps.companyName
        &&
        prevProps.companyLogoUrl == nextProps.companyLogoUrl
        &&
        prevProps.showFavIcon == nextProps.showFavIcon
        &&
        prevProps.showShareIcon == nextProps.showShareIcon
        &&
        prevProps.isSwipeable == nextProps.isSwipeable
        &&
        prevProps.isAlreadyApplied == nextProps.isAlreadyApplied
        &&
        prevProps.applicationUrl == nextProps.applicationUrl
        &&
        prevProps.jobSourceId == nextProps.jobSourceId
        &&
        prevProps.swipeRightText == nextProps.swipeRightText
        &&
        prevProps.isFav == nextProps.isFav
        &&
        prevProps.postedOnText == nextProps.postedOnText
    );
}

const JobCard = React.memo((props: IProps) => {
    const { styles, colors } = useCurrentTheme(createStyles, props);
    const [swipeableRef, setSwipeAbleRef] = React.useState<Swipeable | null>(
        null,
    );

    const renderRightAction = React.useCallback(() => {
        return (
            <View style={styles.renderActionContainer}>
                <View style={styles.hideJobsContainer}>
                    <View style={styles.renderActionContentContainer}>
                        <FontAwesomeIcon style={{ alignSelf: 'flex-end', marginRight: 16 }} icon={faEyeSlash} size={35} color={'#909090'} />
                        <Text styles={styles.renderRightActionText} text={props.swipeRightText} />
                    </View>
                </View>
            </View>
        );
    }, []);

    const renderLeftAction = React.useCallback(() => {
        return (
            <View style={styles.renderActionContainer}>
                <View
                    style={
                        !!props.isAlreadyApplied
                            ? styles.successEasyApplyContainer
                            : styles.easyApplyContainer
                    }>
                    <View style={styles.renderActionContentContainer}>
                        {props.isAlreadyApplied === false && <FontAwesomeIcon style={{ alignSelf: 'flex-start', marginLeft: 35 }} icon={faLocationArrow} size={35} color={'#11c0e7'} />}
                        {props.isAlreadyApplied === true && <FontAwesomeIcon style={{ alignSelf: 'flex-start', marginLeft: 50 }} icon={faCheckCircle} size={35} color={'#004000'} />}
                        <Text styles={props.isAlreadyApplied === true
                            ? styles.renderActionSuccessText
                            : styles.renderActionText}>
                            {!!props.isAlreadyApplied
                                ? 'Already Applied'
                                : 'Easy Apply'}
                        </Text>
                    </View>
                </View>
            </View>
        );
    }, [props.isAlreadyApplied]);

    const swipeAbleContent = (
        <View style={styles.container}>
            <View style={{
                flex: 1,
                flexDirection: 'row',
            }}>
                <View style={{
                    flex: 2,
                    flexDirection: 'column'
                }}>
                    <Pressable
                        onPress={() => {
                            props.goToJobDetail(props.jobId);
                        }}>
                        <Text
                            styles={styles.jobTitle}
                            text={`${props.jobTitle}`}
                            numberOfLines={1}
                        />
                    </Pressable>
                    <Text styles={styles.postedOnText} numberOfLines={1}>
                        {(!!props.postedOnText ? `Posted on ${props.postedOnText}` : '') + (!!props.companyName ? ' by ' : '')}
                        {!!props.companyName &&
                            <Text
                                styles={styles.companyName}
                                numberOfLines={1}
                                text={` ${props.companyName}`}
                            />}
                    </Text>

                    <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faMapMarkerAlt}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.locationText}
                    </Text>

                    {!!props.salaryRange && <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faPoundSign}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.salaryRange}
                    </Text>}

                    {!!props.jobType && <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faBriefcase}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.jobType}
                    </Text>}
                </View>
                <View style={{
                    flex: 1.2,
                    flexDirection: 'column'
                }}>
                    <View style={{
                        flex: 1,
                        flexDirection: 'row'
                    }}>
                        <View style={{
                            flex: 1.2,
                            justifyContent: 'center'
                        }}>
                            {props.showFavIcon && props.showFavIcon === true && (
                                <TouchableOpacity activeOpacity={1} onPress={() => {
                                    props.favJobsAddOrRemove(props.jobId, props.isFav);
                                }} style={styles.headerCell3Row1}>
                                    {!!props.isFav && <FontAwesomeIcon
                                        icon={faHeart}
                                        style={styles.addToFavIcon}
                                        size={30}
                                        color={colors.primaryColor}
                                    />}
                                    {!props.isFav && <Icon
                                        name='heart-o'
                                        style={styles.addToFavIcon}
                                        size={30}
                                        color={colors.primaryColor}
                                    />}
                                </TouchableOpacity>
                            )}
                        </View>

                        <View style={{
                            flex: 2,
                            justifyContent: 'center',
                            borderWidth:0.3,
                            borderRadius:3,
                            borderColor:colors.primaryColor,
                        }}>
                            <Image
                                defaultSource={defaultCompanyLogo1}
                                source={
                                    !isNullOrWhitespace(props.companyLogoUrl)
                                        ? { uri: props.companyLogoUrl }
                                        : defaultCompanyLogo1
                                }
                                style={styles.companyLogo}
                                key={props.jobId}
                            />
                        </View>
                    </View>
                    <View style={{
                        flex: 1,
                        justifyContent: 'flex-end'
                    }}>
                        <Button pressed={() => {
                            if (!props.isAlreadyApplied) {
                                props.easyApply(props.jobId, props.jobSourceId, props.jobTitle, props.applicationUrl,false);
                            }else{
                                showInfoMessage('You already have applied for this job.');
                            }
                        }} styles={styles.applyNowButton} text={!!props.isAlreadyApplied ? 'Applied':'Apply'} />
                    </View>
                </View>
            </View>
        </View>
    );

    return (
        <>
            {!!props.isSwipeable && props.isSwipeable == true && (
                <Swipeable
                    renderLeftActions={props.isSwipeLeftEnabled ? renderLeftAction : undefined}
                    onSwipeableLeftWillOpen={() => {
                        if (props.isSwipeLeftEnabled) {
                            if (!props.isAlreadyApplied) {
                                props.easyApply(props.jobId, props.jobSourceId, props.jobTitle, props.applicationUrl,true);
                            }else{
                                showInfoMessage('You already have applied for this job.');
                            }

                            swipeableRef?.close();
                        }
                    }}
                    renderRightActions={props.isSwipeRightEnabled ? renderRightAction : undefined}
                    onSwipeableRightOpen={() => {
                        if (props.isSwipeRightEnabled) {
                            if (props.jobId > 0) {
                                props.onSwipeRight(props.jobId);
                            } else {
                                swipeableRef?.close();
                            }
                        }
                    }}
                    ref={ref => setSwipeAbleRef(ref)}>
                    <View style={styles.swipeAbleContainer}>{swipeAbleContent}</View>
                </Swipeable>
            )}
            {!props.isSwipeable && swipeAbleContent}
        </>
    );
}, jobCardAreEqual);

const createStyles = (theme: ITheme, props: any) => {
    const styles = StyleSheet.create({
        container: {
            width: props.isSwipeable == true ? '100%' : '97%',
            height: jobCardHeight,
            backgroundColor: '#ffffff',
            marginTop: theme.spacing(5),
            padding: theme.spacing(5),
            paddingBottom: 10,
            alignSelf: 'center',
            justifyContent: 'center',
            marginHorizontal: props.isSwipeable == true ? 0 : 5,
            borderRadius: theme.borderRadius(10),
        },
        swipeAbleContainer: {
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'center',
            paddingHorizontal: 10,
        },
        header: {
            flex: 1.3,
            flexDirection: 'row',
        },
        headerCell1: {
            flex: 1,
            flexDirection: 'column',
            //backgroundColor: 'green',
            justifyContent: 'center',
            borderWidth: 1,
            borderRadius: 5,
            borderColor: theme.palette.lightGray,
        },
        headerCell2: {
            flex: 3,
            flexDirection: 'column',
            //backgroundColor: 'red',
            justifyContent: 'center',
        },
        headerCell2Row1: {
            flex: 1,
            flexDirection: 'column',
        },
        headerCell2Row2: {
            flex: 1,
            flexDirection: 'column',
            justifyContent: 'flex-start',
        },
        headerCell3: {
            flex: 1,
            flexDirection: 'column',
            //backgroundColor: 'blue',
        },
        headerCell3Container: {
            flex: 1,
            flexDirection: 'row',
        },
        headerCell3Row1: {
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'center',
        },
        headerCell3Row2: {
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'center',
        },
        content: {
            flex: 2.5,
            flexDirection: 'row',
            //backgroundColor: 'yellow',
            borderTopWidth: 0.2,
            borderTopColor: 'gray',
        },
        contentContainer: {
            flex: 1,
            flexDirection: 'column',
        },
        contentRow1: {
            flex: 1,
            flexDirection: 'column',
            //backgroundColor: 'red',
            paddingLeft: theme.spacing(5),
        },
        contentRow2: {
            flex: 1.8,
            flexDirection: 'column',
        },
        contentRow2Row1: {
            flex: 1,
            //backgroundColor: 'pink',
        },
        contentRow2Row2: {
            flex: 1,
            //backgroundColor: 'pink',
        },
        contentRowCellsContainer: {
            flex: 1,
            flexDirection: 'row',
        },
        contentRowCell: {
            flex: 1,
            flexDirection: 'column',
            paddingLeft: theme.spacing(5),
        },
        contentRow1Cell1: {
            flex: 1,
            flexDirection: 'column',
            paddingLeft: theme.spacing(5),
            justifyContent: 'center',
        },
        companyLogo: {
            width: '95%',
            height: '95%',
            resizeMode: 'contain',
            alignSelf: 'center',
        },
        jobTitle: {
            color: '#4f40d1',
            fontWeight: 'bold',
            letterSpacing: 0.5,
            paddingHorizontal: theme.spacing(2),
        },
        addToFavIconColor: {
            color: '#a2b3e0',
        },
        shareIconColor: {
            color: '#a2b3e0',
        },
        addToFavIcon: {
            marginTop: theme.spacing(6),
        },
        shareIcon: {
            color: '#a2b3e0',
            marginTop: theme.spacing(6),
        },
        detailText: {
            fontSize: 12,
            color: '#333333',
            letterSpacing: 0.5,
            alignSelf: 'flex-start',
        },
        iconStyle: {
            width: 20,
            color: '#d1d1d1',
            marginRight:theme.spacing(10),
        },
        iconText: {
            fontSize: 12,
            color: '#333333',
        },
        renderActionContainer: {
            flex: 1,
        },
        hideJobsContainer: {
            width: '95%',
            height: jobCardHeight,
            marginTop: theme.spacing(5),
            padding: theme.spacing(3),
            paddingBottom: 10,
            alignSelf: 'center',
            justifyContent: 'center',
            borderRadius: theme.borderRadius(10),
            backgroundColor: '#c8c8c8',
        },
        easyApplyContainer: {
            width: '95%',
            height: jobCardHeight,
            marginTop: theme.spacing(5),
            padding: theme.spacing(3),
            paddingBottom: 10,
            alignSelf: 'center',
            justifyContent: 'center',
            borderRadius: theme.borderRadius(10),
            backgroundColor: '#a6e4f3',
        },
        successEasyApplyContainer: {
            width: '95%',
            height: jobCardHeight,
            marginTop: theme.spacing(5),
            padding: theme.spacing(3),
            paddingBottom: 10,
            alignSelf: 'center',
            justifyContent: 'center',
            borderRadius: theme.borderRadius(10),
            backgroundColor: '#009F00',
        },
        renderActionContentContainer: {
            paddingRight: 10,
            justifyContent: 'center',
        },
        renderActionText: {
            ...theme.typography.bold.small,
            alignSelf: 'flex-start',
            color: '#11c0e7',
            marginLeft: theme.spacing(8),
        },
        renderActionSuccessText: {
            ...theme.typography.bold.small,
            alignSelf: 'flex-start',
            color: '#004000',
            marginLeft: theme.spacing(8),
        },
        renderRightActionText: {
            ...theme.typography.bold.small,
            alignSelf: 'flex-end',
            color: '#909090',
            marginRight: theme.spacing(8)
        },

        applyNowButton: {
            ...theme.buttons.secondary,
            borderRadius: theme.borderRadius(5),
            width: 90,
            marginTop: 0,
            marginBottom: 0,
            alignSelf: 'flex-end'
        },
        jobOptionsText: {
            ...theme.typography.normal.extraSmall,
            paddingLeft: theme.spacing(5),
            color: '#a8a7a7',
            paddingVertical: theme.spacing(1.5)
        },
        companyName: {
            ...theme.typography.bold.extraSmall,
            fontSize: 11,
            color: theme.palette.secondary,
        },
        postedOnText: {
            ...theme.typography.bold.extraSmall,
            paddingLeft: theme.spacing(3),
            color: '#bbbbbb',
            paddingTop:0,
            marginBottom:theme.spacing(3)
        },
    });

    const colors = { defaultIconColor: '#a2b3e0', green: 'green',primaryColor:theme.palette.primary }
    return { styles, colors };
};

export { JobCard };
