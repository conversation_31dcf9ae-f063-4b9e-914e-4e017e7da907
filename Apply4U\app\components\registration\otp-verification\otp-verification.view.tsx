import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  PermissionsAndroid,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  HelperText,
} from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { BlueheadContainer } from '../../common';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { successBubbles } from '../../../assets';

interface IProps {
  email: string;
}

export const OtpVerificationView = ({ email }: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const [otp, setOtp] = useState(['', '', '', '']);
  const [timer, setTimer] = useState(30);
  const [isResendDisabled, setIsResendDisabled] = useState(true);
  const [otpError, setOtpError] = useState('');
  const [isAutoFilling, setIsAutoFilling] = useState(false);
  const inputRefs = useRef<Array<any>>([null, null, null, null]);

  const correctOtp = '1234';

  const autoFillOtp = (otpCode: string) => {
    if (otpCode && otpCode.length === 4) {
      const otpArray = otpCode.split('');
      setOtp(otpArray);
      setIsAutoFilling(true);
      setOtpError('');

      setTimeout(() => {
        setIsAutoFilling(false);
        handleVerifyOtp(otpCode);
      }, 500);
    }
  };

  const handleVerifyOtp = (otpString?: string) => {
    const otpToVerify = otpString || otp.join('');
    console.log('Verifying OTP:', otpToVerify);

    if (otpToVerify === correctOtp) {
      setOtpError('');
      navigate(screens.SuccessScreen, {
        title: 'Congratulation !!!',
        message: 'You successfully registered with us!',
        message2: 'Unlock job offers by answer a few quick questions to personalize your experience ',
        buttonText: 'Next',
        navigateTo: screens.NewRegisterIntro,
        customImage: successBubbles,
        checkmarkPosition: 'center',
      });
    } else {
      setOtpError('Invalid OTP. Please enter a valid OTP.');
    }
  };

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) {
      const numericValue = value.replace(/\D/g, '');

      if (numericValue.length === 4) {
        autoFillOtp(numericValue);
        return;
      } else if (numericValue.length > 0) {
        const newOtp = [...otp];
        const digits = numericValue.split('').slice(0, 4 - index);

        digits.forEach((digit, i) => {
          if (index + i < 4) {
            newOtp[index + i] = digit;
          }
        });

        setOtp(newOtp);

        const nextIndex = Math.min(index + digits.length, 3);
        if (inputRefs.current[nextIndex]) {
          inputRefs.current[nextIndex].focus();
        }
        return;
      }
    }

 
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(0, 1);
    setOtp(newOtp);

    if (value && index < 3) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  useEffect(() => {
    const focusTimeout = setTimeout(() => {
      if (inputRefs.current[0]) {
        inputRefs.current[0].focus();
      }
    }, 100);

    return () => clearTimeout(focusTimeout);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      if (timer > 0) {
        setTimer(timer - 1);
      } else {
        setIsResendDisabled(false);
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [timer]);

  const handleResendOtp = () => {
    if (!isResendDisabled) {
      setOtp(['', '', '', '']);
      setTimer(30);
      setIsResendDisabled(true);
      setOtpError('');
      inputRefs.current[0].focus();
    }
  };

  const isOtpComplete = otp.every(digit => digit !== '');

  const handleVerify = () => {
    Keyboard.dismiss();
    if (isOtpComplete) {
      handleVerifyOtp();
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}>
        <View style={styles.mainContainer}>
          <BlueheadContainer showBackButton={true} showHomeButton={true}>
            <ScrollView
              contentContainerStyle={styles.scrollContent}
              keyboardShouldPersistTaps="handled">
              <View style={styles.contentContainer}>
                <Text style={styles.title}>OTP Verification</Text>
                <View style={styles.descriptionContainer}>
                  <Text style={styles.description}>
                  We’ve sent an OTP to{" "}
                    <Text style={styles.emailDescription}>
                      {email}
                    </Text>
                  </Text>


                </View>
                <View style={styles.otpContainer}>
                  {otp.map((digit, index) => (
                    <TextInput
                      key={`otp-${index}`}
                      ref={(el: any) => inputRefs.current[index] = el}
                      style={[
                        styles.otpInput,
                        isAutoFilling && styles.autoFillingInput
                      ]}
                      mode="outlined"
                      value={digit}
                      onChangeText={(value) => {
                        handleOtpChange(value, index);
                        if (otpError) setOtpError('');
                      }}
                      keyboardType="numeric"
                      maxLength={index === 0 ? 4 : 1}
                      outlineStyle={[
                        styles.otpInputOutline,
                        !!otpError && styles.errorOutline,
                        isAutoFilling && styles.autoFillingOutline
                      ]}
                      activeOutlineColor="#0E1C5D"
                      onKeyPress={(e) => handleKeyPress(e, index)}
                      autoComplete={index === 0 ? "one-time-code" : "off"}
                      textContentType={index === 0 ? "oneTimeCode" : "none"}
                      autoCorrect={false}
                      autoCapitalize="none"
                      selectTextOnFocus={true}
                      error={!!otpError}
                    />
                  ))}
                </View>

                {isAutoFilling && (
                  <View style={styles.autoFillIndicator}>
                    <Text style={styles.autoFillText}>Auto-filling OTP...</Text>
                  </View>
                )}

                {!!otpError && (
                  <HelperText type="error" visible={true} style={styles.errorText}>
                    {otpError}
                  </HelperText>
                )}

                <View style={styles.resendContainer}>
                  <Text
                    style={[
                      styles.resendText,
                      isResendDisabled ? styles.disabledText : styles.activeText
                    ]}
                    onPress={handleResendOtp}
                  >
                    Resend OTP
                  </Text>
                  <Text style={styles.timerText}>{formatTime(timer)}</Text>
                </View>
              </View>
            </ScrollView>
          </BlueheadContainer>

          {/* Bottom section with verify button */}
          <View style={styles.bottomContainer}>
            <Button
              mode="contained"
              onPress={handleVerify}
              style={[
                styles.signUpButton,
                !isOtpComplete && styles.disabledButton
              ]}
              labelStyle={styles.signUpButtonText}
              disabled={!isOtpComplete}>
              Verify
            </Button>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (_theme: ITheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'white',
    },
    mainContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    description: {
      fontSize: 16,
      color: '#0E1C5D',
      textAlign: 'center',
      fontFamily: 'Poppins-Medium',
      flexWrap: 'wrap',
      lineHeight: 24,
    },
    descriptionContainer: {
      width: '100%',
      paddingHorizontal: 20,
      marginBottom: 30,
      alignItems: 'center',
      justifyContent: 'center',
    },
    emailDescription: {
      fontSize: 16,
      color: '#0E1C5D',
      fontFamily: 'Poppins-Bold',
      flexWrap: 'wrap',
    },
    contentContainer: {
      width: '100%',
      alignItems: 'center',
    },
    bottomContainer: {
      width: '100%',
      paddingHorizontal: 20,
      paddingBottom: 63,
      marginTop: 20,
    },
    title: {
      fontSize: 30,
      color: '#0E1C5D',
      textAlign: 'center',
      marginBottom: 30,
      fontFamily: 'Poppins-Bold',
    },
    inputContainer: {
      marginBottom: 20,
    },
    input: {
      backgroundColor: 'white',
      height: 50,
    },
    inputOutline: {
      borderRadius: 5,
      borderColor: '#0E1C5D',
      borderWidth: 1,
    },
    otpContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '80%',
      marginVertical: 20,
    },
    otpInput: {
      width: 65,
      height: 60,
      textAlign: 'center',
      fontSize: 24,
      backgroundColor: 'white',
      marginHorizontal: 5,
    },
    otpInputOutline: {
      borderRadius: 10,
      borderColor: '#0E1C5D',
      borderWidth: 1.5,
    },
    resendContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      marginTop: 15,
    },
    resendText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      marginBottom: 5,
    },
    timerText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#0E1C5D',
      fontFamily: 'Poppins-Bold',
    },
    disabledText: {
      color: '#9E9E9E',
    },
    activeText: {
      color: '#0E1C5D',
    },
    signUpButton: {
      borderRadius: 5,
      height: 50,
      justifyContent: 'center',
      marginBottom: 15,
      backgroundColor: '#0E1C5D',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    signUpButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      color: 'white',
    },
    disabledButton: {
      backgroundColor: '#9E9E9E',
      opacity: 0.7,
    },
    errorOutline: {
      borderColor: '#FF3B30',
    },
    errorText: {
      color: '#FF3B30',
      fontSize: 14,
      textAlign: 'center',
      marginTop: 5,
      marginBottom: 5,
      fontFamily: 'Poppins-Regular',
    },
    autoFillingInput: {
      backgroundColor: '#E3F2FD',
    },
    autoFillingOutline: {
      borderColor: '#2196F3',
      borderWidth: 2,
    },
    autoFillIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 10,
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: '#E3F2FD',
      borderRadius: 20,
    },
    autoFillText: {
      color: '#2196F3',
      fontSize: 14,
      fontFamily: 'Poppins-Medium',
    },
  });
};
