import React from 'react';
import { MainLayout } from './main.layout';
import { WebView } from 'react-native-webview';
import { hideLoading, showLoading } from '../../utility';

interface IProps{
    URL:string;
}

export const CustomBrowser = (props:IProps) =>{

    React.useEffect(() => {
        showLoading();
    },[]);
    return <MainLayout>
        <WebView source={{ uri: props.URL }}
        onLoadStart={() =>{
            showLoading();
        }}

        onLoadEnd={() => {
            hideLoading();
        }}

        onError={() => {
            hideLoading();
        }}
        />
    </MainLayout>
}