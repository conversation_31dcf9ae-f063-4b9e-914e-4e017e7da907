import React from 'react';
import {LoginView} from './login.view';
import {showErrorMessage} from '../../../external/toaster';
import {loginApi, userApi} from '../../../http';
import {
  getParams,
  isNullOrEmpty,
  navigate,
  validate,
  validateAll,
} from '../../../utility';
import {connect} from 'react-redux';
import {
  hideLoading,
  setTokenAction,
  showLoading,
  setLoginUserDetailAction,
} from '../../../redux/actions';
import {IApplicationState} from '../../../redux';
import {
  IBrowserParam,
  ILogin,
  ILoginParam,
  ITokenResult,
  IUserDetail,
} from '../../../interfaces';
import {useTranslation} from 'react-i18next';
import {
  ValidationRules,
  ValidationResults,
} from '../../../interfaces/validation';
import {registerNowWebUrl, screens} from '../../../app.constant';
import { Linking } from 'react-native';

interface IProps {
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  setToken: (result: ITokenResult | null) => void;
  setLoginUserDetail: (userDetail: IUserDetail) => void;
  hideLoading: () => void;
}

const LoginContainer = (props: IProps) => {
  const loginParam = getParams<ILoginParam>();
  let navigateTo: ILoginParam = {
    screenToNavigate: screens.Dashboard,
    params: null,
  };

  if (loginParam) {
    navigateTo = loginParam;
  }

  const [loginData, setLoginData] = React.useState<ILogin>({
    email: '',
    password: '',
  });
  const {t} = useTranslation(['Login']);

  const login = async () => {
    let validateAllResult = validateAll<ILogin>(validations, loginData);
    if (
      validateAllResult.isValid === false &&
      validateAllResult.validationResults
    ) {
      setValidationResults({...validateAllResult.validationResults});
    }

    if (validateAllResult.isValid) {
      props.showLoading();
      loginApi
        .login(loginData.email ?? '', loginData.password ?? '')
        .then(tokenResult => {
          if (!tokenResult || !isNullOrEmpty(tokenResult)) {
            let expireOn = new Date(
              Date.now() + (tokenResult.expires_in as number) * 1000,
            );

            tokenResult = {...tokenResult, expire_on: expireOn};
            userApi
              .getUserDetail(loginData.email)
              .then(loginUserDetail => {
                if (
                  (loginUserDetail?.Id ?? 0) > 0 &&
                  (loginUserDetail?.UserProfile?.Id ?? 0) > 0
                ) {
                  props.setToken(tokenResult);
                  props.setLoginUserDetail({
                    ...loginUserDetail,
                    LoginName: loginData.email,
                    Password: loginData.password,
                  });
                  navigate(navigateTo.screenToNavigate, navigateTo.params);
                } else {
                  showErrorMessage(t('Messages.Invalid_Username_Password'));
                }
              })
              .catch(er => {
                showErrorMessage(t('Messages.Invalid_Username_Password'));
              })
              .finally(() => {
                props.hideLoading();
              });
          } else {
            showErrorMessage(t('Messages.Invalid_Username_Password'));
          }
        })
        .catch(error => {
          props.hideLoading();
          showErrorMessage(t('Messages.Invalid_Username_Password'));
        });
    }
  };

  const registerNow = () => {
     //Linking.openURL(registerNowWebUrl).catch(err => {});
    // navigate<IBrowserParam>(screens.Browser,{URL:registerNowWebUrl});
    navigate(screens.NewRegisterIntro);
  };

  const validations: ValidationRules<ILogin> = {
    email: {
      validationRules: {
        required: {
          isRequired: true,
          message: t('Messages.Email_Required'),
        },
        validEmail: {
          checkValidEmail: true,
          message: t('Messages.Invalid_Email'),
        },
      },
    },
    password: {
      validationRules: {
        required: {
          isRequired: true,
          message: t('Messages.Password_Required'),
        },
      },
    },
  };

  const [validationResults, setValidationResults] = React.useState<
    ValidationResults<ILogin>
  >({email: {validationResults: []}, password: {validationResults: []}});
  const resetValidation = (key: keyof ILogin) => {
    let results = {...validationResults};
    results[key].validationResults = [];
    setValidationResults({...results});
  };

  const validateIt = (key: keyof ILogin) => {
    let results = validate(loginData[key], validations[key]?.validationRules);
    let updatedValidationResults = {...validationResults};
    updatedValidationResults[key].validationResults = results;
    setValidationResults({...updatedValidationResults});
  };

  const forgotPassword = () => {
    navigate(screens.ForgotPassword);
  }

  return (
    <LoginView
      login={login}
      loginData={loginData}
      setLoginData={setLoginData}
      registerNow={registerNow}
      resetValidation={resetValidation}
      validationResults={validationResults}
      validate={validateIt}
      forgotPasswordClick={forgotPassword}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const mapDispatchToProps = {
  setToken: setTokenAction,
  setLoginUserDetail: setLoginUserDetailAction,
  showLoading,
  hideLoading,
};

const connectedLoginContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(LoginContainer);
export {connectedLoginContainer as LoginContainer};
