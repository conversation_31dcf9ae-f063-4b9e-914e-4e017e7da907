import {
  createNavigationContainerRef,
  DrawerActions,
} from '@react-navigation/core';
import {getPropertyNames, hasValues, isAuthenticated, isNullOrEmpty} from '.';
import {screens} from '../app.constant';
import {ILoginParam, IScreen} from '../interfaces';

export const navigationRef = createNavigationContainerRef<any>();
export const navigate = <T extends unknown>(
  screen: IScreen | string,
  params?: T,
) => {
  let screenToNavigate: IScreen;
  if (typeof screen === 'string') {
    screenToNavigate = getScreenByName(screen as string);
  } else {
    screenToNavigate = screen as IScreen;
  }

  if (screenToNavigate)
    if (navigationRef && navigationRef.isReady()) {
      if (isAllowedToNavigate(screenToNavigate)) {
        navigationRef.navigate(screenToNavigate.screen, params);
      } else {
        navigate<ILoginParam>(screens.Login, {screenToNavigate, params});
      }
    }
};

export const isAllowedToNavigate = (screen: IScreen) => {
  return screen.isAuthenticationRequired === true ? isAuthenticated() : true;
};

export const toggleDrawer = () => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(DrawerActions.toggleDrawer());
  }
};

export const getParams = <T extends unknown>(): T | undefined => {
  if (navigationRef.isReady()) {
    const route = navigationRef.getCurrentRoute();
    return route?.params as T;
  }

  return undefined;
};

export const getScreenByName = (name: string): IScreen => {
  let screenName: string = name;
  let propertyNames = getPropertyNames(screens);

  if (isNullOrEmpty(screenName) || !propertyNames.includes(screenName)) {
    screenName = screens.Default;
  }

  let propertyName = propertyNames.find(
    m =>
      typeof screens[m] !== 'string' &&
      (screens[m] as IScreen).screen === screenName,
  );

  return screens[propertyName as string] as IScreen;
};

export const goBack = () => {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
};

export const canGoBack = () => {
  return navigationRef.isReady() && navigationRef.canGoBack();
};
