# Interactive Map Component Integration Guide

## Overview
The `InteractiveMap` component provides a custom map-like visualization for job search radius selection without requiring external APIs or internet connectivity.

## Component Location
- **Main Component**: `app/components/common/interactive-map.component.tsx`
- **Example Usage**: `app/components/common/interactive-map-example.tsx`

## Features
✅ **Visual Map Interface**: Scrollable/pannable view with grid system  
✅ **Search Radius Visualization**: Semi-transparent circular overlay  
✅ **Interactive Radius Control**: Drag handles and synchronized slider  
✅ **No External Dependencies**: Uses only React Native built-in components  
✅ **Responsive Design**: Works on different screen sizes  
✅ **Theme Integration**: Follows existing Apply4U design patterns  

## Basic Usage

```tsx
import { InteractiveMap } from '../common';

const MyComponent = () => {
  const [radius, setRadius] = useState(20);

  return (
    <InteractiveMap
      initialRadius={radius}
      minRadius={1}
      maxRadius={50}
      onRadiusChange={(newRadius) => setRadius(newRadius)}
      centerLabel="London"
      radiusUnit="miles"
      showGrid={true}
      showSlider={true}
    />
  );
};
```

## Props Interface

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialRadius` | `number` | `20` | Starting radius value |
| `minRadius` | `number` | `1` | Minimum allowed radius |
| `maxRadius` | `number` | `50` | Maximum allowed radius |
| `onRadiusChange` | `(radius: number) => void` | `undefined` | Callback when radius changes |
| `centerLabel` | `string` | `undefined` | Label to show at map center |
| `radiusUnit` | `'miles' \| 'km'` | `'miles'` | Unit for radius display |
| `mapSize` | `{width: number, height: number}` | Auto-calculated | Custom map dimensions |
| `showGrid` | `boolean` | `true` | Show/hide grid lines |
| `showSlider` | `boolean` | `true` | Show/hide slider control |

## Integration with Existing Registration Step 4

To integrate with the existing `NewRegisterStepFourView`, you can:

### Option 1: Replace the dropdown with InteractiveMap

```tsx
// In new-register-step-four.view.tsx
import { InteractiveMap } from '../../common';

// Replace the radius dropdown section with:
<View style={styles.mapContainer}>
  <InteractiveMap
    initialRadius={20}
    minRadius={1}
    maxRadius={50}
    onRadiusChange={(newRadius) => {
      setRadius(`${newRadius} Miles`); // Keep existing state format
    }}
    centerLabel={location}
    radiusUnit="miles"
    showGrid={true}
    showSlider={true}
  />
</View>
```

### Option 2: Add as an alternative option

```tsx
// Add a toggle to switch between traditional dropdown and interactive map
const [useInteractiveMap, setUseInteractiveMap] = useState(false);

// Then conditionally render:
{useInteractiveMap ? (
  <InteractiveMap {...props} />
) : (
  // Existing dropdown code
)}
```

## Styling Customization

The component uses the existing theme system. Key style classes:

- `mapContainer`: Main map area styling
- `radiusCircle`: Circular overlay appearance
- `radiusHandle`: Drag handle styling
- `controlsContainer`: Bottom controls area

## Technical Notes

- Uses `react-native-gesture-handler` for smooth drag interactions
- Uses `@react-native-community/slider` for slider control
- Implements `PanResponder` for radius circle dragging
- Uses `Animated.View` for smooth radius changes
- Grid system provides visual geographic reference

## Performance Considerations

- Grid lines are rendered efficiently using absolute positioning
- Animations use native driver where possible
- Touch targets are optimized for mobile interaction
- Component re-renders are minimized through proper state management

## Testing

To test the component:

1. Import `InteractiveMapExample` in any screen
2. The example shows all features and integration patterns
3. Test on different screen sizes and orientations
4. Verify smooth drag interactions and slider synchronization

## Future Enhancements

Potential improvements that could be added:

- Location search integration with existing `LocationAutoComplete`
- Save/load location preferences
- Multiple location pins support
- Custom map themes/colors
- Accessibility improvements
- Haptic feedback for interactions
