import {IAction} from './action.interface';
import {FavJobsConstant} from './action.constant';

export const setFavJobsAction = (payload: number[]): IAction<number[]> => {
  return {
    type: FavJobsConstant.SET_FAV_JOBS,
    payload,
  };
};

export const addToFavJobsAction = (payload: number): IAction<number> => {
    return {
      type: FavJobsConstant.ADD_FAV_JOB,
      payload,
    };
  };

  export const removeFromFavJobsAction = (payload: number): IAction<number> => {
    return {
      type: FavJobsConstant.REMOVE_FAV_JOB,
      payload,
    };
  };
