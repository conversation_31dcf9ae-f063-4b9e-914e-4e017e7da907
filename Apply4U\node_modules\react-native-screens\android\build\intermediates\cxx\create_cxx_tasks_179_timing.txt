# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 168ms
create_cxx_tasks completed in 172ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-ARMEABI_V7A-model 40ms
    create-ARM64_V8A-model 10ms
    create-X86_64-model 11ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 159ms
create_cxx_tasks completed in 163ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 14ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 11ms
    [gap of 24ms]
    create-variant-model 11ms
    create-ARMEABI_V7A-model 10ms
    [gap of 18ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 112ms
create_cxx_tasks completed in 117ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 52ms]
    create-variant-model 22ms
    [gap of 32ms]
  create-initial-cxx-model completed in 106ms
create_cxx_tasks completed in 110ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 62ms]
    create-X86-model 17ms
  create-initial-cxx-model completed in 86ms
create_cxx_tasks completed in 89ms

