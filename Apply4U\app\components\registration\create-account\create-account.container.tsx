import React from 'react';
import { CreateAccountView } from './create-account.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const CreateAccountContainer = () => {
  return <CreateAccountView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedCreateAccountContainer = connect(
  mapStateToProps,
  null,
)(CreateAccountContainer);

export { connectedCreateAccountContainer as CreateAccountContainer };
