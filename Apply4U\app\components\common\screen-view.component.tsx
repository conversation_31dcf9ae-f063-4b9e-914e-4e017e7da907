import React from 'react';
import {SafeAreaView, StatusBar, StyleSheet, View} from 'react-native';

import {ITheme, useCurrentTheme} from '../../theme';
import NavigationBar from 'react-native-navbar-color';

const STATUSBAR_HEIGHT = StatusBar.currentHeight;
const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    root: {
      backgroundColor: theme.palette.primary,
      flex: 1,
    },
    statusBar: {
      height: STATUSBAR_HEIGHT,
    },
  });

  const colors = {primaryColor:theme.palette.primary};
  return {...styles,...colors};
};

export interface ScreenViewProps {
  children?: React.ReactNode;
}

export const ScreenView = React.memo<ScreenViewProps>(props => {
  const styles = useCurrentTheme(createStyles);

  const MyStatusBar = () => (
    <View style={[styles.statusBar, { backgroundColor:styles.primaryColor }]}>
      <SafeAreaView>
        <StatusBar translucent backgroundColor={styles.primaryColor} barStyle={'default'} />
      </SafeAreaView>
    </View>
  );

  React.useEffect(() => {
    NavigationBar.setColor(styles.primaryColor);
    NavigationBar.setStatusBarTheme('default');
  },[]);
  
  return <View style={styles.root}>
    <MyStatusBar />
    {props.children}
    </View>;
});
