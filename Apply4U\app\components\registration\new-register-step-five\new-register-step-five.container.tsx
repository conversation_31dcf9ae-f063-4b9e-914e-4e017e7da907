import React from 'react';
import { NewRegisterStepFiveView } from './new-register-step-five.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterStepFiveContainer = () => {
  return <NewRegisterStepFiveView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterStepFiveContainer);

export { connectedNewRegisterContainer as NewRegisterStepFiveContainer };
