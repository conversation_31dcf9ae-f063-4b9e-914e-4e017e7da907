import React from 'react';
import {StyleSheet, View, useWindowDimensions} from 'react-native';
import {Text, Image} from '.';
import {unReadMarker} from '../../assets';
import {ITheme, useCurrentTheme, useTheme} from '../../theme';
import RenderHtml from 'react-native-render-html';
import { timeAgo } from '../../utility';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';

interface IProps {
  descriptionHtml:string;
  recordedAt:Date;
  isViewed:boolean;
  notificationId:number;
  markAsRead:(notificationId:number) => void;
  deleteNotification:(notificationId:number) => void;
}

const Notify = (props: IProps) => {
  const {width} = useWindowDimensions();
  const styles = useCurrentTheme(createStyles);

  return (
    <View style={styles.row}>
      <View style={[styles.textCol]}>
        <RenderHtml
                contentWidth={width}
                enableCSSInlineProcessing={true}
                source={{html: props.descriptionHtml ?? ''}}
              />
        <Text styles={styles.timeText} numberOfLines={1} text={timeAgo(props.recordedAt)} />
      </View>
      <View style={{flex:1,flexDirection:'column'}}>
        <View style={{flex:1}}>
          <TouchableOpacity onPress={() => {props.deleteNotification(props.notificationId);}} style={{marginRight:2,marginTop:2}}>
            <FontAwesomeIcon style={{alignSelf:'flex-end'}} color={'gray'} size={15} icon={faTimes} />
          </TouchableOpacity>
        </View>
        <View style={{flex:8}}>
          {!props.isViewed && <TouchableOpacity onPress={() => {props.markAsRead(props.notificationId);}} style={{marginTop:8, alignSelf:'flex-start',justifyContent:'center',width:20,height:20}}><Image style={{width:16,height:16,resizeMode:'contain'}} source={unReadMarker} /></TouchableOpacity>}
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    row: {
      width: '95%',
      flexDirection: 'row',
      backgroundColor: '#fff',
      alignSelf:'center',
      //paddingHorizontal:theme.spacing(5),
      marginTop:theme.spacing(2),
      minHeight:70,
      borderRadius:theme.borderRadius(3),
      borderLeftWidth:5,
      borderLeftColor:theme.palette.primary,
    },
    textCol: {
      flex:10,
      marginTop: theme.spacing(5),
      marginBottom:theme.spacing(5),
      paddingHorizontal:theme.spacing(5)
    },
    notifyText: {
      color: '#333',
      fontSize: 13,
      fontWeight: 'bold',
      width:'95%',
    },
    timeText: {
      color: '#5d5d5d',
      fontSize: 12,
    },
  });

  const colors = {primary:theme.palette.primary,secondary:theme.palette.secondary,lightGray:theme.palette.lightGray}
  return {...styles,...colors};
};

export {Notify};
