import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Image as RNImage,
  ImageSourcePropType,
  Animated,
} from 'react-native';
import { Text } from 'react-native-paper';
import { ITheme, useCurrentTheme } from '../../theme';

interface IProps {
  
  image?: ImageSourcePropType;
  text?: string;
  containerStyle?: any;
  imageStyle?: any;
  textStyle?: any;
  imageSize?: number;
  floating?: boolean;
}

export const A4UHuma = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const {
    image,
    text,
    containerStyle,
    imageStyle,
    textStyle,
    imageSize = 80,
    floating = false,
  } = props;

  const floatAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (floating) {
      const startFloating = () => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(floatAnim, {
              toValue: 1,
              duration: 2000,
              useNativeDriver: true,
            }),
            Animated.timing(floatAnim, {
              toValue: 0,
              duration: 2000,
              useNativeDriver: true,
            }),
          ])
        ).start();
      };

      startFloating();
    }
  }, [floating, floatAnim]);

  return (
    <View style={[styles.container, containerStyle]}>
      {image && floating ? (
        <Animated.View
          style={{
            transform: [
              {
                translateY: floatAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -8],
                }),
              },
            ],
          }}
        >
          <RNImage
            source={image}
            style={[
              styles.image,
              { width: imageSize, height: imageSize },
              imageStyle
            ]}
            resizeMode="contain"
          />
        </Animated.View>
      ) : image ? (
        <RNImage
          source={image}
          style={[
            styles.image,
            { width: imageSize, height: imageSize },
            imageStyle
          ]}
          resizeMode="contain"
        />
      ) : null}

      {text && (
        <Text style={[styles.text, textStyle]}>
          <Text style={styles.customComma}>"</Text>{text}<Text style={styles.customComma}>"</Text>
        </Text>
      )}
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  return StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    image: {
      marginRight: 16,
    },
    text: {
      flex: 1,
      fontSize: 16,
      color: '#333',
      fontFamily: 'Poppins-Regular',
    },
    customComma: {
      fontFamily: 'Poppins-SemiBold',
      color: '#007CFF',
      fontSize: 16,
    },
  });
};
