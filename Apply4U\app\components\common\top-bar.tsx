import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {connect} from 'react-redux';

import {menuIcon, transparentLogo, defaultProfileImage} from '../../assets';
import {IUserDetail} from '../../interfaces';
import {IApplicationState} from '../../redux';
import {ITheme, useCurrentTheme, useTheme} from '../../theme';
import {toggleDrawer} from '../../utility';
import {Button, Image} from '.';
import {ApiBaseUrl} from '../../app.constant';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';

interface IProps {
  loginUser: IUserDetail | null;
}

const topBarAreEqual = (prevProps:IProps, nextProps:IProps) => {
  return (
    prevProps.loginUser?.Id == nextProps.loginUser?.Id
  );
}

const TopBar = React.memo((props: IProps) => {
  const styles = useCurrentTheme(createStyles);

  return (
    <View style={styles.headerContainer}>
      <View style={styles.row}>
        <View style={[styles.box, styles.box2]}>
          {!!props.loginUser?.UserProfile?.ProfileImagePath && (
            <Image
              defaultSource={defaultProfileImage}
              source={{
                uri: `${ApiBaseUrl}${props.loginUser.UserProfile.ProfileImagePath}`,
              }}
              style={styles.profileImgStyle}
            />
          )}
          {!props.loginUser?.UserProfile?.ProfileImagePath && (
            <Image
              source={transparentLogo}
              style={styles.profileImgApply4UStyle}
            />
          )}
        </View>

        <View style={[styles.box, styles.box2, styles.two]}>
          {!!props?.loginUser?.UserProfile && (
            <Text style={styles.userName}>
              Hi, {props.loginUser.UserProfile?.FirstName}
            </Text>
          )}
          {!props?.loginUser?.UserProfile && (
            <Text style={styles.userName}>{'Apply4U'}</Text>
          )}
        </View>

        <View style={[styles.box]}>
          <Button pressed={toggleDrawer}>
          <FontAwesomeIcon style={{alignSelf:'flex-end',marginRight:7}} icon={faBars} size={30} color={'white'} />
          </Button>
        </View>
      </View>
    </View>
  );
},topBarAreEqual);

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    headerContainer: {
      //flex: 0.087,
      flexDirection: 'row',
      height: theme.spacing(30),
      justifyContent: 'flex-start', //replace with flex-end or center
    },
    row: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    box: {
      flex: 1,
      height: theme.spacing(30),
      backgroundColor: theme.palette.primary,
    },
    box2: {
      backgroundColor: theme.palette.primary,
    },
    two: {
      flex: 4,
    },
    menuImg: {
      alignSelf: 'flex-end',
      width: 40,
      height: 40,
      marginLeft: theme.spacing(2),
      marginRight: theme.spacing(5),
    },
    profileImgStyle: {
      width: theme.spacing(20),
      height: theme.spacing(20),
      marginLeft: theme.spacing(5),
      marginTop: theme.spacing(5),
      borderRadius: 40,
    },
    profileImgApply4UStyle: {
      width: theme.spacing(20),
      height: theme.spacing(20),
      marginLeft: theme.spacing(5),
      marginTop: theme.spacing(5),
    },
    userName: {
      fontSize: 16,
      color: '#fff',
      fontWeight: 'bold',
      marginTop: 18,
    },
  });
  return styles;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser: state.loginUser,
  };
};

const connectedTopBar = connect(mapStateToProps)(TopBar);
export {connectedTopBar as TopBar};
