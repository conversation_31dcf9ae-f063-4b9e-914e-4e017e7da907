import {http} from './http-base';
import {IEnum, IUserDetail, IUserPackage, IUserStat} from '../interfaces';
import {ISector} from '../interfaces';

export const getJobTypes = async (): Promise<IEnum[]> => {
  const requestUrl = 'api/MasterData/JobTypes';

  const result = await http
    .get<IEnum[]>(requestUrl)
    .catch(error => Promise.reject(error));
  return result.data;
};

export const getJobCategories = async (): Promise<IEnum[]> => {
  const requestUrl = 'api/MasterData/JobCategories';

  const result = await http
    .get<IEnum[]>(requestUrl)
    .catch(error => Promise.reject(error));
  return result.data;
};

export const getSalaryDuration = async (): Promise<IEnum[]> => {
  const requestUrl = 'api/MasterData/Durations';

  const result = await http
    .get<IEnum[]>(requestUrl)
    .catch(error => Promise.reject(error));
  return result.data;
};

export const getSectors = async (): Promise<ISector[]> => {
  const requestUrl = 'api/MasterData/Sectors';

  const result = await http
    .get<ISector[]>(requestUrl)
    .catch(error => Promise.reject(error));
  return result.data;
};
