import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Keyboard,
  View,
  Alert,
  Image,
  Animated,
} from 'react-native';
import {
  Text,
  Button,
} from 'react-native-paper';
import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { BlueheadContainer, A4UHuma, CVProcessingAnimation } from '../../common';
import { uploadIcon, humaLikeThumb, a4uHuma } from '../../../assets';
import { IChooseFileResult } from '../../../interfaces';
import DocumentPicker from 'react-native-document-picker';

export const CvUploadView = () => {
  const styles = useCurrentTheme(createStyles);
  const [cvFile, setCvFile] = useState<IChooseFileResult | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [showProcessingAnimation, setShowProcessingAnimation] = useState(false);



  const handleSubmit = () => {
    Keyboard.dismiss();
    if (!cvFile) {
      Alert.alert(
        'No File Selected',
        'Please select a CV file to upload before submitting.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    const maxSize = 5 * 1024 * 1024;
    if (cvFile.size && cvFile.size > maxSize) {
      Alert.alert(
        'File Too Large',
        'Please select a CV file smaller than 5MB.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (cvFile.type && !allowedTypes.includes(cvFile.type)) {
      Alert.alert(
        'Invalid File Type',
        'Please select a PDF, Word document, or text file.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    setShowProcessingAnimation(true);
  };

  const handleProcessingComplete = () => {
    setShowProcessingAnimation(false);

    navigate(screens.SuccessScreen, {
      title: 'Great Work!',
      message: 'I have processed your CV!',
      message2: 'I have extracted your information for you \n please click next to verify ',
      buttonText: 'Next',
      navigateTo: screens.NewRegisterStepOne,
      customImage: humaLikeThumb,
      showCheckmark: false,
      checkmarkPosition: 'center'
    });
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepOne);
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterIntro);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  const formatFileSize = (bytes: number | null): string => {
    if (!bytes) return '0 KB';

    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };


  const getFileTypeDisplay = (type: string | null): string => {
    if (!type) return 'Unknown';

    switch (type) {
      case 'application/pdf':
        return 'PDF Document';
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return 'Word Document';
      case 'text/plain':
        return 'Text File';
      default:
        return type.split('/')[1]?.toUpperCase() || 'Document';
    }
  };

  // Alternative direct file picker function
  const handleBrowseFileDirectly = async () => {
    try {
      setIsUploading(true);

      const result = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.pdf,
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.plainText,
        ],
        allowMultiSelection: false,
      });

      if (result && result.length > 0) {
        const selectedFile = result[0];

        const maxSize = 5 * 1024 * 1024;
        if (selectedFile.size && selectedFile.size > maxSize) {
          Alert.alert(
            'File Too Large',
            'Please select a file smaller than 5MB.',
            [{ text: 'OK' }]
          );
          setIsUploading(false);
          return;
        }

  
        const cvFileData: IChooseFileResult = {
          path: selectedFile.uri,
          name: selectedFile.name || 'Unknown File',
          size: selectedFile.size || 0,
          type: selectedFile.type || 'unknown',
        };

        setCvFile(cvFileData);

        // Alert.alert(
        //   'Success',
        //   `Successfully selected: ${selectedFile.name}`,
        //   [{ text: 'OK' }]
        // );
      }
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        // User cancelled the picker
      } else {
        Alert.alert(
          'Error',
          'Failed to select file. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setIsUploading(false);
    }
  };

  const handleBrowseFile = async () => {
    await handleBrowseFileDirectly();
  };

  return (
    <BlueheadContainer
      showBackButton={true}
      showHomeButton={true}
      onHomePress={handleGoHome}
      onBackPress={handleGoBack}
    >
      <View style={styles.container}>
        <View style={styles.contentContainer}>

        {!showProcessingAnimation && (
          <A4UHuma
            image={a4uHuma}
            text="Upload your cv and let me help you fill in your details"
            containerStyle={styles.humaContainer}
            imageSize={60}
            textStyle={styles.humaText}
            floating={true}
          />
        )}

          <View style={styles.uploadContainer}>
            {!cvFile ? (
              <>
                <View style={styles.cloudIconContainer}>
                  <Image
                    source={uploadIcon}
                    style={styles.cloudIcon}
                  />
                </View>
                <Text style={styles.dragDropText}>
                  Drag and drop your CV here
                </Text>
                <Button
                  mode="outlined"
                  onPress={handleBrowseFile}
                  disabled={isUploading}
                  buttonColor="#D9D9D9"
                  textColor="#0E1C5D"
                  style={styles.browseButtonStyle}
                  contentStyle={styles.browseButtonContent}
                  labelStyle={styles.browseButtonText}
                >
                  {isUploading ? 'Selecting File...' : 'Browse file'}
                </Button>
              </>
            ) : (

              <View style={styles.selectedFileContainer}>
                <Text style={styles.selectedFileTitle}>Selected File:</Text>
                <Text style={styles.selectedFileName}>{cvFile.name}</Text>
                <Text style={styles.selectedFileSize}>{formatFileSize(cvFile.size)}</Text>
                <Text style={styles.selectedFileType}>{getFileTypeDisplay(cvFile.type)}</Text>

                <View style={styles.fileActionButtons}>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      Alert.alert(
                        'Remove File',
                        'Are you sure you want to remove this file?',
                        [
                          { text: 'Cancel', style: 'cancel' },
                          {
                            text: 'Remove',
                            style: 'destructive',
                            onPress: () => setCvFile(null)
                          }
                        ]
                      );
                    }}
                    disabled={isUploading}
                    buttonColor="transparent"
                    textColor="#FF5252"
                    style={styles.removeButton}
                    contentStyle={styles.removeButtonContent}
                    labelStyle={styles.removeButtonText}
                  >
                    Remove
                  </Button>

                  <Button
                    mode="contained"
                    onPress={handleBrowseFile}
                    disabled={isUploading}
                    buttonColor="#0E1C5D"
                    textColor="#FFFFFF"
                    style={styles.changeButton}
                    contentStyle={styles.changeButtonContent}
                    labelStyle={styles.changeButtonText}
                  >
                    {isUploading ? 'Selecting...' : 'Change'}
                  </Button>
                </View>
              </View>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={handleSkip}
            style={styles.skipButton}
            labelStyle={styles.skipButtonText}
          >
            Skip
          </Button>

          <View style={styles.spacer} />

          <Button
            mode="contained"
            onPress={handleSubmit}
            disabled={showProcessingAnimation}
            style={[
              styles.submitButton,
              showProcessingAnimation && styles.submitButtonDisabled
            ]}
            labelStyle={styles.submitButtonText}
          >
            {showProcessingAnimation ? 'Processing...' : 'Submit'}
          </Button>
        </View>
      </View>

      <CVProcessingAnimation
        visible={showProcessingAnimation}
        onComplete={handleProcessingComplete}
      />
    </BlueheadContainer>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    contentContainer: {
      width: '100%',
      alignItems: 'flex-start',
      flex: 1,
    },

    humaContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 15,
      paddingHorizontal: 0,
      width: '100%',
    },
    humaImage: {
      width: 60,
      height: 60,
      marginRight: 16,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
      flex: 1,
      marginLeft: 10,
      lineHeight: 20,
    },
    customComma: {
      fontFamily: 'Poppins-SemiBold',
      color: '#007CFF',
      fontSize: 14,
    },


    uploadContainer: {
      width: '100%',
      height: 280,
      borderWidth: 4,
      borderColor: '#CCCCCC',
      borderStyle: 'dashed',
      borderRadius: 15,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 60,
      marginBottom: 30,
      backgroundColor: '#FFFFFF',
      padding: 30,
      alignSelf: 'center',
    },
    cloudIconContainer: {
      width: 70,
      height: 70,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
    },
    cloudIcon: {
      width: 75,
      height: 75,
      resizeMode: 'contain',
      tintColor: '#0E1C5D',
    },
    dragDropText: {
      fontSize: 16,
      fontFamily: 'Poppins-Regular',
      color: '#0E1B5D',
      marginBottom: 15,
      textAlign: 'center',
      flexWrap: 'wrap',
      flex: 1,
    },
    browseButtonContent: {
      // height: 48,
      // paddingHorizontal: 20,
    },
    browseButtonStyle: {
      borderRadius: 5,
      borderColor: '#DDDDDD',
      marginTop: 10,
    },
    browseButtonText: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      textTransform: 'none',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      marginTop: 'auto',
      marginBottom: 20,
    },
    spacer: {
      flex: 1,
    },
    skipButton: {
      width: '32%',
      backgroundColor: '#D9D9D9',
      borderColor: '#DDDDDD',
      borderRadius: 5,

    },

    skipButtonText: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      color: '#0E1B5D',
      textTransform: 'none',
    },
    submitButton: {
      width: '32%',
      backgroundColor: '#0E1B5D',
      borderRadius: 5,
    },
    submitButtonText: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      color: '#FFFFFF',
      textTransform: 'none',
    },
    submitButtonDisabled: {
      backgroundColor: '#CCCCCC',
      opacity: 0.7,
    },
    selectedFileContainer: {
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
      flex: 1,
    },
    selectedFileTitle: {
      fontSize: 16,
      fontFamily: 'Poppins-SemiBold',
      color: '#0E1C5D',
      marginBottom: 10,
      textAlign: 'center',
    },
    selectedFileName: {
      fontSize: 14,
      fontFamily: 'Poppins-Medium',
      color: '#333333',
      marginBottom: 4,
      textAlign: 'center',
    },
    selectedFileSize: {
      fontSize: 12,
      fontFamily: 'Poppins-Regular',
      color: '#666666',
      marginBottom: 4,
      textAlign: 'center',
    },
    selectedFileType: {
      fontSize: 11,
      fontFamily: 'Poppins-Regular',
      color: '#888888',
      marginBottom: 25,
      textAlign: 'center',
    },
    fileActionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    removeButtonContent: {
      // height: 48,
    },
    removeButtonText: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      textTransform: 'none',
    },
    removeButton: {
      flex: 1,
      marginRight: 5,
      borderRadius: 5,
      borderColor: '#FF5252',
    },
    changeButtonContent: {
      // height: 48,
    },
    changeButtonText: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      textTransform: 'none',
    },
    changeButton: {
      flex: 1,
      marginLeft: 5,
      borderRadius: 5,
    },
  });

  return styles;
};

