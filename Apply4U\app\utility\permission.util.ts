import React from 'react';
import {Permission, PermissionsAndroid} from 'react-native';
export const requestSinglePermission = async (
  permission: Permission,
  title: string,
  message: string,
) => {
  try {
    const granted = await PermissionsAndroid.request(permission, {
      title: title,
      message: message,
      buttonNegative: 'Deny',
      buttonPositive: 'OK',
    });
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      return true;
    } else {
      return false;
    }
  } catch (err) {
    return false;
  }
};
