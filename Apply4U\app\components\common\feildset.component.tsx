import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';

interface IProps {
  children: any;
  title: string;
}
export const FeildSet = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  return (
    <View style={styles.fieldSet}>
      <Text style={styles.legend}>{props.title}</Text>
      <View style={styles.contentContainer}>{props.children}</View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    fieldSet: {
      width: '100%',
      marginTop: 20,
      paddingHorizontal: 10,
      paddingBottom: 10,
      paddingTop: 10,
      borderRadius: 5,
      borderWidth: 1,
      borderColor: theme.palette.lightGray,
    },
    contentContainer: {},
    legend: {
      ...theme.typography.normal.small,
      color: theme.palette.black,
      position: 'absolute',
      top: -12,
      left: 10,
      paddingLeft: theme.spacing(2),
      paddingRight: theme.spacing(2),
      backgroundColor: '#edf1fc',
    },
  });

  return styles;
};
