import { numberOfRecentSearchesToSave } from '../../app.constant';
import {RecentSearchesConstant} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {initialState} from '../initial-state';

export const RecentSearchesReducer = (
  state: string[] = initialState.recentSearches,
  action: IAction<string[]> | IAction<string>,
) => {
  switch (action.type) {
    case RecentSearchesConstant.SET_RECENT_SEARCHES:{
        const payload = action.payload as string[] ?? initialState.recentSearches;
        let newState: string[] = [...payload];
        return newState;
    }
    case RecentSearchesConstant.ADD_RECENT_SEARCH:{
        const recentSearch:string = action.payload as string;
        if(!!state && state.indexOf(recentSearch) >= 0){
            return state;
        }else{
            let newState = !!state ? [...state] : [];

            if(!!state && state.length >= numberOfRecentSearchesToSave){
                newState = state.slice(0,numberOfRecentSearchesToSave - 1);
            }

            const stateToReturn = [recentSearch, ...newState];
            return stateToReturn;
        }
    }
    case RecentSearchesConstant.REMOVE_RECENT_SEARCH:{
        const recentSearch:string = action.payload as string;
        if(!!state && state.indexOf(recentSearch) >= 0){
            const newState = state.filter(m => m != recentSearch);
            return newState;
        }else{
            return state;
        }
    }
    default:
      return state;
  }
};
