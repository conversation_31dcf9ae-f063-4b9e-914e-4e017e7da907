import {ILocation} from '../location/location.interface';
import {IEnum} from '../enum.interface';
import {IJobSectorLink} from './job-sector-link.interface';
export interface IJob {
  JobNumber: number;
  Id: number;
  Title: string;
  Description: string;
  PostedByUserId: number;
  JobCategoryId: number;
  JobTypeIds: string;
  JobDurationId: number;
  PriorityTypeId: number;
  SalaryCurrencyId: any;
  JobSourceId: number;
  JobStatusId: number;
  LocationId: number;
  LocationText: string;
  IndustrySectorText: string;
  IsFullTime: boolean;
  SalaryFrom: number;
  SalaryTo: number;
  SalaryBenefits?: string;
  PostedDate: string;
  DisplayStartDate: Date;
  DisplayEndDate: Date;
  ContactEmail: any;
  IsEligibleToWorkInUK: boolean;
  IsEligibleToWorkInEU: boolean;
  SkillTags: any;
  ApplicationURL: string;
  LogoUrl: string;
  JobReference: any;
  IndexerStatus: number;
  Location: ILocation;
  Longitude: number;
  PostedByUser: any;
  Latitude: number;
  JobCategory: IEnum;
  JobTypes: IEnum[];
  Duration: IEnum;
  Priority: IEnum;
  Currency: any;
  JobSource: IEnum;
  JobStatistics: any;
  JobSectorLinks: IJobSectorLink[];
  IsExpired: boolean;
  JobStatus: IEnum;
  CompanyName: any;
  CompanyLogoUrl: any;
  JobApplication: any;
  Company: any;
  UserFollowing: any;
  UserConnection: any;
  IsShortListed: boolean;
  JobApplicants: any;
  PostedBy: any;
}
