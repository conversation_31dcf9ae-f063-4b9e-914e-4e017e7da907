import {http} from './http-base';
import {IJobSearchResponse, IJobSearchResult, ISearchParam, ISearchResult} from '../interfaces';
import {
  getCommaSeparatedString,
  getQueryParams,
  greaterThanZero,
  hasValues,
  isNullOrUndefined,
  isNullOrWhitespace,
  isNullOrZero,
} from '../utility';
import {anyJobStatusId, anyJobTypeId} from '../app.constant';

const searchJobs = async (
  keywords: string | null,
  location: string | null,
  pageNumber: number,
  pageSize: number,
  hiddenJobIds:number[],
  userId?: number | undefined,
): Promise<IJobSearchResult> => {
  let hiddenJobs:string | null = null;

  if(!!hiddenJobIds && hiddenJobIds.length > 0){
    hiddenJobs = hiddenJobIds.join(',');
  }

  let queryParams = getQueryParams({
    keywords,
    location,
    pageNumber,
    pageSize,
    userId: userId,
    hiddenJobIds:hiddenJobs
  });
  let url: string = `api/search/new/jobs${queryParams}`;
  let result = await http
    .get<IJobSearchResult>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const getJobResults = async (
  searchParam: ISearchParam | null,
  hiddenJobIds:number[],
  userId?: number,
  pageNumber?: number,
  pageSize?: number,
): Promise<IJobSearchResult> => {
  let paramsToPass: any = {};
  if (searchParam) {
    if (!isNullOrWhitespace(searchParam.keywords)) {
      paramsToPass.keywords = searchParam.keywords;
    }

    if (!isNullOrWhitespace(searchParam.locationText)) {
      paramsToPass.location = searchParam.locationText;
    }

    if (!isNullOrZero(searchParam.radius)) {
      paramsToPass.radius = searchParam.radius;
    }

    if (searchParam.sortBy && !isNullOrWhitespace(searchParam.sortBy)) {
      paramsToPass.sortBy = searchParam.sortBy;
    }

    if (
      !isNullOrUndefined(searchParam?.applyFilters) &&
      searchParam.applyFilters === true
    ) {
      if (!isNullOrWhitespace(searchParam.sector?.SectorSeoName)) {
        paramsToPass.sectorName = searchParam.sector?.SectorSeoName;
      }

      if (!isNullOrZero(searchParam.salaryFrom)) {
        paramsToPass.salaryFrom = searchParam.salaryFrom;
      }

      if (!isNullOrZero(searchParam.salaryTo)) {
        paramsToPass.salaryTo = searchParam.salaryTo;
      }

      if (
        searchParam.salaryPer &&
        greaterThanZero(searchParam?.salaryPer?.Id)
      ) {
        paramsToPass.salaryDuration = searchParam.salaryPer.Id;
      }

      if (
        searchParam.selectedJobTypes &&
        hasValues(searchParam.selectedJobTypes)
      ) {
        const jobTypes = searchParam.selectedJobTypes.filter(
          m => m != anyJobTypeId,
        );

        if (hasValues(jobTypes)) {
          paramsToPass.jobTypes = getCommaSeparatedString(jobTypes);
        }
      }

      if (
        searchParam.searchWithinDays &&
        greaterThanZero(searchParam.searchWithinDays.value)
      ) {
        paramsToPass.searchWithInLast = searchParam.searchWithinDays.value;
      }

      if (
        searchParam.selectedJobCategories &&
        hasValues(searchParam.selectedJobCategories)
      ) {
        const categories = searchParam.selectedJobCategories.filter(
          m => m != anyJobStatusId,
        );
        if (hasValues(categories)) {
          paramsToPass.jobStatus = getCommaSeparatedString(categories);
        }
      }
    }
  }

  if (userId && greaterThanZero(userId)) {
    paramsToPass.userId = userId;
  }

  if (greaterThanZero(pageNumber)) {
    paramsToPass.pageNumber = pageNumber;
  }

  if (greaterThanZero(pageSize)) {
    paramsToPass.pageSize = pageSize;
  }

  if(!!hiddenJobIds && hiddenJobIds.length > 0){
    paramsToPass.hiddenJobIds  = hiddenJobIds.join(',')
  }

  const queryParams = getQueryParams(paramsToPass);
  let url: string = `api/search/new/jobs${queryParams}`;
  let result = await http
    .get<IJobSearchResult>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const getJobsByIds = async (
  hiddenJobIds:number[],
  userId?: number
): Promise<IJobSearchResponse[]> => {
  let paramsToPass: any = {};

  if(!!hiddenJobIds && hiddenJobIds.length > 0){
    paramsToPass.ids  = hiddenJobIds.join(',')
  }

  if (userId && greaterThanZero(userId)) {
    paramsToPass.userId = userId;
  }

  const queryParams = getQueryParams(paramsToPass);
  let url: string = `api/Jobs/NewJobs${queryParams}`;
  let result = await http
    .get<IJobSearchResponse[]>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const searchApi = {
  searchJobs,
  getJobResults,
  getJobsByIds
};

export {searchApi};
