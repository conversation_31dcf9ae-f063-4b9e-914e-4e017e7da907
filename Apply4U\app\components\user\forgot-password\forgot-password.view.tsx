import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  ImageBackground,
  SafeAreaView,
} from 'react-native';

import {transparentLogo, background_2} from '../../../assets';

import {useTranslation} from 'react-i18next';
import {ITheme, useCurrentTheme, useTheme} from '../../../theme';
import {Button, Text, InputText} from '../../common';
import {InputType} from '../../../enum';
import { ValidationResults } from '../../../interfaces/validation';
import { IForgotPassword } from '../../../interfaces';

interface IProps{
  email:string;
  isResetEmailSent:boolean;
  validationResults?: ValidationResults<IForgotPassword>;
  setEmail:(email:string) => void;
  backToSignIn:() => void;
  resetPassword:()=>void;
  resetValidation?: (key: keyof IForgotPassword) => void;
  validate?: (key: keyof IForgotPassword) => void;
}
export const ForgotPasswordView = (props:IProps) => {
  const {t} = useTranslation(['ForgotPassword']);
  const styles = useCurrentTheme(createStyles);

  const handleChange = (value: string, name: string) => {
    props.setEmail(value);
  };

  return (
    <ImageBackground source={background_2} style={styles.container}>
      <SafeAreaView>
        <ScrollView
          contentInsetAdjustmentBehavior="automatic"
          keyboardShouldPersistTaps="always"
          keyboardDismissMode="interactive">
          <View style={styles.logoContainer}>
            <Image
              source={transparentLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.loginBox}>
          {props.isResetEmailSent === false && <KeyboardAvoidingView enabled>
              <InputText
                name={'email'}
                inputType={InputType.EmailAddress}
                onChangeText={handleChange}
                placeholder={t('Placeholders.Enter_Email_Address')}
                value={props.email}
                validationResults={
                  props.validationResults?.email?.validationResults
                }
                onBlur={e => {
                  if (props.validate) {
                    props.validate('email');
                  }
                }}
                onFocus={e => {
                  if (props.resetValidation) {
                    props.resetValidation('email');
                  }
                }}
              />

              <Button
                pressed={() => {
                  Keyboard.dismiss();
                  props.resetPassword();
                }}
                text={t('Buttons.Reset_Password')}
              />
            </KeyboardAvoidingView>}

            {props.isResetEmailSent === true && <View>
              <Text styles={styles.instructionsText} text='We have sent instructions to your email to reset password. Please check your email and follow the instructions.' />
            </View>}
          </View>

          <Button
                pressed={() => {
                  Keyboard.dismiss();
                  props.backToSignIn();
                }}
                styles={styles.backToSignInButton}
                text={t('Labels.Back_To_Sign_In')}
              />
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'column',
      flexGrow: 1,
      justifyContent: 'space-between',
    },
    logoContainer: {
      alignItems: 'center',
    },
    appLogo: {
      height: 100,
      width: '50%',
      marginTop: theme.spacing(30),
      marginRight: theme.spacing(20),
      marginBottom: theme.spacing(20),
      marginLeft: theme.spacing(20),
      resizeMode: 'contain',
    },
    loginBox: {
      flex: 1,
      margin: theme.spacing(10),
      marginTop:theme.spacing(30),
      padding: theme.spacing(7.5),
      height: '100%',
      borderRadius: theme.borderRadius(5),
      backgroundColor: theme.backgroundPalette.primary,
    },
    inputView: {
      height: 40,
      marginBottom: theme.spacing(7.5),
      flexDirection: 'row',
    },
    inputStyle: {
      flex: 1,
      borderWidth: 1,
      color: theme.palette.gray,
      paddingLeft: theme.spacing(7.5),
      paddingRight: theme.spacing(7.5),
      borderRadius: theme.borderRadius(15),
      borderColor: theme.palette.lightGray,
      backgroundColor: theme.palette.white,
    },
    textStyle: {
      padding: theme.spacing(5),
      ...theme.typography.bold.small,
      color: theme.palette.white,
      textAlign: 'center',
      alignSelf: 'center',
    },
    instructionsText:{
      ...theme.typography.normal.medium,
      color:theme.palette.black,
    },
    backToSignInButton:{
      ...theme.buttons.secondary,
      width:'90%',
      alignSelf:'center',
    }
  });

  return {...styles};
};
