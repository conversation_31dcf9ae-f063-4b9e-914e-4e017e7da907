import {FavJobsConstant} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {initialState} from '../initial-state';

export const FavJobsReducer = (
  state: number[] = initialState.favJobs,
  action: IAction<number[]> | IAction<number>,
) => {
  switch (action.type) {
    case FavJobsConstant.SET_FAV_JOBS:{
        const payload = action.payload as number[] ?? initialState.favJobs;
        let newState: number[] = [...payload];
        return newState;
    }
    case FavJobsConstant.ADD_FAV_JOB:{
        const jobId:number = action.payload as number;
        if(!!state && state.indexOf(jobId) >= 0){
            return state;
        }else{
            const newState = !!state ? [...state] : [];
            const stateToReturn = newState.concat(jobId);
            return stateToReturn;
        }
    }
    case FavJobsConstant.REMOVE_FAV_JOB:{
        const jobId:number = action.payload as number;
        if(!!state && state.indexOf(jobId) >= 0){
            const newState = state.filter(m => m != jobId);
            return newState;
        }else{
            return state;
        }
    }
    default:
      return state;
  }
};
