{"logs": [{"outputFile": "com.apply4u.app-mergeDebugResources-36:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,237,333,419,493,567,655,727,794,870,949,1037,1123,1195,1276,1361,1437,1519,1602,1679,1752,1825,1910,1984,2064,2134", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,232,328,414,488,562,650,722,789,865,944,1032,1118,1190,1271,1356,1432,1514,1597,1674,1747,1820,1905,1979,2059,2129,2214"}, "to": {"startLines": "35,41,42,46,67,69,70,72,86,87,88,126,127,128,129,131,132,133,134,135,136,137,138,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,3665,3773,4164,7123,7263,7337,7488,8502,8569,8645,11684,11772,11858,11930,12093,12178,12254,12336,12419,12496,12569,12642,12828,12902,12982,13052", "endColumns": "73,107,95,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "3217,3768,3864,4245,7192,7332,7420,7555,8564,8640,8719,11767,11853,11925,12006,12173,12249,12331,12414,12491,12564,12637,12722,12897,12977,13047,13132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3a8a24834f295d8078f18b8298041f7\\transformed\\material-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1179,1270,1336,1399,1487,1549,1616,1674,1745,1804,1858,1972,2032,2095,2149,2222,2341,2427,2510,2649,2734,2821,2909,2966,3017,3083,3155,3231,3321,3394,3471,3552,3626,3716,3795,3886,3982,4056,4137,4232,4286,4352,4439,4525,4587,4651,4714,4821,4913,5011,5103,5164,5219", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,87,56,50,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,60,54,81", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1174,1265,1331,1394,1482,1544,1611,1669,1740,1799,1853,1967,2027,2090,2144,2217,2336,2422,2505,2644,2729,2816,2904,2961,3012,3078,3150,3226,3316,3389,3466,3547,3621,3711,3790,3881,3977,4051,4132,4227,4281,4347,4434,4520,4582,4646,4709,4816,4908,5006,5098,5159,5214,5296"}, "to": {"startLines": "2,36,37,38,39,40,43,44,45,65,66,68,71,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3222,3300,3378,3466,3574,3869,3965,4081,6965,7032,7197,7425,7560,7648,7710,7777,7835,7906,7965,8019,8133,8193,8256,8310,8383,8724,8810,8893,9032,9117,9204,9292,9349,9400,9466,9538,9614,9704,9777,9854,9935,10009,10099,10178,10269,10365,10439,10520,10615,10669,10735,10822,10908,10970,11034,11097,11204,11296,11394,11486,11547,11602", "endLines": "7,36,37,38,39,40,43,44,45,65,66,68,71,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "12,77,77,87,107,90,95,115,82,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,82,138,84,86,87,56,50,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,60,54,81", "endOffsets": "419,3295,3373,3461,3569,3660,3960,4076,4159,7027,7118,7258,7483,7643,7705,7772,7830,7901,7960,8014,8128,8188,8251,8305,8378,8497,8805,8888,9027,9112,9199,9287,9344,9395,9461,9533,9609,9699,9772,9849,9930,10004,10094,10173,10264,10360,10434,10515,10610,10664,10730,10817,10903,10965,11029,11092,11199,11291,11389,11481,11542,11597,11679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19ec269da49c6c76309942cfa013a60d\\transformed\\jetified-play-services-base-17.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,351,546,671,779,992,1119,1260,1390,1617,1721,1914,2037,2243,2411,2507,2594", "endColumns": "105,194,124,107,212,126,140,129,226,103,192,122,205,167,95,86,112", "endOffsets": "350,545,670,778,991,1118,1259,1389,1616,1720,1913,2036,2242,2410,2506,2593,2706"}, "to": {"startLines": "47,48,49,50,51,52,53,54,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4250,4360,4555,4684,4796,5009,5140,5285,5624,5851,5959,6152,6279,6485,6657,6757,6848", "endColumns": "109,194,128,111,212,130,144,133,226,107,192,126,205,171,99,90,116", "endOffsets": "4355,4550,4679,4791,5004,5135,5280,5414,5846,5954,6147,6274,6480,6652,6752,6843,6960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3627ca143232ec2ceb6e319eb18971d\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "424,533,635,743,829,934,1052,1133,1212,1303,1396,1491,1585,1685,1778,1873,1968,2059,2150,2249,2355,2461,2559,2666,2773,2878,3048,12011", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "528,630,738,824,929,1047,1128,1207,1298,1391,1486,1580,1680,1773,1868,1963,2054,2145,2244,2350,2456,2554,2661,2768,2873,3043,3143,12088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13659b94c66de92d98d7f8af61234b95\\transformed\\core-1.9.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "12727", "endColumns": "100", "endOffsets": "12823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4343b2ae69420088cd050005b2aafd9c\\transformed\\jetified-play-services-basement-17.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "204", "endOffsets": "451"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "5419", "endColumns": "204", "endOffsets": "5619"}}]}]}