import React from 'react';
import {StyleSheet, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../../theme';
import {Button, MainLayout, Text} from '../../common';
import {faCheck} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
interface IProps {
  backToHome: () => void;
  goToSimilarJobs: () => void;
}

export const ApplySuccessfulView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  return (
    <MainLayout>
      <View style={styles.container}>
        <View style={styles.contentView}>
          <View style={styles.messageContainerView}>
            <View style={styles.messageContainerInnerView}>
              <View style={styles.messageView}>
                <View
                  style={{
                    width: 200,
                    height: 130,
                    backgroundColor: 'white',
                    justifyContent: 'center',
                    alignSelf: 'center',
                    borderRadius: 10,
                  }}>
                  <Text
                    styles={styles.successMessageText}
                    text={'Application Successful'}
                    numberOfLines={1}
                  />
                  <View
                    style={{
                      width: 50,
                      height: 50,
                      backgroundColor: 'green',
                      alignSelf: 'center',
                      marginTop: 10,
                      borderRadius: 50,
                      justifyContent: 'center',
                    }}>
                    <FontAwesomeIcon
                      style={{alignSelf: 'center'}}
                      size={30}
                      color="white"
                      icon={faCheck}
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.buttonsContainerView}>
            <Button
              styles={styles.similarJobsButton}
              text={'Similar Jobs'}
              pressed={props.goToSimilarJobs}
            />
            <Button
              styles={styles.backToHomeButton}
              text={'Back To Home'}
              pressed={props.backToHome}
            />
          </View>
        </View>
      </View>
    </MainLayout>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    contentView: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'center',
    },
    messageContainerView: {
      height: 200,
    },
    buttonsContainerView: {
      height: 200,
      paddingHorizontal: theme.spacing(10),
    },
    messageContainerInnerView: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    messageView: {
      flex: 1,
      justifyContent: 'center',
    },
    successMessageText: {
      ...theme.typography.normal.small,
      color: theme.palette.black,
      alignSelf: 'center',
      paddingBottom: 10,
    },
    backToHomeButton: {
      width: '100%',
      borderRadius: theme.borderRadius(2),
    },
    similarJobsButton: {
      ...theme.buttons.secondary,
      width: '100%',
      borderRadius: theme.borderRadius(2),
    },
  });

  return styles;
};
