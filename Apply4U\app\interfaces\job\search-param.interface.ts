import {IDropDownDataSource, IEnum, IAutoComplete, ISector} from '..';

export interface ISearchParam {
  keywords?: string;
  locationText?: string;
  locationId?: number;
  selectedLocation?: IAutoComplete;
  radius?: number;
  salaryFrom?: number;
  salaryTo?: number;
  salaryPer?: IEnum;
  selectedJobTypes?: number[];
  selectedJobCategories?: number[];
  sector?: ISector;
  searchWithinDays?: IDropDownDataSource;
  sortBy?: 'Relevancy' | 'Date';
  applyFilters?: boolean;
}
