import React from 'react';
import {Alert} from 'react-native';
import {connect} from 'react-redux';
import {screens} from '../../../app.constant';
import {searchApi} from '../../../http';
import {IJob, IJobDetailParam, IJobSearchResponse, ISearchParam} from '../../../interfaces';
import { IApplicationState } from '../../../redux';
import {hideLoading, showLoading} from '../../../redux/actions';
import {hasValues, navigate} from '../../../utility';
import {HomeView} from './home.view';

interface IProps {
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  hideLoading: () => void;
  hiddenJobs:number[];
}
const HomeContainer = (props: IProps) => {
  const getSearchResults = () => {
    props.showLoading();
    searchApi
      .searchJobs(
        searchParam?.keywords ?? '',
        searchParam?.locationText ?? '',
        1,
        10,
        props.hiddenJobs
      )
      .then(results => {
        if (results && hasValues(results.Response)) {
          setSearchResults(results.Response);
        }
      })
      .catch(e => {})
      .finally(() => {
        props.hideLoading();
      });
  };

  const [searchResults, setSearchResults] = React.useState<IJobSearchResponse[]>([]);
  const [searchParam, setSearchParam] = React.useState<ISearchParam>();

  const handleChange = (value: string, key: keyof ISearchParam) => {
    let newSearchParam: any = {...searchParam};
    newSearchParam[key] = value;
    setSearchParam({...newSearchParam});
  };

  const handleSearch = () => {
    getSearchResults();
  };

  const handleGoToJobDetail = (jobId: number) => {
    navigate<IJobDetailParam>(screens.JobDetail, {jobId});
  };

  const addJobToFav = (jobId: number) => {
  };

  const shareJob = (jobId: number) => {
  };

  React.useEffect(() => {
    getSearchResults();
  }, []);
  return (
    <HomeView
      searchParams={searchParam}
      handleChange={handleChange}
      goToJobDetail={handleGoToJobDetail}
      onFindJobsClick={handleSearch}
      searchResults={searchResults}
      jobAddToFav={addJobToFav}
      shareJob={shareJob}
    />
  );
};

const mapStateToProps = (state:IApplicationState) => {
  return {
    hiddenJobs:state.hiddenJobs
  };
};

const mapDispatchToProps = {
  showLoading,
  hideLoading,
};

const connectedHomeContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(HomeContainer);
export {connectedHomeContainer as HomeContainer};
