import {http, tokenHttp} from './http-base';
import {ITokenResult} from '../interfaces';

const login = async (
  userName: string,
  password: string,
): Promise<ITokenResult> => {
  
  let body = `client_id=MobileApp&client_secret=Apply4U&scope=smartcommunicator transmitter&grant_type=password&username=${encodeURIComponent(userName)}&password=${encodeURIComponent(password)}`;

  let result = await tokenHttp
    .post<any>('connect/token', body)
    .catch(error => Promise.reject(error));
  return result.data;
};

const loginApi = {
  login,
};

export {loginApi};
