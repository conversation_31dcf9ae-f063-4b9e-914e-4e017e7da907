import React from 'react';
import { screens } from '../../../app.constant';
import { ValidationResults, ValidationRules } from '../../../interfaces/validation';
import { navigate, validate, validateAll } from '../../../utility';
import { ForgotPasswordView } from './forgot-password.view';
import {IForgotPassword} from '../../../interfaces';
import { hideLoading, showLoading } from '../../../redux/actions';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';
import {userApi} from '../../../http';

interface IProps{
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  hideLoading: () => void;
}
const ForgotPasswordContainer = (props:IProps) => {
  const [email, setEmail] = React.useState<string>("");
  const [isResetEmailSent, setIsResetEmailSent] = React.useState<boolean>(false);

  const resetPassword = () => {
    let validateAllResult = validateAll<IForgotPassword>(validations, {email});
    if (
      validateAllResult.isValid === false &&
      validateAllResult.validationResults
    ) {
      setValidationResults({...validateAllResult.validationResults});
    }

    if (validateAllResult.isValid) {
      props.showLoading();
      userApi.emailAlreadyExists(email)
      .then((data:any[]) => {
        if(!!data && data.length > 0 && !!data[0] && data[0].Id > 0){
          userApi.resetPassword(email)
          .then(() => {
            setIsResetEmailSent(true);
          })
          .catch((e:any) => {

          })
          .finally(() => {
            props.hideLoading();
          });
        } else{
          props.hideLoading();
          setValidationResults({email: {validationResults: [{isValid:false,message:'Account not found.'}]}})
        }
      })
      .catch(() => {
        props.hideLoading();
      });
    }
  }

  const backToSignIn = () => {
    navigate(screens.Login);
  }

  const validations: ValidationRules<IForgotPassword> = {
    email: {
      validationRules: {
        required: {
          isRequired: true,
          message: 'Email is required.',
        },
        validEmail: {
          checkValidEmail: true,
          message: 'Invalid email address.',
        },
      },
    },
  };

  const [validationResults, setValidationResults] = React.useState<
  ValidationResults<IForgotPassword>
>({email: {validationResults: []}});
const resetValidation = (key: keyof IForgotPassword) => {
  let results = {...validationResults};
  results[key].validationResults = [];
  setValidationResults({...results});
};

const validateIt = (key: keyof IForgotPassword) => {
  let results = validate(email, validations[key]?.validationRules);
  let updatedValidationResults = {...validationResults};
  updatedValidationResults[key].validationResults = results;
  setValidationResults({...updatedValidationResults});
};

  return <ForgotPasswordView
    isResetEmailSent={isResetEmailSent}
    email={email} setEmail={setEmail}
    resetPassword={resetPassword}
    backToSignIn={backToSignIn}
    validate={validateIt}
    resetValidation={resetValidation}
    validationResults={validationResults}
    />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {};
};

const mapDispatchToProps = {
  showLoading,
  hideLoading,
};

const connectedForgotPasswordContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(ForgotPasswordContainer);
export {connectedForgotPasswordContainer as ForgotPasswordContainer};
