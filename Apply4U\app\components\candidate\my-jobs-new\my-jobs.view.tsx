import React from 'react';
import { Keyboard, StyleSheet, View } from 'react-native';
import { useCurrentTheme, ITheme } from '../../../theme';
import { Text, Button, MainLayout, JobCardList, DropDown } from '../../common';
import { getThousandsSeparated } from '../../../utility';
import { IAutoSearch, IJobSearchResponse, ISearchParam } from '../../../interfaces';
import { dropdownPlaceholderValue, zIndex } from '../../../app.constant';
import { TouchableOpacity } from 'react-native-gesture-handler';

interface IProps {
    searchResults: IJobSearchResponse[];
    searchParam?: ISearchParam | null;
    isMoreResultsExists: boolean;
    totalResults: number;
    isLoading: boolean;
    isTopJobsActive: boolean;
    autoSearches: IAutoSearch[];
    selectedAutoSearch: IAutoSearch | undefined;
    showFullText:boolean;
    isKeywordsTextCropped:boolean;
    setIsKeywordsTextCropped:(isCropped:boolean) => void;
    toggleTextVisibility:() => void;
    setTopJobsActive: () => void;
    setAutoSearchActive: (autoSearch:IAutoSearch | undefined) => void;
    favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
    goToJobDetail: (jobId: number) => void;
    loadMore: () => void;
    onHideJob: (jobId: number) => void;
    handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
    handleAutoSearchChange: (aSearch: IAutoSearch) => void;
    editMySavedSearch:() => void;
    createMySavedSearch:() => void;
}

const MyJobsView = (props: IProps) => {
    const styles = useCurrentTheme(createStyles);
    return (
        <MainLayout>
            <View style={styles.header}>
                <Text
                    styles={styles.headerText}
                    text={`My Jobs`}
                    numberOfLines={2}
                />
            </View>

            <View style={styles.keywordsView}>

                <View style={{ flexDirection: 'row' }}>
                    <Button
                        styles={!!props.isTopJobsActive ? styles.btnActive : styles.btn}
                        textStyles={!!props.isTopJobsActive ? styles.btnTextStyleActive : styles.btnFilterTextStyle}
                        pressed={() => {
                            Keyboard.dismiss();
                            props.setTopJobsActive();
                        }} text={'Top Jobs'} />
                    <Button
                        styles={!props.isTopJobsActive ? styles.btnActive : styles.btn}
                        textStyles={!props.isTopJobsActive ? styles.btnTextStyleActive : styles.btnFilterTextStyle}
                        pressed={() => {
                            Keyboard.dismiss();
                            props.setAutoSearchActive({...props.selectedAutoSearch});
                        }} text={'Auto Search'} />
                </View>
            </View>

            {!!props.autoSearches && props.autoSearches.length > 0 && !props.isTopJobsActive && <View style={styles.inputView}>
                <DropDown<IAutoSearch>
                    dataSource={props.autoSearches}
                    mode={'dialog'}
                    name={'autoSearch'}
                    onChange={(
                        value: IAutoSearch | number | string | null,
                        name: string,
                    ) => {
                        let aSearch: IAutoSearch | null = null;
                        if (
                            typeof value !== 'number' &&
                            typeof value !== 'string'
                        ) {
                            aSearch = { ...value };
                        }

                        if (!!aSearch) {
                            props.handleAutoSearchChange(aSearch);
                        }
                    }}
                    placeholder={'Select Auto Search'}
                    value={
                        props.selectedAutoSearch ?? dropdownPlaceholderValue
                    }
                    displayFeild={'KeyWords'}
                    valueFeild={'Id'}
                />
                </View>}

            {!props.isTopJobsActive && !!props.searchResults && props.searchResults.length > 0 && (!!props.searchParam?.keywords && props.searchParam?.keywords.trim() != '') && <View style={{paddingHorizontal:10}}>
            <TouchableOpacity onPress={props.toggleTextVisibility}><Text textCropped={props.setIsKeywordsTextCropped} styles={styles.totalRecordsTextStyle} numberOfLines={props.showFullText ? 500 : 2} >{`${getThousandsSeparated(props.totalResults) + (props.totalResults >= 10000 ? "+" : "")} "${!!props.searchParam?.keywords && props.searchParam?.keywords.trim() != '' ? props.searchParam?.keywords : ''}" jobs${!!props.searchParam.locationText ? ' in '+props.searchParam.locationText : ''}`} {!!props.isKeywordsTextCropped && <TouchableOpacity><Text styles={styles.moreTextStyle} text={props.showFullText ? 'See Less' : ''} /></TouchableOpacity>}</Text></TouchableOpacity>
            </View>}

            {!props.isTopJobsActive && !!props.searchResults && props.searchResults.length > 0 && (!props.searchParam?.keywords || props.searchParam?.keywords.trim() == '') && <View>
                <Text styles={styles.totalRecordsTextStyle} numberOfLines={2} text={`${getThousandsSeparated(props.totalResults)} jobs`} />
            </View>}

            {!props.isTopJobsActive && (props.totalResults <= 0) && !props.isLoading && (!props.searchResults || props.searchResults.length <= 0) && <View>
                <Text styles={styles.totalRecordsTextStyle} numberOfLines={2} text={`no jobs found`} />
            </View>}

            {!props.isTopJobsActive && !!props.searchResults && props.searchResults.length > 0 && (!!props.searchParam?.keywords && props.searchParam?.keywords.trim() != '') && <Button pressed={props.editMySavedSearch} styles={styles.editMySearchButton} text={'Edit My Search'} />}
            {!props.isTopJobsActive && (!props.autoSearches || props.autoSearches.length <= 0) && <Button pressed={props.createMySavedSearch} styles={styles.createMySearch} text={'Create My Search'} />}

            <View style={styles.listContainer}>
                <JobCardList
                    jobResults={props.searchResults}
                    isMoreResultsExists={props.isMoreResultsExists}
                    isSwipeLeftEnabled={true}
                    isSwipeRightEnabled={true}
                    swipeRightText='Hide'
                    isSwipeable={true}
                    showFavIcon={true}
                    showShareIcon={true}
                    goToJobDetail={props.goToJobDetail}
                    handleEasyApply={props.handleEasyApply}
                    favJobsAddOrRemove={props.favJobsAddOrRemove}
                    loadMore={props.loadMore}
                    onSwipeRight={props.onHideJob}
                />
            </View>
        </MainLayout>
    );
};

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        header2: {
            height: 60,
            width: '100%',
            flexWrap: 'wrap',
            flexDirection: 'row',
            backgroundColor: '#5178e1',
        },
        headerButton: {
            width: '40%',
            textAlign: 'right',
        },
        headerCol: {
            width: '55%',
            marginLeft: 10,
        },
        headerText: {
            ...theme.typography.bold.medium,
        },
        btnStyle: {
            fontSize: 11,
            borderRadius: 5,
            color: '#333333',
            backgroundColor: '#fdba4f',
        },
        listContainer: {
            flex: 1,
            marginBottom: 5
        },
        inputField: {
            borderRadius: 6,
        },
        keywordsViewHeight: {
            padding: theme.spacing(7.5),
            width: '100%',
            backgroundColor: theme.palette.primary,
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
            height: '90%',
        },
        keywordsView: {
            //padding: theme.spacing(7.5),
            width: '100%',
            backgroundColor: theme.palette.primary,
            height: 70,
            justifyContent: 'center',
            ...theme.zIndex(zIndex.locationAutoCompleteContainer),
        },
        btn: {
            ...theme.buttons.secondary,
            marginLeft: theme.spacing(6),
            width: '30%',
            backgroundColor: theme.palette.primary,
            borderWidth: 1,
            borderColor: theme.palette.lightGray,
            borderRadius: theme.borderRadius(10),
            height: 35,
        },
        btnActive: {
            ...theme.buttons.secondary,
            marginLeft: theme.spacing(6),
            width: '30%',
            backgroundColor: theme.palette.white,
            borderWidth: 1,
            borderColor: theme.palette.lightGray,
            borderRadius: theme.borderRadius(10),
            height: 35,
        },
        btnMatchingJobs: {
            ...theme.buttons.secondary,
            width: '30%',
            backgroundColor: theme.palette.primary,
            borderWidth: 1,
            borderColor: theme.palette.lightGray,
            borderRadius: theme.borderRadius(10),
            ...theme.zIndex(1),
            marginLeft: theme.spacing(7),
            height: 35,
        },
        btnFilterTextStyle: {
            ...theme.zIndex(1),
        },
        btnTextStyleActive: {
            color: theme.palette.gray
        },
        buttonIcon: {

            flexDirection: 'row',


            alignSelf: 'flex-end',
            ...theme.zIndex(1),
        },
        buttonTextDefault: {
            ...theme.typography.bold.small,
            color: theme.palette.white,
            marginLeft: 20,
        },
        buttonTextMatchingJobs: {
            ...theme.typography.bold.small,
            color: theme.palette.white,
            marginLeft: 20,
            alignSelf: 'flex-start'
        },
        totalRecordsTextStyle: {
            ...theme.typography.bold.medium,
            color: theme.palette.lightGray,
            alignSelf: 'center',
            marginTop: theme.spacing(7),
            marginBottom: theme.spacing(3)
        },
        header: {
            height: 50,
            paddingTop: theme.spacing(10),
            width: '100%',
            flexDirection: 'row',
            backgroundColor: theme.palette.primary,
            justifyContent: 'center',
            alignItems: 'center'
        },
        inputView: {
            marginBottom: theme.spacing(5),
            marginLeft:theme.spacing(5),
            marginRight:theme.spacing(5),
            flexDirection: 'row',
        },
        editMySearchButton:{
            ...theme.buttons.secondary,
            width:135,
            height:35,
            marginLeft:10
        },
        moreTextStyle:{
            ...theme.typography.bold.extraSmall,
            color:theme.palette.blue,
            
        },
        createMySearch:{
            ...theme.buttons.secondary,
            width:'80%',
            alignSelf:'center',
            marginTop:theme.spacing(20),
        }

    });

    const colors = { faColor: theme.palette.white };
    return { ...styles, ...colors };
};

export { MyJobsView };
