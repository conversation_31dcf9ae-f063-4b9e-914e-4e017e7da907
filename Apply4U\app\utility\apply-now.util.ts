import {Linking} from 'react-native';
import {navigate} from '.';
import {JobSource} from '../enum';
import {alreadyAppliedForJob, screens} from '../app.constant';
import {IApplyNowParam, IBrowserParam, IJob, IJobApplication, IJobSearchResponse} from '../interfaces';
import { jobApplicationApi, resumeApi } from '../http';

export const applyNow = (jobId:number | null | undefined,jobSourceId:number,jobTitle:string, applicationUrl:string, easyApply?:(jobId:number) => void) => {
  if (!!jobId) {
    if (jobId > 0) {
      if(!applicationUrl || applicationUrl.trim() == '' || (jobSourceId != JobSource.JobGate && jobSourceId != JobSource.Broadbean && jobSourceId != JobSource.Barchester)){
        if(easyApply){
          easyApply(jobId);
        }else{
          navigate<IApplyNowParam>(screens.ApplyNow, {
            jobId,
            jobTitle
          });
        }
      } else {
        navigateToApplicationUrl(applicationUrl);
      }
    } else {
      navigateToApplicationUrl(applicationUrl);
    }
  } else{
    navigateToApplicationUrl(applicationUrl);
  }
};

const navigateToApplicationUrl = (applicationUrl:string) => {
  if(!!applicationUrl && applicationUrl.trim() != ''){
    //Linking.openURL(applicationUrl).catch(err => {});
    navigate<IBrowserParam>(screens.Browser,{URL:applicationUrl});
  }
}

const getResumeAndPostApplication = (userId:number,jobId:number,error:(message:string) => void,success:(message:string) => void) => {
  resumeApi.getResume(userId).then(
    results => {
      if (results && results.length > 0) {
        let selectedResume = {...results[0]};
        const defaultResume = results.find(m => m.IsDefault == true);
        if (!!defaultResume && defaultResume.Id > 0) {
          selectedResume = {...defaultResume};
        }

        if (selectedResume && selectedResume.Id > 0) {
          postJobApplication(userId,selectedResume.Id,jobId,success,error);
        }
        else
        {
          error('Resume not found');
        }
      }else{
        error('Resume not found');
      }
    }
  )
  .catch((e) => {
    error('Something went wrong. Please try again');
  });
};

const postJobApplication = (
  userId:number,
  resumeId:number,
  jobId:number,
  success:(message:string) => void,
  error:(message:string) => void
) => {
  const jobApplication: IJobApplication = {
    CoveringLetter: '',
    IsDeleted: false,
    IsViewedByRecruiter: false,
    JobId: jobId,
    ResumeId:resumeId,
    UserId: userId,
    AppliedOn: new Date(),
    IsEmailSimilarJobs: false,
    IsPersonalityAssessment: false,
    FileDescription: null,
    AttachedDocumentDescription: null,
    Id: 0,
  };

  jobApplicationApi
    .postApplication(jobApplication)
    .then(r => {
      success('Applied Successfully');
    })
    .catch(e => {
      error('Failed to post the application');
    });
};

export const easyApplyWithDefaultResume = (userId:number,jobId:number,error:(message:string) => void,success:(message:string) => void) => {
  jobApplicationApi
    .isAlreadyApplied(userId, jobId)
    .then(result => {
      if (result === alreadyAppliedForJob) {
        success('You already have applied for this job.');
      } else {
        getResumeAndPostApplication(userId, jobId,error,success);
      }
    })
    .catch(e => {
      error('Something went wrong. Please try again');
    });
};
