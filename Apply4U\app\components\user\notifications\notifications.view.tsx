import React from 'react';
import { StyleSheet, View, FlatList } from 'react-native';
import { useCurrentTheme, ITheme } from '../../../theme';
import { Text, MainLayout } from '../../common';
import { Notify } from '../../common/notify';
import { INotification } from '../../../interfaces';
import { userApi } from '../../../http';

interface IProps {
  notifications: INotification[];
  loadMore: () => void;
  isMoreResultExists: boolean;
  markAsRead:(notificationId:number) => void;
  deleteNotification:(notificationId:number) => void;
}
const NotificationsView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);

  const keyExtractor = React.useCallback(
    (item: INotification, index: number) => `${item.Id}`,
    [],
  );

  const onEndReached = (info: any) => {
    if (props.isMoreResultExists) {
      props.loadMore();
    }
  };

  const listItem = (itemInfo: any) => (
    <Notify
      descriptionHtml={itemInfo.item.Description}
      isViewed={itemInfo.item.IsViewed}
      recordedAt={itemInfo.item.RecordedAt}
      notificationId={itemInfo.item.Id}
      markAsRead={props.markAsRead}
      deleteNotification={props.deleteNotification}
    />
  );

  return (
    <MainLayout>
      <View style={styles.header2}>
        <View style={styles.header2Text}>
          <Text styles={styles.notificationHeadText} text={'Notifications'} />
        </View>
      </View>

      <View style={styles.container}>
        <FlatList
          data={props.notifications}
          keyExtractor={keyExtractor}
          onEndReached={onEndReached}
          onEndReachedThreshold={0.01}
          renderItem={listItem}
          initialNumToRender={10}
          maxToRenderPerBatch={20}
          updateCellsBatchingPeriod={30}
          removeClippedSubviews={true}
          windowSize={21}
        />
      </View>
    </MainLayout>
  );
};

export { NotificationsView };

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      marginBottom: theme.spacing(2),
    },
    header2: {
      height: 60,
      backgroundColor: theme.palette.primary,
      justifyContent: 'center',
    },
    header2Text: {
      color: '#fff',
      fontSize: 18,
      fontWeight: 'bold',
      letterSpacing: 0.5,
      alignSelf: 'center',
    },
    notificationHeadText:{
      ...theme.typography.bold.large
    }
  });

  return { ...styles };
};
