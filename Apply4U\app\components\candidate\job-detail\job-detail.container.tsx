import React from 'react';
import {jobApi, jobApplicationApi} from '../../../http';
import {
  IApplyNowParam,
  IJob,
  IJobDetailParam,
  IJobSearchResponse,
  IUserDetail,
} from '../../../interfaces';
import {
  applyNow,
  canGoBack,
  getParams,
  goBack,
  navigate,
} from '../../../utility';
import {connect} from 'react-redux';

import {JobDetailView} from './job-detail.view';
import {IApplicationState} from '../../../redux';
import {hideLoading, showLoading,addToFavJobsAction,removeFromFavJobsAction} from '../../../redux/actions';
import {showErrorMessage} from '../../../external/toaster';
import {useTranslation} from 'react-i18next';
import {alreadyAppliedForJob, screens} from '../../../app.constant';
import { IAction } from '../../../redux/actions/action.interface';

interface IProps {
  loginUser: IUserDetail | null;
  favJobs:number[],
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  hideLoading: () => void;
  addToFav:(payload:number) => IAction<number>;
  removeFromFav:(payload:number) => IAction<number>;
}
const JobDetailContainer = (props: IProps) => {
  const jobDetailParam = getParams<IJobDetailParam>();
  const {t} = useTranslation('JobDetail');
  const [isAlreadyApplied, setIsAlreadyApplied] =
    React.useState<boolean>(false);

  const [job, setJob] = React.useState<IJobSearchResponse>();
  const getJob = () => {
    if (jobDetailParam) {
      setJob(undefined);
      props.showLoading();
      jobApi.getJob(jobDetailParam?.jobId, props.loginUser?.Id).then(
        job => {
          props.hideLoading();
          setJob({...job});
        },
        error => {
          props.hideLoading();
          showErrorMessage(t('Messages.Job_Load_Failure'));
        },
      ).catch(() => {});
    }
  };

  const addToFav = () => {
    if(isFav){
      props.removeFromFav(job?.Id??0);
    }else{
      props.addToFav(job?.Id ?? 0);
    }
  };

  const applyNowClick = () => {
    
    if(!!job){
      applyNow(job.Id,job.JobSourceId,job.Title,job.ApplicationUrl);
    }
  };

  React.useEffect(() => {
    getJob();
    jobApplicationApi
      .isAlreadyApplied(props.loginUser?.Id ?? 0, jobDetailParam?.jobId ?? 0)
      .then(result => {
        if (result === alreadyAppliedForJob) {
          setIsAlreadyApplied(true);
        } else {
          setIsAlreadyApplied(false);
        }
      });
      markAsFav();
  }, [jobDetailParam]);

  const [isFav,setIsFav] = React.useState<boolean>(false);
  const markAsFav = () => {
    if(!!props.favJobs && props.favJobs.length > 0){
      if(props.favJobs.findIndex(m => m == jobDetailParam?.jobId) >= 0){
        setIsFav(true);
      }else{
        setIsFav(false);
      }
    }else{
      setIsFav(false);
    }
  }
  React.useEffect(() => {
    markAsFav();
  },[props.favJobs]);
  return (
    <JobDetailView
      job={job}
      canGoBack={canGoBack()}
      goBack={goBack}
      addToFav={addToFav}
      applyNow={applyNowClick}
      isAlreadyApplied={isAlreadyApplied}
      isFav={isFav}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser: state.loginUser,
    favJobs:state.favJobs
  };
};
const mapDispatchToProps = {
  showLoading,
  hideLoading,
  addToFav:addToFavJobsAction,
  removeFromFav:removeFromFavJobsAction
};
const connectedJobDetail = connect(
  mapStateToProps,
  mapDispatchToProps,
)(JobDetailContainer);
export {connectedJobDetail as JobDetailContainer};
