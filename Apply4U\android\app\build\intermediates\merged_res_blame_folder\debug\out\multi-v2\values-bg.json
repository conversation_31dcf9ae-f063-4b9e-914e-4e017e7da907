{"logs": [{"outputFile": "com.apply4u.app-mergeDebugResources-36:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,218,302,375,450,538,607,674,754,836,923,1003,1074,1161,1248,1322,1401,1483,1560,1637,1712,1796,1871,1953,2023", "endColumns": "69,92,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "120,213,297,370,445,533,602,669,749,831,918,998,1069,1156,1243,1317,1396,1478,1555,1632,1707,1791,1866,1948,2018,2103"}, "to": {"startLines": "33,39,43,64,66,67,69,83,84,85,123,124,125,126,128,129,130,131,132,133,134,135,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3064,3607,4017,7060,7213,7288,7438,8490,8557,8637,11735,11822,11902,11973,12144,12231,12305,12384,12466,12543,12620,12695,12880,12955,13037,13107", "endColumns": "69,92,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "3129,3695,4096,7128,7283,7371,7502,8552,8632,8714,11817,11897,11968,12055,12226,12300,12379,12461,12538,12615,12690,12774,12950,13032,13102,13187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4343b2ae69420088cd050005b2aafd9c\\transformed\\jetified-play-services-basement-17.5.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "196", "endOffsets": "443"}, "to": {"startLines": "52", "startColumns": "4", "startOffsets": "5309", "endColumns": "196", "endOffsets": "5501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13659b94c66de92d98d7f8af61234b95\\transformed\\core-1.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "136", "startColumns": "4", "startOffsets": "12779", "endColumns": "100", "endOffsets": "12875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b3627ca143232ec2ceb6e319eb18971d\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,12060", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,12139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3a8a24834f295d8078f18b8298041f7\\transformed\\material-1.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2912,2965,3016,3082,3157,3233,3319,3396,3472,3549,3623,3714,3789,3880,3972,4046,4133,4224,4279,4345,4428,4514,4576,4640,4703,4820,4933,5044,5161,5218,5273", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,84,52,50,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2907,2960,3011,3077,3152,3228,3314,3391,3467,3544,3618,3709,3784,3875,3967,4041,4128,4219,4274,4340,4423,4509,4571,4635,4698,4815,4928,5039,5156,5213,5268,5354"}, "to": {"startLines": "2,34,35,36,37,38,40,41,42,62,63,65,68,70,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3134,3213,3292,3375,3497,3700,3795,3928,6897,6963,7133,7376,7507,7596,7659,7724,7783,7856,7919,7973,8101,8158,8220,8274,8347,8719,8803,8891,9027,9115,9203,9288,9341,9392,9458,9533,9609,9695,9772,9848,9925,9999,10090,10165,10256,10348,10422,10509,10600,10655,10721,10804,10890,10952,11016,11079,11196,11309,11420,11537,11594,11649", "endLines": "5,34,35,36,37,38,40,41,42,62,63,65,68,70,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,84,52,50,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,56,54,85", "endOffsets": "310,3208,3287,3370,3492,3602,3790,3923,4012,6958,7055,7208,7433,7591,7654,7719,7778,7851,7914,7968,8096,8153,8215,8269,8342,8485,8798,8886,9022,9110,9198,9283,9336,9387,9453,9528,9604,9690,9767,9843,9920,9994,10085,10160,10251,10343,10417,10504,10595,10650,10716,10799,10885,10947,11011,11074,11191,11304,11415,11532,11589,11644,11730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\19ec269da49c6c76309942cfa013a60d\\transformed\\jetified-play-services-base-17.5.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,352,549,682,791,1017,1150,1295,1429,1657,1767,1973,2109,2325,2517,2613,2698", "endColumns": "106,196,132,108,225,132,144,133,227,109,205,135,215,191,95,84,97", "endOffsets": "351,548,681,790,1016,1149,1294,1428,1656,1766,1972,2108,2324,2516,2612,2697,2795"}, "to": {"startLines": "44,45,46,47,48,49,50,51,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4101,4212,4409,4546,4659,4885,5022,5171,5506,5734,5848,6054,6194,6410,6606,6706,6795", "endColumns": "110,196,136,112,225,136,148,137,227,113,205,139,215,195,99,88,101", "endOffsets": "4207,4404,4541,4654,4880,5017,5166,5304,5729,5843,6049,6189,6405,6601,6701,6790,6892"}}]}]}