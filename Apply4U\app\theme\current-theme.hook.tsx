import React from 'react';
import {ITheme} from './theme.interface';
import {useTheme} from './theme.context';

type Generator<T extends {}, P extends {}> = (theme: ITheme, props?: P) => T;

export const useCurrentTheme = <T extends {}, P extends {}>(
  fn: Generator<T, P>,
  props?: P,
) => {
  const {theme} = useTheme();

  const currentTheme = React.useMemo(() => fn(theme, props), [fn, theme]);
  return currentTheme;
};
