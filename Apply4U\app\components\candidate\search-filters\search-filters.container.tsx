import React from 'react';
import {
  anyJobStatusId,
  defaultPageNumberJobSearch,
  defaultPageSizeSearchResults,
  screens,
  searchWithinDropdownDataSource,
} from '../../../app.constant';
import {
  getJobCategories,
  getJobTypes,
  getSalaryDuration,
  getSectors,
} from '../../../http';
import {
  IDropDownDataSource,
  IEnum,
  IAutoComplete,
  ISearchParam,
  ISector,
} from '../../../interfaces';
import {SearchFiltersView} from './search-filters.view';
import {connect} from 'react-redux';
import {IApplicationState} from '../../../redux';
import {setSearchParamAction} from '../../../redux/actions';
import {IAction} from '../../../redux/actions/action.interface';
import {navigate} from '../../../utility';

interface IProps {
  setSearchParam: (payload: ISearchParam | null) => IAction<ISearchParam>;
  searchParam: ISearchParam | null;
  isSearchResultsPage:boolean;
}
const SearchFiltersContainer = (props: IProps) => {
  const [jobStatuses, setJobStatuses] = React.useState<IEnum[]>([]);
  const [jobTypes, setJobTypes] = React.useState<IEnum[]>([]);
  const [salaryPerDataSource, setSalaryPerDataSource] = React.useState<IEnum[]>(
    [],
  );
  const [sectorsDataSource, setSectorsDataSource] = React.useState<ISector[]>(
    [],
  );
  const [searchWithinDataSource, setSearchWithinDataSource] = React.useState<
    IDropDownDataSource[]
  >(searchWithinDropdownDataSource);

  const [localSearchParam, setLocalSearchParam] =
    React.useState<ISearchParam | null>({...props.searchParam});

  const handleJobTypesChange = (selectedJobTypes: number[]) => {
    let searchParam: any = {};

    if (localSearchParam) {
      searchParam = {...localSearchParam};
    }
    setLocalSearchParam({...searchParam, selectedJobTypes});
  };

  const handleJobStatusesChange = (selectedJobCategories: number[]) => {
    let searchParam: any = {};

    if (localSearchParam) {
      searchParam = {...localSearchParam};
    }

    setLocalSearchParam({...searchParam, selectedJobCategories});
  };

  const handleChange = (value: any, name: string) => {
    let searchParam: any = {};
    if (localSearchParam) {
      searchParam = {...localSearchParam};
    }

    searchParam[name as keyof ISearchParam] = value;
    setLocalSearchParam({...searchParam});
  };

  const handleLocationChange = (
    value: any,
    name: keyof ISearchParam,
    selectedLocationFeild: keyof ISearchParam,
    selectedLocation: IAutoComplete | null | undefined,
  ) => {
    let searchParam: any = {};
    if (localSearchParam) {
      searchParam = {...localSearchParam};
    }

    searchParam[name as keyof ISearchParam] = value;
    searchParam[selectedLocationFeild] = selectedLocation;
    setLocalSearchParam({...searchParam});
  };

  const findJobs = () => {
    const applyFilters = props.isSearchResultsPage ? true : localSearchParam?.applyFilters;
    props.setSearchParam({...localSearchParam,applyFilters:applyFilters});

    if(!props.isSearchResultsPage){
      navigate(screens.JobSearchResults);
    }
  };

  React.useEffect(() => {
    let isComponentMounted = true;
    getJobTypes().then(result => {
      if (result && result.length > 0 && isComponentMounted) {
          setJobTypes([...result]);
      }
    });

    getJobCategories().then(result => {
      if (result && result.length > 0 && isComponentMounted) {
        let newResults: IEnum[] = [
          {Id: anyJobStatusId, Description: 'Any', EnumKey: 'Any'},
          ...result,
        ];

          setJobStatuses([...newResults]);
      }
    });

    getSalaryDuration().then(results => {
      if (results && results.length > 0 && isComponentMounted) {
        setSalaryPerDataSource([...results]);
      }
    });

    getSectors().then(sectors => {
      if (sectors && sectors.length > 0 && isComponentMounted) {
        setSectorsDataSource(sectors);
      }
    });

    return () => {
      isComponentMounted = false;
    }
  }, []);

  React.useEffect(() => {
    let isMounted = true;
    if(isMounted){
      setLocalSearchParam({...props.searchParam});
    }

    return () =>{
      isMounted = false;
    }
  }, [props.searchParam]);

  return (
    <SearchFiltersView
      jobStatuses={jobStatuses}
      jobTypes={jobTypes}
      handleJobStatusesChange={handleJobStatusesChange}
      handleJobTypesChange={handleJobTypesChange}
      salaryPerDataSource={salaryPerDataSource}
      sectorsDataSource={sectorsDataSource}
      searchWithinDataSource={searchWithinDataSource}
      handleChange={handleChange}
      searchParam={localSearchParam}
      handleLocationChange={handleLocationChange}
      findJobs={findJobs}
      isSearchResultsPage={props.isSearchResultsPage}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    searchParam: state.searchParam,
  };
};

const mapPropsToState = {
  setSearchParam: setSearchParamAction,
};

const connectedSearchFiltersContainer = connect(
  mapStateToProps,
  mapPropsToState,
)(SearchFiltersContainer);

export {connectedSearchFiltersContainer as SearchFiltersContainer};
