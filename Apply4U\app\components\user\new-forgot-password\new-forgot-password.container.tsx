import React, { useState } from 'react';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import { userApi } from '../../../http';
import { navigate } from '../../../utility';
import { connect } from 'react-redux';
import {
  hideLoading,
  showLoading,
} from '../../../redux/actions';
import { IApplicationState } from '../../../redux';
import { screens } from '../../../app.constant';
import { NewForgotPasswordView } from './new-forgot-password.view';

interface IProps {
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => void;
  hideLoading: () => void;
}

const NewForgotPasswordContainer = (props: IProps) => {
  const [isResetEmailSent, setIsResetEmailSent] = useState<boolean>(false);

  const resetPassword = async (email: string) => {
    try {
      props.showLoading('Sending reset email...');
      
      const emailExistsData = await userApi.emailAlreadyExists(email);
      
      if (emailExistsData && emailExistsData.length > 0 && emailExistsData[0]?.Id > 0) {
        await userApi.resetPassword(email);
        setIsResetEmailSent(true);
        showSuccessMessage('Password reset email sent successfully!');
      } else {
        showErrorMessage('Account not found. Please check your email address.');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      showErrorMessage('Failed to send reset email. Please try again.');
    } finally {
      props.hideLoading();
    }
  };

  const backToSignIn = () => {

    setIsResetEmailSent(false);
    navigate(screens.NewLogin);
  };

  return (
    <NewForgotPasswordView
      resetPassword={resetPassword}
      backToSignIn={backToSignIn}
      isResetEmailSent={isResetEmailSent}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
  };
};

const mapDispatchToProps = {
  showLoading,
  hideLoading,
};

const connectedNewForgotPasswordContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(NewForgotPasswordContainer);

export { connectedNewForgotPasswordContainer as NewForgotPasswordContainer };
