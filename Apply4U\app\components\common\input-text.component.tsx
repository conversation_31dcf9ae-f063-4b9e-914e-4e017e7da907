import { IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import React from 'react';
import {
  Keyboard,
  KeyboardTypeOptions,
  NativeSyntheticEvent,
  StyleSheet,
  TextInput,
  TextInputEndEditingEventData,
  TextInputFocusEventData,
  TouchableOpacity,
  View,
} from 'react-native';
import {InputType} from '../../enum';
import {IValidationResult} from '../../interfaces/validation';
import {ITheme, useCurrentTheme} from '../../theme';
import {hasValues} from '../../utility';
import {Text} from './text.component';
import {defaultInputHeight,defaultInputIconSize, zIndex} from '../../app.constant';

interface IProps {
  name: string;
  placeholder: string;
  styles?: any | null;
  inputType?: InputType | null;
  value?: any;
  validationResults?: IValidationResult[];
  onChangeText?: (value: string, name: string) => void;
  onBlur?: (event: NativeSyntheticEvent<TextInputEndEditingEventData>) => void;
  onFocus?: (event: NativeSyntheticEvent<TextInputFocusEventData>) => void;
  onClick?: () => void;
  multiline?: boolean;
  height?:number;
  inputIconSize?:number;
  icon?:IconDefinition;
  iconEnd?:IconDefinition;
  iconEndPressed?:() => void;
  selection?:any;
}

const InputText = (props: IProps) => {
  let isPassoword: boolean = false;
  let keyBoardType: KeyboardTypeOptions = 'default';
  switch (props.inputType) {
    case InputType.Decimal: {
      keyBoardType = 'decimal-pad';
      break;
    }

    case InputType.EmailAddress: {
      keyBoardType = 'email-address';
      break;
    }

    case InputType.Numeric: {
      keyBoardType = 'numeric';
      break;
    }

    case InputType.Password: {
      isPassoword = true;
      break;
    }

    case InputType.PhoneNumber: {
      keyBoardType = 'phone-pad';
      break;
    }

    default: {
      break;
    }
  }
  const styles = useCurrentTheme(createStyles, props);

  const [isValid, setIsValid] = React.useState<boolean>(true);
  const inputHeight = props.height ?? defaultInputHeight;
  React.useEffect(() => {
    if (props.validationResults && hasValues(props.validationResults)) {
      setIsValid(false);
    } else {
      setIsValid(true);
    }
  }, [props.validationResults]);

  return (
    <React.Fragment>
      <View style={styles.container}>
        <View style={{flexDirection:'row',height:inputHeight}}>
        <TextInput
          key={props.name}
          style={[
            styles.inputStyle,
            !isValid ? styles.invalidInput : styles.validInput,
            props.styles,
          ]}
          onTouchStart={() => {
            if (props.onClick) {
              props.onClick();
            }
          }}
          placeholder={props.placeholder}
          placeholderTextColor={
            !isValid
              ? styles.inValidPlaceholderColor
              : styles.validPlaceholderColor
          }
          keyboardType={keyBoardType}
          autoCapitalize="none"
          blurOnSubmit={false}
          secureTextEntry={isPassoword}
          returnKeyType="next"
          value={props.value ? props.value : ''}
          onSubmitEditing={() => {
            Keyboard.dismiss();
          }}
          onChangeText={(value: string) => {
            if (props.onChangeText) {
              props.onChangeText(value, props.name);
            }
          }}
          onEndEditing={(
            event: NativeSyntheticEvent<TextInputEndEditingEventData>,
          ) => {
            if (props.onBlur) {
              props.onBlur(event);
            }
          }}
          onFocus={(event: NativeSyntheticEvent<TextInputFocusEventData>) => {
            if (props.onFocus) {
              props.onFocus(event);
            }
          }}
          multiline={props.multiline ? props.multiline : false}
          selection={props.selection}
        />
        {!!props.iconEnd && <TouchableOpacity style={{position:'absolute',right:10,top:(inputHeight/2) - 10,...styles.iconZIndex}} onPress={() =>{
          if(props.iconEndPressed){
            props.iconEndPressed();
          }
        }}>
          <FontAwesomeIcon  color={styles.validPlaceholderColor} size={props.inputIconSize ?? defaultInputIconSize} icon={props.iconEnd} /></TouchableOpacity>}
          {!!props.icon && <FontAwesomeIcon style={{position:'absolute',left:10,top:(inputHeight/2) - 10,...styles.iconZIndex}} color={styles.validPlaceholderColor} size={props.inputIconSize ?? defaultInputIconSize} icon={props.icon} />}
        </View>
      </View>
      {!isValid &&
        !!props.validationResults &&
        hasValues(props.validationResults) &&
        props.validationResults.map(
          (result, index) =>
            result.isValid == false &&
            result.message && (
              <View key={index} style={styles.textContainer}>
                <Text styles={styles.errorMessageText} text={result.message} />
              </View>
            ),
        )}
    </React.Fragment>
  );
};

const createStyles = (theme: ITheme, props: any) => {
  const styles = StyleSheet.create({
    inputStyle: {
      ...theme.typography.normal.small,
      flex: 1,
      height: props.height ?? defaultInputHeight,
      borderWidth: 1,
      paddingLeft: !!props.icon ? theme.spacing(18) : theme.spacing(7.5),
      paddingRight: !!props.iconEnd ? theme.spacing(18) : theme.spacing(7.5),
      borderRadius: theme.borderRadius(15),
      backgroundColor: theme.palette.white,
      textAlignVertical:
        props?.multiline && props.multiline === true ? 'top' : 'center',
    },
    invalidInput: {
      borderColor: theme.palette.error,
      color: theme.palette.error,
    },
    validInput: {
      borderColor: theme.palette.lightGray,
      color: theme.palette.gray,
    },
    container: {
      width: '100%',
      height: props?.multiline && props.multiline === true ? 100 : (props.height ?? defaultInputHeight),
      marginTop: theme.spacing(5),
      flexDirection: 'column',
    },
    textContainer: {
      width: '100%',
      marginLeft: theme.spacing(5),
      flexDirection: 'column',
    },
    errorMessageText: {
      ...theme.typography.normal.extraSmall,
      color: theme.palette.error,
    },
    iconZIndex:{
      ...theme.zIndex(zIndex.inputIconZIndex),
    }
  });

  return {
    ...styles,
    validPlaceholderColor: theme.palette.lightGray,
    inValidPlaceholderColor: theme.palette.error,
  };
};

export {InputText};
