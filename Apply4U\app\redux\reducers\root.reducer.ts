import {combineReducers} from 'redux';
import {tokenReducer} from './token.reducer';
import {loadingReducer} from './loading.reducer';
import {loginUserReducer} from './login-user.reducer';
import {isFirstVisitReducer} from './is-first-visit.reducer';
import {searchReducer} from './search.reducer';
import {hiddenJobsReducer} from './hidden-jobs-reducer';
import {FavJobsReducer} from './fav-jobs.reducer';
import {RecentSearchesReducer} from './recent-search.reducer';

const rootReducer = combineReducers({
  token: tokenReducer,
  loading: loadingReducer,
  loginUser: loginUserReducer,
  isFirstVisit: isFirstVisitReducer,
  searchParam: searchReducer,
  hiddenJobs: hiddenJobsReducer,
  favJobs:FavJobsReducer,
  recentSearches:RecentSearchesReducer,
});

export default rootReducer;
