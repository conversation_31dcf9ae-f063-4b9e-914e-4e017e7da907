import React from 'react';
import {StyleSheet, Text as RNText} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';

interface IProps {
  text?: string;
  styles?: any | null;
  numberOfLines?: number | undefined;
  children?: any;
  pressed?: (e: any) => void;
  textCropped?:(isCropped:boolean) => void;
}

const Text = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const onTextLayout = React.useCallback((e:any) => {
    if(props.textCropped){
      props.textCropped(!!props.numberOfLines && e.nativeEvent.lines.length > props.numberOfLines);
    }
  }, []);
  return (
    <RNText
      numberOfLines={props.numberOfLines ? props.numberOfLines : undefined}
      ellipsizeMode="tail"
      onPress={props.pressed}
      onTextLayout={onTextLayout}
      style={
        props.styles
          ? {...styles.textPrimary, ...props.styles}
          : styles.textPrimary
      }>
      {props.children ? props.children : props.text}
    </RNText>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    textPrimary: {...theme.typography.normal.medium} as any,
  });

  return {...styles};
};

export {Text};
