import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  HelperText,
} from 'react-native-paper';
import { a4uLogo } from '../../../assets';
import { ITheme, useCurrentTheme } from '../../../theme';

interface IProps {
  login: (email: string, password: string) => void;
  registerNow: () => void;
  forgotPassword: () => void;
}

export const NewLoginView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [touched, setTouched] = useState({ email: false, password: false });

  const {
    login,
    registerNow,
    forgotPassword
  } = props;

  const validateEmail = (text: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!text) {
      setEmailError('Email is required');
      return false;
    } else if (!emailRegex.test(text)) {
      setEmailError('Please enter a valid email address');
      return false;
    } else {
      setEmailError('');
      return true;
    }
  };

  const validatePassword = (text: string): boolean => {
    if (!text) {
      setPasswordError('Password is required');
      return false;
    } else if (text.length < 3) {
      setPasswordError('Password must be at least 3 characters');
      return false;
    } else {
      setPasswordError('');
      return true;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}>
        <View style={styles.mainContainer}>
          {/* Form section with content */}
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled">
            <View style={styles.contentContainer}>
              {/* Logo inside content container */}
              <View style={styles.logoContainer}>
                <Image
                  source={a4uLogo}
                  style={styles.logo}
                  resizeMode="contain"
                />
              </View>

              <Text style={styles.title}>Sign In To Account</Text>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Email"
                  placeholder="Enter Email"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    if (touched.email) {
                      validateEmail(text);
                    }
                  }}
                  style={styles.input}
                  outlineStyle={[
                    styles.inputOutline,
                    !!emailError && touched.email && styles.errorInputOutline
                  ]}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  activeOutlineColor="#0E1C5D"
                  outlineColor={!!emailError && touched.email ? "#FF3B30" : "#0E1C5D"}
                  error={!!emailError && touched.email}
                  onBlur={() => {
                    setTouched({ ...touched, email: true });
                    validateEmail(email);
                  }}
                />
                {!!emailError && touched.email && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {emailError}
                  </HelperText>
                )}
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  mode="outlined"
                  label="Password"
                  placeholder="Enter Password"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (touched.password) {
                      validatePassword(text);
                    }
                  }}
                  style={styles.input}
                  outlineStyle={[
                    styles.inputOutline,
                    !!passwordError && touched.password && styles.errorInputOutline
                  ]}
                  activeOutlineColor="#0E1C5D"
                  outlineColor={!!passwordError && touched.password ? "#FF3B30" : "#0E1C5D"}
                  secureTextEntry={!passwordVisible}
                  error={!!passwordError && touched.password}
                  onBlur={() => {
                    setTouched({ ...touched, password: true });
                    validatePassword(password);
                  }}
                  right={
                    <TextInput.Icon
                      icon={passwordVisible ? "eye-off" : "eye"}
                      onPress={() => setPasswordVisible(!passwordVisible)}
                    />
                  }
                />
                {!!passwordError && touched.password && (
                  <HelperText type="error" visible={true} style={styles.helperText}>
                    {passwordError}
                  </HelperText>
                )}
              </View>

              <Text
                style={styles.forgotPassword}
                onPress={() => {
                  Keyboard.dismiss();
                  forgotPassword();
                }}>
                Forgot Password?
              </Text>
            </View>
          </ScrollView>

          <View style={styles.bottomContainer}>
            <Button
              mode="contained"
              onPress={() => {
                Keyboard.dismiss();
                const isEmailValid = validateEmail(email);
                const isPasswordValid = validatePassword(password);

                if (isEmailValid && isPasswordValid) {
                  login(email, password);
                } else {
                  setTouched({ email: true, password: true });
                }
              }}
              style={styles.loginButton}
              labelStyle={styles.loginButtonText}>
              Log In
            </Button>

            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>Don't have an account? </Text>
              <Text
                style={styles.registerLink}
                onPress={() => registerNow()}>
                Register Now
              </Text>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (_theme: ITheme) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'white',
    },
    mainContainer: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    logoContainer: {
      alignItems: 'center',
    },
    logo: {
      width: 160,
      height: 160,
    },
    contentContainer: {
      width: '100%',
      paddingTop: 40,
    },
    bottomContainer: {
      width: '100%',
      paddingHorizontal: 20,
      paddingBottom: 30,
      marginTop: 20,
    },
    title: {
      fontSize: 30,
      fontWeight: 'bold',
      color: '#0E1C5D',
      textAlign: 'center',
      marginBottom: 30,
      fontFamily: 'Poppins-Bold',
    },
    inputContainer: {
      marginBottom: 10,
    },
    input: {
      backgroundColor: 'white',
      height: 50,
    },
    inputOutline: {
      borderRadius: 5,
      borderColor: '#0E1C5D',
      borderWidth: 1,
    },
    errorInputOutline: {
      borderRadius: 5,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },
    errorText: {
      color: 'red',
      fontSize: 12,
      marginTop: 5,
      fontFamily: 'Poppins-Regular',
    },
    forgotPassword: {
      color: '#0E1C5D',
      textAlign: 'right',
      marginTop: 5,
      marginBottom: 10,
      fontFamily: 'Poppins-Regular',
      fontSize: 14,
    },
    loginButton: {
      borderRadius: 5,
      height: 50,
      justifyContent: 'center',
      marginBottom: 15,
      backgroundColor: '#0E1C5D',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
    },
    loginButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      color: 'white',
    },
    registerContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 10,
      gap:5,
    },
    registerText: {
      color: '#333',
      fontFamily: 'Poppins-SemiBold',
      fontSize: 14,

    },
    registerLink: {
      color: '#00C851',
      fontWeight: 'bold',
      fontFamily: 'Poppins-Bold',
      fontSize: 14,
      paddingBottom: 5,
      textDecorationLine: 'underline',
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },
  });
};
