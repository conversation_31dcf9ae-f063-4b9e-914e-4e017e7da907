import {HiddenJobsConstant} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {initialState} from '../initial-state';

export const hiddenJobsReducer = (
  state: number[] = initialState.hiddenJobs,
  action: IAction<number[] | number>,
) => {
  switch (action.type) {
    case HiddenJobsConstant.SET_HIDDEN_JOBS:{
      const payload = action.payload as number[] ?? initialState.hiddenJobs;
      let newState: number[] = [...payload];
      return newState;
    }
    case HiddenJobsConstant.HIDE_JOB:{
      const jobId:number = action.payload as number;
      if(!!state && state.indexOf(jobId) >= 0){
          return state;
      }else{
          const newState = !!state ? [...state] : [];
          const stateToReturn = newState.concat(jobId);
          return stateToReturn;
      }
  }
  case HiddenJobsConstant.UN_HIDE_JOB:{
      const jobId:number = action.payload as number;
      if(!!state && state.indexOf(jobId) >= 0){
          const newState = state.filter(m => m != jobId);
          return newState;
      }else{
          return state;
      }
  }
    default:
      return state;
  }
};
