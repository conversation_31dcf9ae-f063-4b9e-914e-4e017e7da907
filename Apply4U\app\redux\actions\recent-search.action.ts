import {IAction} from './action.interface';
import {RecentSearchesConstant} from './action.constant';

export const setRecentSearchesAction = (payload: string[]): IAction<string[]> => {
  return {
    type: RecentSearchesConstant.SET_RECENT_SEARCHES,
    payload,
  };
};

export const addRecentSearchAction = (payload: string): IAction<string> => {
    return {
      type: RecentSearchesConstant.ADD_RECENT_SEARCH,
      payload,
    };
  };

  export const removeRecentSearchAction = (payload: string): IAction<string> => {
    return {
      type: RecentSearchesConstant.REMOVE_RECENT_SEARCH,
      payload,
    };
  };
