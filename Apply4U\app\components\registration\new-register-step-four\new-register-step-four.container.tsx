import React from 'react';
import { NewRegisterStepFourView } from './new-register-step-four.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterStepFourContainer = () => {
  return <NewRegisterStepFourView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterStepFourContainer);

export { connectedNewRegisterContainer as NewRegisterStepFourContainer };
