import React from 'react';
import { CvUploadView } from './cv-upload.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const CvUploadContainer = () => {
  return <CvUploadView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedCvUploadContainer = connect(
  mapStateToProps,
  null,
)(CvUploadContainer);

export { connectedCvUploadContainer as CvUploadContainer };
