import {faAngleLeft, faTrash} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import React from 'react';
import {ImageSourcePropType, KeyboardAvoidingView, Pressable, StyleSheet, View} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import {
  docFileExtension,
  docxFileExtension,
  pdfFileExtension,
  rtfFileExtension,
  txtFileExtension,
} from '../../../app.constant';
import {wordIcon, pdfIcon, rtfIcon, txtIcon} from '../../../assets';
import {InputType} from '../../../enum';
import {ITheme, useCurrentTheme} from '../../../theme';
import {isNullOrWhitespace} from '../../../utility';
import {
  Button,
  FeildSet,
  Image,
  InputText,
  MainLayout,
  Text,
} from '../../common';

interface IProps {
  loginUserName: string | null | undefined;
  jobTitle: string;
  cvFileName: string;
  fileExtension: string;
  canGoBack: () => boolean;
  goBack: () => void;
  uploadNewCVClick: () => void;
  addAdditionalFile: () => void;
  submit: (addCoveLetter: boolean) => void;
  additionalFileName?: string;
  resetAdditionalFileSelection?: () => void;
  coverLetterText: string;
  hanldeCoverLetterTextChange: (value: string) => void;
}
export const ApplyNowView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const canGoBack = React.useState<boolean>(props.canGoBack());
  const [showCoverLetterBox, setShowCoverLetterBox] =
    React.useState<boolean>(false);
  const toggleCoverLetterBoxVisibility = () => {
    setShowCoverLetterBox(!showCoverLetterBox);
  };

  const handleCoverLetterTextChange = (value: string, name: string) => {
    props.hanldeCoverLetterTextChange(value);
  };

  const onSubmit = () => {
    props.submit(showCoverLetterBox);
  };
  return (
    <MainLayout>
      <ScrollView>
      <View style={styles.container}>
        <View style={styles.headerView}>
          <View style={styles.headerContentView}>
            <View style={styles.headerIconView}>
              {!!canGoBack && (
                <Pressable style={styles.headerIcon} onPress={props.goBack}>
                  <FontAwesomeIcon
                    icon={faAngleLeft}
                    style={styles.backIcon}
                    size={30}
                  />
                </Pressable>
              )}
            </View>
            <View style={styles.headerTextView}>
              <Text styles={styles.headerText} text={'Application summary'} />
            </View>
            <View style={styles.headerPlaceholderView}></View>
          </View>
        </View>
        <View style={styles.contentView}>
          <View style={styles.jobTitleContainerView}>
            <Text
              styles={styles.titleText}
              text={`${props.jobTitle ?? ''}`}
              numberOfLines={1}
            />
            <Text
              styles={styles.ApplyingForText}
              text={`Applying for ${props.jobTitle ?? ''}`}
              numberOfLines={1}
            />
          </View>
          <View style={styles.cvContainerView}>
            <View style={styles.cvContainerInnerView}>
              <View style={styles.cvView}>
                {!!props.fileExtension &&
                  props.fileExtension === pdfFileExtension && (
                    <Image style={styles.image} source={pdfIcon} />
                  )}
                {!!props.fileExtension &&
                  (props.fileExtension === docFileExtension ||
                    props.fileExtension === docxFileExtension) && (
                    <Image style={styles.image} source={wordIcon} />
                  )}
                {!!props.fileExtension &&
                  props.fileExtension === rtfFileExtension && (
                    <Image style={styles.image} source={rtfIcon} />
                  )}
                {!!props.fileExtension &&
                  props.fileExtension === txtFileExtension && (
                    <Image style={styles.image} source={txtIcon} />
                  )}
                <Text
                  styles={styles.cvNameText}
                  text={props.cvFileName}
                  numberOfLines={3}
                />
              </View>
              <View style={styles.uploadNewCvView}>
                <Button
                  styles={styles.uploadNewCVButton}
                  text={'Upload New CV'}
                  pressed={props.uploadNewCVClick}
                />
              </View>
            </View>
          </View>
          {!isNullOrWhitespace(props.additionalFileName) && (
            <View style={styles.buttonsContainerView}>
              <FeildSet title={'Additional File'}>
                <View
                  style={{
                    height: 50,
                    paddingVertical: 10,
                    flexDirection: 'row',
                  }}>
                  <View
                    style={{
                      flex: 4,
                      justifyContent: 'flex-start',
                      flexDirection: 'row',
                    }}>
                    <Text
                      styles={styles.additionalFileName}
                      text={props.additionalFileName}
                      numberOfLines={1}
                    />
                  </View>
                  <View
                    style={{
                      flex: 1,
                      justifyContent: 'flex-end',
                      flexDirection: 'row',
                    }}>
                    <Pressable
                      onPress={() => {
                        if (props.resetAdditionalFileSelection) {
                          props.resetAdditionalFileSelection();
                        }
                      }}>
                      <FontAwesomeIcon icon={faTrash} size={18} />
                    </Pressable>
                  </View>
                </View>
              </FeildSet>
            </View>
          )}
          <View style={styles.buttonsContainerView}>
            {isNullOrWhitespace(props.additionalFileName) && (
              <Button
                styles={styles.addAdditionalFileButton}
                textStyles={{color: 'gray'}}
                text={'Add Additional File'}
                pressed={props.addAdditionalFile}
              />
            )}
            <Button
              styles={styles.addCoverLetterButton}
              text={`${showCoverLetterBox ? 'Remove' : 'Add'} Cover Letter`}
              pressed={toggleCoverLetterBoxVisibility}
            />
            {!!showCoverLetterBox && (
              <KeyboardAvoidingView enabled><InputText
                name={'coverLetter'}
                styles={styles.coverLetterInputFeild}
                inputType={InputType.Text}
                placeholder={'Cover Letter'}
                value={props.coverLetterText}
                onChangeText={handleCoverLetterTextChange}
                multiline={true}
              /></KeyboardAvoidingView>
            )}
            <Button
              pressed={onSubmit}
              styles={styles.submitButton}
              text={'Submit Application'}
            />
          </View>
        </View>
      </View>
      </ScrollView>
    </MainLayout>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    headerView: {
      height: 100,
      width: '100%',
      backgroundColor: theme.palette.primary,
      justifyContent: 'center',
      flexDirection: 'row',
      position: 'absolute',
      top: 0,
    },
    headerContentView: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    headerIcon: {
      fontSize: 24,
      alignSelf: 'center',
    },
    headerText: {
      ...theme.typography.bold.medium,
      alignSelf: 'center',
    },
    headerTextView: {
      flex: 8,
      justifyContent: 'center',
    },
    headerIconView: {
      flex: 1,
      justifyContent: 'center',
    },
    headerPlaceholderView: {
      flex: 1,
    },
    backIcon: {
      color: theme.palette.white,
    },
    contentView: {
      marginTop: 120,
      flex: 1,
      flexDirection: 'column',
    },
    cvContainerView: {
      height: 200,
    },
    buttonsContainerView: {
      paddingHorizontal: theme.spacing(10),
    },
    jobTitleContainerView: {
      height: 60,
      paddingHorizontal: theme.spacing(10),
      justifyContent: 'center',
    },
    image: {
      width: 100,
      height: 130,
      resizeMode: 'contain',
      margin: theme.spacing(2),
      alignSelf: 'center',
    },
    button: {
      width: '100%',
      borderRadius: theme.borderRadius(2),
    },
    cvContainerInnerView: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    cvView: {
      flex: 1,
      justifyContent: 'center',
    },
    uploadNewCvView: {
      flex: 1,
      justifyContent: 'center',
      padding: theme.spacing(5),
    },
    titleText: {
      ...theme.typography.bold.medium,
      color: theme.palette.primary,
    },
    ApplyingForText: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
    },
    cvNameText: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
      alignSelf: 'center',
      paddingLeft: theme.spacing(10),
    },
    uploadNewCVButton: {
      backgroundColor:'#35dff8',
      borderRadius: theme.borderRadius(7),
    },
    addCoverLetterButton: {
      ...theme.buttons.secondary,
      width: '80%',
      alignSelf:'center',
      borderRadius: theme.borderRadius(7),
    },
    submitButton: {
      ...theme.buttons.secondary,
      width: '80%',
      alignSelf:'center',
      backgroundColor:'#28a745',
      borderRadius: theme.borderRadius(7),
    },
    addAdditionalFileButton: {
      ...theme.buttons.secondary,
      width: '80%',
      alignSelf:'center',
      borderRadius: theme.borderRadius(7),
      backgroundColor: theme.palette.white,
    },
    additionalFileName: {
      ...theme.typography.normal.medium,
      color: theme.palette.gray,
      alignSelf: 'flex-start',
    },
    coverLetterInputFeild: {
      borderRadius: 6,
      height:100,
    },
  });

  return styles;
};
