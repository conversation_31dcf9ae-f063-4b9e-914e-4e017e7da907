import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  Keyboard,
} from 'react-native';
import {useCurrentTheme, ITheme} from '../../../theme';
import {TopBar, Button, InputText, MainLayout} from '../../common';
import {InputType} from '../../../enum';
import {useTranslation} from 'react-i18next';
import {IJob, IJobSearchResponse, ISearchParam} from '../../../interfaces';
import {formatedDate} from '../../../utility';

interface IProps {
  searchResults: IJobSearchResponse[];
  searchParams: ISearchParam | null | undefined;
  handleChange: (value: string, key: keyof ISearchParam) => void;
  jobAddToFav: (jobId: number) => void;
  shareJob: (jobId: number) => void;
  goToJobDetail: (jobId: number) => void;
  onFindJobsClick: () => void;
}

const HomeView = (props: IProps) => {
  const {t} = useTranslation(['HomeView']);
  const styles = useCurrentTheme(createStyles);

  return (
    <MainLayout>
      <View style={styles.formContainer}>
        <View style={styles.inputView}>
          <InputText
            name={'keywords'}
            inputType={InputType.Text}
            placeholder={t('Placeholders.What')}
            styles={styles.inputField}
            onChangeText={(value: string, key: string) =>
              props.handleChange(value, key as keyof ISearchParam)
            }
            value={props?.searchParams?.keywords}
          />
        </View>
        <View style={styles.inputView}>
          <InputText
            name={'location'}
            inputType={InputType.Text}
            placeholder={t('Placeholders.Where')}
            styles={styles.inputField}
            onChangeText={(value: string, key: string) =>
              props.handleChange(value, key as keyof ISearchParam)
            }
            value={props?.searchParams?.locationText}
          />
        </View>
        <Button
          styles={styles.btnSpace}
          pressed={() => {
            Keyboard.dismiss();
            props.onFindJobsClick();
          }}
          text={t('Buttons.Find_Jobs')}
        />
      </View>

      <ScrollView>
        <View style={styles.wrapper}></View>
      </ScrollView>
    </MainLayout>
  );
};

export {HomeView};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    viewPort: {
      height: '100%',
      width: '100%',
    },
    container: {
      flexDirection: 'column',
      backgroundColor: '#edf1fc',
    },
    wrapper: {
      padding: 10,
      width: '100%',
      flexDirection: 'column',
    },
    formContainer: {
      paddingTop: 0,
      paddingRight: 10,
      paddingLeft: 10,
      backgroundColor: '#fff',
    },
    inputField: {
      padding: 0,
      margin: 0,
      borderRadius: 6,
    },
    inputView: {
      height: 40,
      marginBottom: 10,
      flexDirection: 'row',
    },
    btnSpace: {
      borderRadius: 6,
    },
  });

  return {...styles};
};
