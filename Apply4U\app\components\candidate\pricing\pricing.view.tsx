import React from 'react';
import {StyleSheet, View} from 'react-native';
import {useCurrentTheme, useTheme, ITheme} from '../../../theme';
import {Text, MainLayout, PriceCard} from '../../common';
import {useTranslation} from 'react-i18next';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faAngleLeft} from '@fortawesome/free-solid-svg-icons';
import {ScrollView} from 'react-native';

const PricingView = (props: any) => {
  const {t, i18n} = useTranslation(['PricingView']);
  const styles = useCurrentTheme(createStyles);

  const theme = useTheme();
  const currentTheme = theme.theme;

  return (
    <MainLayout>
      <View>
        <View style={styles.header2}>
          <View style={styles.header2Icon}>
            <FontAwesomeIcon
              icon={faAngleLeft}
              style={styles.faColor}
              size={24}
            />
          </View>
          <View style={styles.header2Text}>
            <Text text={'Jobseeker Pricing Packages'} />
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollViewStyle}>
        <PriceCard
          free={'FREE'}
          price={'Price £0'}
          popular={'MOST POPULAR'}
          total={'Total: £0'}
          feature1={'CV Review'}
          feature2={'CV Distribution'}
          feature3={'CV & Profile Review Call'}
          feature4={'Matching Jobs'}
          feature5={'1 Auto Searches'}
          feature6={'Career Advice Call'}
          feature7={'Verification & Screening'}
          feature8={'Job Search Request'}
          feature9={'Video CV Introduction'}
          feature10={'Mock Interview Video Call'}
          feature11={'Unlimited Connect & Message'}
          feature12={'Industry Forums'}
          feature13={'Total: £0'}
          feature14={'Total: £0'}
          feature15={'Total: £0'}
        />

        <PriceCard
          free={'ESSENTIAL'}
          price={'Price £49'}
          popular={'MOST POPULAR'}
          total={'Total: £49'}
          feature1={'CV Review'}
          feature2={'CV Distribution'}
          feature3={'CV & Profile Review Call'}
          feature4={'Matching Jobs'}
          feature5={'1 Auto Searches'}
          feature6={'Career Advice Call'}
          feature7={'Verification & Screening'}
          feature8={'Job Search Request'}
          feature9={'Video CV Introduction'}
          feature10={'Mock Interview Video Call'}
          feature11={'Unlimited Connect & Message'}
          feature12={'Industry Forums'}
          feature13={'Total: £0'}
          feature14={'Total: £0'}
          feature15={'Total: £0'}
        />

        <PriceCard
          free={'STANDARD'}
          price={'Price £70'}
          popular={'MOST POPULAR'}
          total={'Total: £70'}
          feature1={'CV Review'}
          feature2={'CV Distribution'}
          feature3={'CV & Profile Review Call'}
          feature4={'Matching Jobs'}
          feature5={'1 Auto Searches'}
          feature6={'Career Advice Call'}
          feature7={'Verification & Screening'}
          feature8={'Job Search Request'}
          feature9={'Video CV Introduction'}
          feature10={'Mock Interview Video Call'}
          feature11={'Unlimited Connect & Message'}
          feature12={'Industry Forums'}
          feature13={'Total: £0'}
          feature14={'Total: £0'}
          feature15={'Total: £0'}
        />

        <PriceCard
          free={'ENHANCED'}
          price={'Price £99'}
          popular={'MOST POPULAR'}
          total={'Total: £99'}
          feature1={'CV Review'}
          feature2={'CV Distribution'}
          feature3={'CV & Profile Review Call'}
          feature4={'Matching Jobs'}
          feature5={'1 Auto Searches'}
          feature6={'Career Advice Call'}
          feature7={'Verification & Screening'}
          feature8={'Job Search Request'}
          feature9={'Video CV Introduction'}
          feature10={'Mock Interview Video Call'}
          feature11={'Unlimited Connect & Message'}
          feature12={'Industry Forums'}
          feature13={'Total: £0'}
          feature14={'Total: £0'}
          feature15={'Total: £0'}
        />

        <PriceCard
          free={'PREMIUM'}
          price={'Price £179'}
          popular={'MOST POPULAR'}
          total={'Total: £179'}
          feature1={'CV Review'}
          feature2={'CV Distribution'}
          feature3={'CV & Profile Review Call'}
          feature4={'Matching Jobs'}
          feature5={'1 Auto Searches'}
          feature6={'Career Advice Call'}
          feature7={'Verification & Screening'}
          feature8={'Job Search Request'}
          feature9={'Video CV Introduction'}
          feature10={'Mock Interview Video Call'}
          feature11={'Unlimited Connect & Message'}
          feature12={'Industry Forums'}
          feature13={'Total: £0'}
          feature14={'Total: £0'}
          feature15={'Total: £0'}
        />
      </ScrollView>
    </MainLayout>
  );
};

export {PricingView};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    header2: {
      height: 60,
      width: '100%',
      flexWrap: 'wrap',
      flexDirection: 'row',
      backgroundColor: '#5178e1',
    },
    header2Icon: {
      width: '10%',
      padding: 8,
      margin: 10,
      fontSize: 24,
    },
    header2Text: {
      width: '85%',
      color: '#fff',
      fontSize: 18,
      marginLeft: '-10%',
      fontWeight: 'bold',
      letterSpacing: 0.5,
      textAlign: 'center',
      alignItems: 'center',
      justifyContent: 'center',
    },
    faColor: {
      fontSize: 20,
      color: '#fff',
      marginRight: 5,
    },
    scrollViewStyle: {
      marginBottom: 80,
    },
  });

  return {...styles};
};
