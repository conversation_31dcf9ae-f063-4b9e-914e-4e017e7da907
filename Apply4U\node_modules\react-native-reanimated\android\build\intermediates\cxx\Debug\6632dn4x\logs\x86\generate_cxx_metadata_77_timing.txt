# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 30ms
  [gap of 11ms]
generate_cxx_metadata completed in 55ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 39ms
  [gap of 15ms]
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 44ms
  [gap of 13ms]
generate_cxx_metadata completed in 76ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 47ms
  [gap of 15ms]
  write-metadata-json-to-file 25ms
generate_cxx_metadata completed in 108ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 48ms
  [gap of 14ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 108ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 24ms
  [gap of 10ms]
generate_cxx_metadata completed in 49ms

