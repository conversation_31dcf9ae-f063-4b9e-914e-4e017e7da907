import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  ScrollView,
  TouchableOpacity,
  Modal,
  FlatList,
  Animated,
  Image,
} from 'react-native';
import {
  Text,
  TextInput,
  Chip,
  TouchableRipple,
  Card,
  HelperText,
} from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent,A4UHuma, ScreenView } from '../../common';
import { a4uHuma } from '../../../assets';

const jobTitleOptions = [
  { label: 'Marketing Intern', value: 'Marketing Intern' },
  { label: 'Digital Marketing Intern', value: 'Digital Marketing Intern' },
  { label: 'Social Media Intern', value: 'Social Media Intern' },
  { label: 'Content Marketing Intern', value: 'Content Marketing Intern' }
];

export const NewRegisterStepThreeView = () => {
  const styles = useCurrentTheme(createStyles);
  const [jobTitle, setJobTitle] = useState('Marketing Intern');
  const [showDropdown, setShowDropdown] = useState(false);
  const [selectedChips, setSelectedChips] = useState([
    { id: 1, label: 'Marketing Assistant', selected: true },
    { id: 2, label: 'Digital Marketing Intern', selected: true },
    { id: 3, label: 'Social Media Intern', selected: true },
    { id: 4, label: 'Content Marketing Intern', selected: true },
    { id: 5, label: 'Brand Marketing Intern', selected: true },
  ]);


  const [jobTitleError, setJobTitleError] = useState('');

  const [touched, setTouched] = useState({
    jobTitle: false,
  });

  const validateJobTitle = (text: string): boolean => {
    if (!text.trim()) {
      setJobTitleError('Job title is required');
      return false;
    } else {
      setJobTitleError('');
      return true;
    }
  };

  const handleContinue = () => {
    Keyboard.dismiss();

    const isJobTitleValid = validateJobTitle(jobTitle);
    
    setTouched({
      jobTitle: true,
    });

    if (isJobTitleValid) {
      navigate(screens.NewRegisterStepFour);
    }
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepFour);
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterStepTwo);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  const toggleChip = (id: number) => {
    setSelectedChips(
      selectedChips.map(chip =>
        chip.id === id ? { ...chip, selected: !chip.selected } : chip
      )
    );
  };

  const removeChip = (id: number) => {
    setSelectedChips(selectedChips.filter(chip => chip.id !== id));
  };

  const addChipFromDropdown = (selectedTitle: string) => {

    const existingChip = selectedChips.find(chip => chip.label === selectedTitle);

    if (!existingChip && selectedChips.length < 5) {

      const maxId = selectedChips.length > 0 ? Math.max(...selectedChips.map(chip => chip.id)) : 0;
      const newChip = {
        id: maxId + 1,
        label: selectedTitle,
        selected: true
      };
      setSelectedChips([...selectedChips, newChip]);
    }
  };

  return (
    <RegistrationStepComponent
      title="Job Preferences"
      currentStep={3}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <View style={styles.inputGroup}>
        <View>
          <TextInput
            mode="outlined"
            label="Desired Job Title"
            value={jobTitle}
            placeholder="Select job title"
            style={styles.input}
            outlineStyle={[
              styles.inputOutline,
              !!jobTitleError && touched.jobTitle && styles.errorInputOutline
            ]}
            right={<TextInput.Icon icon="chevron-down" onPress={() => setShowDropdown(true)} />}
            activeOutlineColor="#0E1C5D"
            outlineColor={!!jobTitleError && touched.jobTitle ? "#FF3B30" : "#39608F"}
            error={!!jobTitleError && touched.jobTitle}
            editable={false}
            onPressIn={() => {
              setShowDropdown(true);
              setTouched({ ...touched, jobTitle: true });
              validateJobTitle(jobTitle);
            }}
          />
          {!!jobTitleError && touched.jobTitle && (
            <HelperText type="error" visible={true} style={styles.helperText}>
              {jobTitleError}
            </HelperText>
          )}
        </View>
        <A4UHuma
        image={a4uHuma}
        text="I have suggested the following job titles for you based on your CV, to help you with your job search. You may add or remove them to better suit your preferences"
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />

        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowDropdown(false)}
          >
            <View style={styles.dropdownContainer}>
              <FlatList
                data={jobTitleOptions}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                  <TouchableRipple
                    style={styles.dropdownItem}
                    onPress={() => {
                      setJobTitle(item.value);
                      addChipFromDropdown(item.value);
                      setShowDropdown(false);
                    }}
                    rippleColor="rgba(14, 28, 93, 0.1)"
                  >
                    <Text style={styles.dropdownItemText}>{item.label}</Text>
                  </TouchableRipple>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
              />
            </View>
          </TouchableOpacity>
        </Modal>
        <ScrollView style={styles.chipsScrollView} contentContainerStyle={styles.chipsContainer}>
        {selectedChips.map(chip => (
          <Chip
            key={chip.id}
            selected={chip.selected}
            onPress={() => toggleChip(chip.id)}
            style={styles.chip}
            textStyle={styles.chipText}
            closeIcon="close"
            onClose={() => removeChip(chip.id)}
            selectedColor={chip.selected ? '#0E1C5D' : undefined}
            showSelectedOverlay={chip.selected}
            showSelectedCheck={false}
            mode={chip.selected ? 'flat' : 'outlined'}
            rippleColor="rgba(14, 28, 93, 0.1)"
          >
            {chip.label}
          </Chip>
        ))}
      </ScrollView>

      {/* Info card section */}
      <View style={styles.cardSection}>
        <View style={styles.infoCard}>
          <View style={styles.infoIconContainer}>
            <Text style={styles.infoIcon}>ⓘ</Text>
          </View>
          <Text style={styles.infoText}>You may only select up to 5 titles.</Text>
        </View>
      </View>
      </View>
    </RegistrationStepComponent>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    humaContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
      marginTop:20,
      paddingHorizontal: 0,
    },
    humaText: {
      fontSize: 14,
      color: '#333333',
      fontFamily: 'Poppins-Regular',
    },
    inputGroup: {
      marginBottom: 5,
    },
    input: {
      backgroundColor: '#FFFFFF',
      height: 50,
      fontSize: 16,
      fontFamily: 'Poppins-Regular',
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    inputOutline: {
      borderRadius: 8,
      borderColor: '#39608F',
      borderWidth: 1,
    },
    errorInputOutline: {
      borderRadius: 8,
      borderColor: '#FF3B30',
      borderWidth: 2,
    },
    helperText: {
      marginBottom: 0,
      paddingBottom: 0,
      marginTop: 4,
      color: '#FF3B30',
      fontSize: 12,
    },

    chipsScrollView: {
      height: '39%',
      width: '100%',
    },
    chipsContainer: {
      flexDirection: 'row',
      display:'flex',
      flexWrap: 'wrap',
      paddingBottom: 10,
    },
    chip: {
      margin: 4,
      height: 36,
      backgroundColor:'#CCCCCC'
    },
    chipText: {
      fontSize: 12,
      fontFamily: 'Poppins-Medium',
      color: 'black',
    },
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    dropdownContainer: {
      width: '90%',
      maxHeight: 250,
      backgroundColor: '#FFFFFF',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#39608F',
      overflow: 'hidden',
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    dropdownItem: {
      paddingVertical: 0,
      paddingHorizontal: 0,
    },
    dropdownItemText: {
      fontFamily: 'Poppins-Regular',
      fontSize: 16,
      color: '#000000',
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    separator: {
      height: 1,
      backgroundColor: '#E0E0E0',
      width: '100%',
    },
    cardSection: {
      marginTop: 15,
      marginBottom: 10,
    },
    infoCard: {
      backgroundColor: '#FFF9E6',
      borderRadius: 8,
      borderColor: '#F5C41D',
      borderWidth: 1,
      padding: 12,
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
    },
    infoIconContainer: {
      marginRight: 10,
    },
    infoIcon: {
      color: '#FFC107',
      fontSize: 20,
      fontWeight: 'bold',
    },
    infoText: {
      fontFamily: 'Poppins-Regular',
      fontSize: 14,
      color: '#333333',
      flex: 1,
    },
  });

  return styles;
};
