import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { ITheme, useCurrentTheme } from '../../theme';
import { InteractiveMap } from './interactive-map.component';
import { Text } from './text.component';
import { Button } from './button.component';

/**
 * Example component demonstrating how to use the InteractiveMap component
 * This can be integrated into job search flows or location preference screens
 */
export const InteractiveMapExample = () => {
  const styles = useCurrentTheme(createStyles);
  const [selectedRadius, setSelectedRadius] = useState<number>(20);
  const [searchLocation, setSearchLocation] = useState<string>('London');

  const handleRadiusChange = (radius: number) => {
    setSelectedRadius(radius);
    console.log('Radius changed to:', radius);
  };

  const handleSearchJobs = () => {
    Alert.alert(
      'Job Search',
      `Searching for jobs within ${selectedRadius} miles of ${searchLocation}`,
      [{ text: 'OK' }]
    );
  };

  const handleSavePreferences = () => {
    Alert.alert(
      'Preferences Saved',
      `Location: ${searchLocation}\nRadius: ${selectedRadius} miles`,
      [{ text: 'OK' }]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text styles={styles.title} text="Set Your Job Search Area" />
        <Text 
          styles={styles.subtitle} 
          text="Drag the circle or use the slider to adjust your search radius" 
        />
      </View>

      {/* Interactive Map */}
      <View style={styles.mapWrapper}>
        <InteractiveMap
          initialRadius={selectedRadius}
          minRadius={1}
          maxRadius={50}
          onRadiusChange={handleRadiusChange}
          centerLabel={searchLocation}
          radiusUnit="miles"
          showGrid={true}
          showSlider={true}
        />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        <View style={styles.buttonRow}>
          <Button
            text="Search Jobs"
            pressed={handleSearchJobs}
            styles={[styles.button, styles.primaryButton]}
            textStyles={styles.primaryButtonText}
          />
          <Button
            text="Save Preferences"
            pressed={handleSavePreferences}
            styles={[styles.button, styles.secondaryButton]}
            textStyles={styles.secondaryButtonText}
          />
        </View>
        
        {/* Current Settings Display */}
        <View style={styles.settingsDisplay}>
          <Text 
            styles={styles.settingsText} 
            text={`Current Settings: ${selectedRadius} miles around ${searchLocation}`} 
          />
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.backgroundPalette.primary,
    },
    header: {
      paddingHorizontal: theme.spacing(4),
      paddingVertical: theme.spacing(3),
      backgroundColor: theme.palette.white,
      borderBottomWidth: 1,
      borderBottomColor: theme.palette.lightGray,
    },
    title: {
      ...theme.typography.bold.large,
      color: theme.palette.primary,
      textAlign: 'center',
      marginBottom: theme.spacing(1),
    },
    subtitle: {
      ...theme.typography.normal.medium,
      color: theme.palette.gray,
      textAlign: 'center',
    },
    mapWrapper: {
      flex: 1,
      margin: theme.spacing(2),
      borderRadius: theme.borderRadius(2),
      overflow: 'hidden',
      backgroundColor: theme.palette.white,
      ...theme.zIndex(1),
    },
    actionsContainer: {
      backgroundColor: theme.palette.white,
      paddingHorizontal: theme.spacing(4),
      paddingVertical: theme.spacing(3),
      borderTopWidth: 1,
      borderTopColor: theme.palette.lightGray,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing(3),
    },
    button: {
      flex: 1,
      marginHorizontal: theme.spacing(1),
      height: 48,
      borderRadius: theme.borderRadius(3),
      justifyContent: 'center',
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.palette.primary,
    },
    primaryButtonText: {
      ...theme.typography.bold.medium,
      color: theme.palette.white,
    },
    secondaryButton: {
      backgroundColor: theme.palette.white,
      borderWidth: 2,
      borderColor: theme.palette.primary,
    },
    secondaryButtonText: {
      ...theme.typography.bold.medium,
      color: theme.palette.primary,
    },
    settingsDisplay: {
      alignItems: 'center',
      paddingVertical: theme.spacing(2),
      backgroundColor: theme.backgroundPalette.secondary,
      borderRadius: theme.borderRadius(2),
    },
    settingsText: {
      ...theme.typography.normal.medium,
      color: theme.palette.primary,
      textAlign: 'center',
    },
  });

  return styles;
};
