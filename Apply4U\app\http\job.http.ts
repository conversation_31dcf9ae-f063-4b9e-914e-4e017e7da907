import {http} from './http-base';
import {IJobSearchResponse} from '../interfaces';

const getJob = async (jobId: number, userId?: number): Promise<IJobSearchResponse> => {
  let url: string = `api/Jobs/new/${jobId}`;
  if (userId) {
    url = `${url}?UserId=${userId}`;
  }

  let result = await http.get<IJobSearchResponse>(url).catch(error => Promise.reject(error));

  return result.data;
};

const shareJob = async (sharedJobId: number,sharedByUserId:number,sharedToEmailId:string,jobDetailUrl:string, userId?: number): Promise<any> => {
  let url: string = `api/Jobs/recommend/job?recommendedJobId=${sharedJobId}&jobDetailUrl=${jobDetailUrl}&recommendToEmail=${sharedToEmailId}&recommendedByUserId=${sharedByUserId}`;
  let result = await http.post(url).catch(error => Promise.reject(error));

  return result.data;
};

const jobApi = {
  getJob,
  shareJob
};

export {jobApi};
