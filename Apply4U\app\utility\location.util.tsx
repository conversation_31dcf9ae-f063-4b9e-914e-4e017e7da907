import Geolocation from 'react-native-geolocation-service';
import { requestSinglePermission } from '.';
import { PermissionsAndroid } from 'react-native';
import { locationPermission } from '../app.constant';

export const getCurrentLocation = async (success:(latitude:number,longitude:number) => void,error?:(e:Geolocation.GeoError) => void) => {
    const permissionResult = await requestSinglePermission(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        locationPermission.title,
        locationPermission.message);
    if (permissionResult) {
        Geolocation.getCurrentPosition(
            (position) => {
                success(position?.coords.latitude,position?.coords.longitude);
            },
            (geoError) => {
                // See error code charts below.
                //console.log(error.code, error.message);
                if(error){
                    error(geoError);
                }
            },
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
    }
}