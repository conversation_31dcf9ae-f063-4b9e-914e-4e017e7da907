import React from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import {
  Button,
  Text,
  Avatar,
} from 'react-native-paper';

import { useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { introSlideOne } from '../../../assets';

const { width } = Dimensions.get('window');

export const NewRegisterIntroView = () => {
  const styles = useCurrentTheme(createStyles);

  const handleStart = () => {
    navigate(screens.CvUpload);
  };

  const handleSkip = () => {
    navigate(screens.Login);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>
            Meet <Text style={styles.highlightText}>Huma</Text> Your{'\n'}
            Personal Assistant!
          </Text>

          <Text style={styles.subtitle}>
            Just a few quick steps with <PERSON><PERSON> to find jobs tailored to you.
          </Text>

          <View style={styles.imageContainer}>
            <Image
              source={introSlideOne}
              style={styles.image}
              resizeMode="contain"
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={handleSkip}
            style={styles.skipButton}
            labelStyle={styles.skipButtonLabel}
          >
            Skip
          </Button>

          <Button
            mode="contained"
            onPress={handleStart}
            style={styles.startButton}
            labelStyle={styles.startButtonLabel}
          >
            Start
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
};

const createStyles = () => {
  const styles = StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    container: {
      flex: 1,
      padding: 20,
      justifyContent: 'space-between',
    },
    contentContainer: {
      flex: 1,
      paddingTop: 60,
    },
    title: {
      fontSize: 30,
      fontFamily: 'Poppins-Bold',
      marginBottom: 10,
      lineHeight: 34,
    },
    highlightText: {
      color: '#2196F3',
      fontSize: 30,
      fontFamily: 'Poppins-Bold',
    },
    subtitle: {
      fontSize: 19.05,
      fontFamily: 'Poppins-Medium',
      color: '#555555',
      marginTop: 20,
      marginBottom: 30,
      lineHeight: 22,
    },
    imageContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
    },
    image: {
      marginBottom: 40,
      height: 400,
      width: 600,
      alignSelf: 'center',
      resizeMode: 'contain',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    skipButton: {
      width: '32%',
      borderColor: '#DDDDDD',
      backgroundColor: '#D9D9D9',
      borderRadius: 5,
    },
    
    skipButtonLabel: {
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
      color: '#0E1B5D',
    },
    startButton: {
      width: '32%',
      backgroundColor: '#0E1B5D',
      borderRadius: 5,
    },
    startButtonLabel: {
      color: '#FFFFFF',
      fontSize: 15,
      fontFamily: 'Poppins-SemiBold',
    },
  });

  return styles;
};
