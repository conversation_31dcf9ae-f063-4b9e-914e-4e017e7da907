import {http} from './http-base';
import {IAutoSearch, INotification, IUserDetail, IUserPackage, IUserStat} from '../interfaces';

const getUserDetail = async (
  email: string | undefined,
): Promise<IUserDetail> => {
  let url: string = `api/SystemUsers/userdetail?loginName=${encodeURIComponent(
    email ?? '',
  )}`;
  let result = await http
    .get<IUserDetail>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const getUserPackage = async (
  userId: number,
  isCandidate: boolean,
): Promise<IUserPackage> => {
  let url: string = `api/Packages/userPackage/consumptionDetail?userId=${userId}&isCandidate=${isCandidate}`;
  let result = await http
    .get<IUserPackage>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const getUserStat = async (userId: number): Promise<IUserStat> => {
  let url: string = `api/Dashboard/Candidate?userId=${userId}`;
  let result = await http
    .get<IUserStat>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const getUserProfileCompletionPercentage = async (
  userId: number,
): Promise<number> => {
  let url: string = `api/UserProfiles/userprogress?loggedInUserId=${userId}&dashboardTypeId=1`;
  let result = await http
    .get<number>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const deleteAccount = async (
  userId: number,
): Promise<number> => {
  let url: string = `api/UserProfiles/Account/${userId}/markinactive`;
  let result = await http
    .put<any>(url, [])
    .catch(error => Promise.reject(error));
  return result.data;
};

const getUserNotifications = async (
  userId: number,
  pageNumber:number,
  pageSize:number
): Promise<INotification[]> => {
  let url: string = `api/Feeds/search?userId=${userId}&pageNumber=${pageNumber}&pageSize=${pageSize}`;
  let result = await http
    .get<INotification[]>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const getTotalNotifications = async (
  userId: number
): Promise<number> => {
  let url: string = `api/Feeds/TotalFeedCounts?userId=${userId}`;
  let result = await http
    .get<number>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const deleteNotification = async (
  notificationId: number
): Promise<any> => {
  let url: string = `api/Feeds/feedId?feedId=${notificationId}`;
  let result = await http
    .delete<any>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const markNotificationAsRead = async (
  notificationId: number
): Promise<any> => {
  let url: string = `api/Feeds/MarkViewed?feedId=${notificationId}`;
  let result = await http
    .put<any>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const emailAlreadyExists = async (
  email: string,
): Promise<any[]> => {
  let url: string = `api/SystemUsers/Search/EmailAlreadyExist?loginName=${email}`;
  let result = await http
    .get<any[]>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const resetPassword = async (email:string) => {
  let url: string = `api/SystemUsers/ForgotPassword?loginName=${email}`;
  let result = await http
    .post<any>(url)
    .catch(error => Promise.reject(error));
  return result.data;
}

const getAutoSearches = async (
  userId: number
): Promise<IAutoSearch[]> => {
  let url: string = `api/SaveSearches/search?userId=${userId}&isJobSearch=true&savedSearchTypeId=2`;
  let result = await http
    .get<IAutoSearch[]>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const getDefaultAutoSearch = async (
  userId: number
): Promise<IAutoSearch> => {
  let url: string = `api/search/GetUserDefaultSavedSearch?userId=${userId}&isRecruiter=false`;
  let result = await http
    .get<IAutoSearch>(url)
    .catch(error => Promise.reject(error));
  return result.data;
};

const userApi = {
  getUserDetail,
  getUserPackage,
  getUserStat,
  getUserProfileCompletionPercentage,
  emailAlreadyExists,
  resetPassword,
  getUserNotifications,
  getTotalNotifications,
  markNotificationAsRead,
  deleteNotification,
  getAutoSearches,
  getDefaultAutoSearch,
  deleteAccount
};

export {userApi};
