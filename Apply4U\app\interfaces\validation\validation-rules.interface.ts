import {IAllowSpecialCharacters} from './allow-special-characters-validation.interface';
import {IEmailValidation} from './email-validation.interface';
import {INumberOnlyValidation} from './number-only-validation.interface';
import {IMaxLengthValidation} from './max-length-validation.interface';
import {IMinLengthValidation} from './min-length-validation.interface';
import {IMaxValueValidation} from './max-value-validation.interface';
import {IMinValueValidation} from './min-value-validation.interface';
import {IRequiredValidation} from './required-validation.interface';

export interface IValidationRules {
  required?: IRequiredValidation;
  minValue?: IMinValueValidation;
  maxValue?: IMaxValueValidation;
  minLength?: IMinLengthValidation;
  maxLength?: IMaxLengthValidation;
  numberOnly?: INumberOnlyValidation;
  specialCharacters?: IAllowSpecialCharacters;
  validEmail?: IEmailValidation;
  validate?: any;
}
