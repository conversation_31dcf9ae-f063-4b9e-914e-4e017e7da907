import React from 'react';
import {View, StyleSheet, SafeAreaView, Keyboard} from 'react-native';
import {BottomTabBarComponent, TopBar} from '.';
import {screens} from '../../app.constant';
import {ITabBarOptions} from '../../interfaces';
import {ITheme, useCurrentTheme} from '../../theme';
import {
  faBell,
  faEnvelope,
  faHeart,
  faHome,
  faSearch,
} from '@fortawesome/free-solid-svg-icons';
import { userApi } from '../../http';
import {useSelector} from 'react-redux';
import { IApplicationState } from '../../redux';

interface IProps {
  children: any;
}
export const MainLayout = (props: IProps) => {
  let styles = useCurrentTheme(createStyles);
  const [showNotificationsDot, setShowNotificationsDot] = React.useState<boolean>(false);
  const tabBarHome: ITabBarOptions = {
    name: 'Home',
    screen: screens.Dashboard,
    icon: faHome,
  };

  const tabBarSearch: ITabBarOptions = {
    name: 'Search',
    screen: screens.JobSearchFilter,
    icon: faSearch,
  };

  const tabBarMyJobs: ITabBarOptions = {
    name: 'My Jobs',
    screen: screens.MyJobs,
    icon: faHeart,
  };

  const tabBarNotifications: ITabBarOptions = {
    name: 'Notifications',
    screen: screens.Notifications,
    icon: faBell,
    showNotificationsDot:showNotificationsDot,
  };

  const tabBarMessages: ITabBarOptions = {
    name: 'Messages',
    screen: screens.ForgotPassword,
    icon: faEnvelope,
  };
  const [showBottomTabBar,setShowBottomTabbar] = React.useState<boolean>(true);
  const userId = useSelector((state:IApplicationState) => state.loginUser?.Id ?? 0);
  const getNotificationsCount = () => {
    userApi.getTotalNotifications(userId)
    .then((total:number) => {
      setShowNotificationsDot(total > 0);
    })
    .catch(() => {});
  }

  React.useEffect(() => {
    const keyboardDidHideEventSubscription = Keyboard.addListener('keyboardDidHide',() => {
      setShowBottomTabbar(true);
    });

    const keyboardWillShowEventSubscription = Keyboard.addListener('keyboardDidShow',() => {
      setShowBottomTabbar(false);
    });

    getNotificationsCount();
    const intervalId = setInterval(() => {
      getNotificationsCount();
    },5000);

    return () => {
      Keyboard.removeAllListeners('keyboardDidShow');
      Keyboard.removeAllListeners('keyboardDidHide');
      clearInterval(intervalId);
    }
  },[]);

  return (
    <SafeAreaView style={styles.container}>
    <View style={styles.container}>
      <TopBar />
      <View style={styles.contentContainer}>{props.children}</View>
      {!!showBottomTabBar && <BottomTabBarComponent
        tabBarOptions={[
          tabBarHome,
          tabBarSearch,
          tabBarMyJobs,
          tabBarNotifications,
        ]}
      />}
    </View>
    </SafeAreaView>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    contentContainer: {
      flex: 1, // pushes the footer to the end of the screen
    },
  });

  const colors = {primaryColor:theme.palette.primary};
  return {...styles,...colors};
};
