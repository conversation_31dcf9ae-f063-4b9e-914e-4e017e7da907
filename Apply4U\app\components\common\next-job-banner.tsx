import React from 'react';
import {Alert, Pressable, StyleSheet, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import {Text} from '../common';

interface IProps {
  onLearnMoreClick?: () => void;
}
export const NextJobBanner = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  return (
    <View style={styles.bottomBanner2}>
      <View style={styles.bannerBox1}>
        <Text
          styles={styles.BannerText1}
          text={'Your Next Job Is a Few Clicks Away!'}
        />
        <Text styles={styles.BannerText2} text={'So Start Looking Today...'} />
      </View>

      <View style={styles.bannerBox2}>
        <Pressable
          style={styles.buttonStyle}
          onPress={() => {
            if (props.onLearnMoreClick) {
              props.onLearnMoreClick();
            }
          }}>
          <Text styles={styles.label2} text={'Learn More'} />
        </Pressable>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    bottomBanner2: {
      flex: 1,
      marginTop: 0,
      marginRight: 10,
      marginBottom: 10,
      marginLeft: 10,
      height: 110,
      padding: 10,
      borderRadius: theme.borderRadius(10),
      flexWrap: 'wrap',
      flexDirection: 'row',
      backgroundColor: theme.palette.primary,
    },
    bannerBox1: {
      width: '70%',
    },
    bannerBox2: {
      width: '30%',
      paddingHorizontal: 5,
      alignSelf:'center',
    },
    BannerText1: {
      fontSize: 18,
      color: '#fff',
      fontWeight: 'bold',
    },
    BannerText2: {
      fontSize: 13,
      color: '#fff',
      marginTop: 15,
      letterSpacing: 0.5,
    },
    buttonStyle: {
      height: 30,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.palette.secondary,
      marginBottom:0,
      paddingTop:0,
      paddingBottom:0,
    },
    label2: {
      fontSize: 13,
      color: theme.palette.white,
      letterSpacing: 0.5,
    },
  });
  return styles;
};
