import {IJob} from '..';

export interface IJobApplication {
  Id?: number;
  UserId?: number;
  JobId?: number;
  CvSuitablityTypeId?: any;
  CandidateSelfSuitableTypeId?: any;
  CvFeedbackTypeId?: any;
  ResumeId?: number;
  CoveringLetter?: string;
  AppliedOn?: Date;
  IsViewedByRecruiter?: boolean;
  RecruiterViewedOn?: any;
  CandidateSelfNotes?: string;
  EmployerSelfNotes?: string;
  ConsultantNotes?: string;
  FileDescription?: any;
  IsDeleted?: boolean;
  Company?: any;
  Job?: IJob;
  IsEmailSimilarJobs?: boolean;
  IsPersonalityAssessment?: boolean;
  AttachedDocumentDescription?: any;
  IsExternal?: boolean;
}
