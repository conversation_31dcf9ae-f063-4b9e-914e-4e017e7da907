# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    [gap of 28ms]
    create-ARM64_V8A-model 49ms
    [gap of 55ms]
    create-X86-model 11ms
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 228ms
create_cxx_tasks completed in 232ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 10ms
    [gap of 27ms]
    create-variant-model 19ms
    create-ARMEABI_V7A-model 11ms
    [gap of 27ms]
  create-initial-cxx-model completed in 121ms
create_cxx_tasks completed in 125ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 13ms]
      create-ndk-meta-abi-list 16ms
    create-module-model completed in 31ms
    create-variant-model 43ms
    create-ARMEABI_V7A-model 58ms
    create-ARM64_V8A-model 28ms
    create-X86-model 15ms
    create-X86_64-model 13ms
    create-module-model 14ms
    create-variant-model 32ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 56ms
    create-X86-model 32ms
    create-X86_64-model 28ms
  create-initial-cxx-model completed in 388ms
create_cxx_tasks completed in 395ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 26ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 14ms
    create-X86-model 18ms
    create-X86_64-model 19ms
    create-module-model 22ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 32ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 235ms
create_cxx_tasks completed in 240ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    [gap of 18ms]
    create-variant-model 22ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 140ms
create_cxx_tasks completed in 148ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 27ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 158ms
create_cxx_tasks completed in 165ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 21ms
    create-ARM64_V8A-model 21ms
    create-X86-model 15ms
    create-X86_64-model 19ms
    create-module-model 20ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 14ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 208ms
create_cxx_tasks completed in 213ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    [gap of 19ms]
    create-variant-model 21ms
    create-ARMEABI_V7A-model 10ms
    [gap of 27ms]
  create-initial-cxx-model completed in 127ms
create_cxx_tasks completed in 131ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 32ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 20ms
    create-X86_64-model 17ms
    create-module-model 11ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 14ms
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 196ms
create_cxx_tasks completed in 203ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 16ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 12ms
    [gap of 25ms]
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
  create-initial-cxx-model completed in 122ms
create_cxx_tasks completed in 126ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 25ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 13ms
    create-X86-model 14ms
    create-X86_64-model 14ms
    create-module-model 14ms
    create-variant-model 17ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 15ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 192ms
create_cxx_tasks completed in 198ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 20ms
    create-ARMEABI_V7A-model 56ms
    [gap of 19ms]
    create-ARM64_V8A-model 46ms
    create-X86-model 47ms
    create-X86_64-model 11ms
    create-module-model 18ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 32ms
    create-X86_64-model 26ms
  create-initial-cxx-model completed in 327ms
create_cxx_tasks completed in 331ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 14ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    [gap of 17ms]
    create-variant-model 22ms
    [gap of 19ms]
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 126ms
create_cxx_tasks completed in 130ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 14ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 11ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 18ms
    create-X86-model 12ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 171ms
create_cxx_tasks completed in 180ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 21ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
    create-module-model 13ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 15ms
    create-X86_64-model 26ms
  create-initial-cxx-model completed in 164ms
create_cxx_tasks completed in 168ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 13ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-module-model 10ms
    create-variant-model 23ms
    [gap of 34ms]
  create-initial-cxx-model completed in 128ms
create_cxx_tasks completed in 133ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 24ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 15ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 20ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 155ms
create_cxx_tasks completed in 158ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 17ms
    create-X86-model 49ms
    create-X86_64-model 59ms
    create-module-model 23ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 36ms
    create-X86-model 16ms
    create-X86_64-model 22ms
  create-initial-cxx-model completed in 311ms
  [gap of 13ms]
create_cxx_tasks completed in 324ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    [gap of 11ms]
    create-X86_64-model 13ms
    create-module-model 17ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 152ms
create_cxx_tasks completed in 155ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 14ms
    [gap of 10ms]
    create-X86-model 17ms
    create-X86_64-model 14ms
    create-module-model 22ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 166ms
create_cxx_tasks completed in 173ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 31ms]
    create-X86-model 13ms
    create-X86_64-model 14ms
    create-module-model 12ms
    create-variant-model 14ms
    [gap of 33ms]
  create-initial-cxx-model completed in 119ms
create_cxx_tasks completed in 123ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 11ms
    create-module-model 12ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 145ms
create_cxx_tasks completed in 149ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 11ms
    create-X86-model 13ms
    create-X86_64-model 17ms
    create-module-model 13ms
    create-variant-model 31ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 16ms
    create-X86_64-model 22ms
  create-initial-cxx-model completed in 192ms
create_cxx_tasks completed in 198ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 20ms
    create-X86_64-model 14ms
    create-module-model 16ms
    create-variant-model 46ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 14ms
    create-X86-model 14ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 227ms
create_cxx_tasks completed in 233ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 10ms
    create-ARM64_V8A-model 11ms
    create-X86-model 11ms
    create-X86_64-model 12ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 16ms
  create-initial-cxx-model completed in 140ms
create_cxx_tasks completed in 146ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 15ms
    create-ARMEABI_V7A-model 18ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 18ms
    create-module-model 16ms
    create-variant-model 36ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 17ms
    create-X86-model 20ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 224ms
create_cxx_tasks completed in 232ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 172ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 51ms
    create-X86-model 12ms
    create-X86_64-model 13ms
    create-module-model 11ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 10ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 352ms
create_cxx_tasks completed in 357ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 12ms
    create-X86-model 11ms
    create-X86_64-model 13ms
    [gap of 10ms]
    create-variant-model 13ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 11ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 140ms
create_cxx_tasks completed in 145ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 50ms
    create-ARM64_V8A-model 17ms
    create-X86-model 14ms
    create-X86_64-model 15ms
    create-module-model 14ms
    create-variant-model 16ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 17ms
  create-initial-cxx-model completed in 249ms
  [gap of 10ms]
create_cxx_tasks completed in 259ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 22ms
    create-ARM64_V8A-model 12ms
    create-X86-model 13ms
    create-X86_64-model 10ms
    create-module-model 10ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 11ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 166ms
create_cxx_tasks completed in 171ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 15ms
    [gap of 75ms]
  create-initial-cxx-model completed in 97ms
create_cxx_tasks completed in 101ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 26ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 13ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    [gap of 10ms]
    create-variant-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 10ms
    create-X86-model 12ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 155ms
create_cxx_tasks completed in 160ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 10ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 12ms
    create-X86-model 12ms
    create-X86_64-model 10ms
    create-module-model 14ms
    create-variant-model 14ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 144ms
  [gap of 10ms]
create_cxx_tasks completed in 154ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 24ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 23ms
    create-X86-model 17ms
    create-X86_64-model 19ms
    create-module-model 13ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 27ms
    create-ARM64_V8A-model 16ms
    create-X86-model 11ms
    create-X86_64-model 13ms
  create-initial-cxx-model completed in 210ms
create_cxx_tasks completed in 214ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 27ms
    create-X86_64-model 17ms
    create-module-model 18ms
    create-variant-model 10ms
    create-ARMEABI_V7A-model 11ms
    [gap of 11ms]
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 179ms
create_cxx_tasks completed in 186ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 20ms
    create-ARMEABI_V7A-model 10ms
    create-X86-model 11ms
    [gap of 61ms]
  create-initial-cxx-model completed in 119ms
create_cxx_tasks completed in 124ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 15ms
    create-X86-model 13ms
    create-X86_64-model 11ms
    create-module-model 11ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 16ms
    create-X86_64-model 15ms
  create-initial-cxx-model completed in 188ms
create_cxx_tasks completed in 194ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 12ms]
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 11ms
    create-X86-model 12ms
    create-X86_64-model 12ms
    create-module-model 12ms
    create-variant-model 29ms
    create-ARMEABI_V7A-model 13ms
    create-ARM64_V8A-model 13ms
    create-X86-model 13ms
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 182ms
create_cxx_tasks completed in 188ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 10ms
    create-X86-model 14ms
    create-X86_64-model 13ms
    create-module-model 10ms
    create-variant-model 11ms
    create-ARMEABI_V7A-model 12ms
    create-ARM64_V8A-model 13ms
    create-X86-model 10ms
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 152ms
create_cxx_tasks completed in 157ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 31ms]
    create-X86-model 10ms
    create-X86_64-model 12ms
    create-variant-model 11ms
    [gap of 27ms]
    create-X86_64-model 10ms
  create-initial-cxx-model completed in 111ms
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 11ms
    create-ARMEABI_V7A-model 15ms
    create-ARM64_V8A-model 10ms
    create-X86-model 13ms
    create-X86_64-model 27ms
    create-module-model 11ms
    create-variant-model 13ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 14ms
    create-X86-model 13ms
    create-X86_64-model 34ms
  create-initial-cxx-model completed in 197ms
create_cxx_tasks completed in 202ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 87ms
create_cxx_tasks completed in 91ms

