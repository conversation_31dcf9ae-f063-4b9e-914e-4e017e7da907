import React from 'react';
import { NewRegisterStepTwoView } from './new-register-step-two.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterStepTwoContainer = () => {
  return <NewRegisterStepTwoView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterStepTwoContainer);

export { connectedNewRegisterContainer as NewRegisterStepTwoContainer };
