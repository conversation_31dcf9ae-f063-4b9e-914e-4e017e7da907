# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 29ms
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 55ms
  [gap of 17ms]
generate_cxx_metadata completed in 102ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 25ms
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 57ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 26ms
  [gap of 10ms]
generate_cxx_metadata completed in 50ms

