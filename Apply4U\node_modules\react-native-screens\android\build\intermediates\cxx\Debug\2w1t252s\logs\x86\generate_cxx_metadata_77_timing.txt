# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 34ms
  [gap of 11ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 84ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 49ms
  [gap of 15ms]
  write-metadata-json-to-file 34ms
generate_cxx_metadata completed in 127ms

# C/C++ build system timings
generate_cxx_metadata 19ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 25ms
  [gap of 20ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 86ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 12ms]
  create-invalidation-state 16ms
  [gap of 10ms]
generate_cxx_metadata completed in 38ms

