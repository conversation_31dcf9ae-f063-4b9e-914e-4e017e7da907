import React from 'react';
import { connect } from 'react-redux';
import { defaultPageSizeSearchResults, screens } from '../../../app.constant';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';
import { searchApi } from '../../../http';
import { IJobDetailParam, IJobSearchResponse, ILoadingIndicator, IUserDetail } from '../../../interfaces';
import { IApplicationState } from '../../../redux';
import { hideLoading, showLoading, unHideJobAction } from '../../../redux/actions';
import { IAction } from '../../../redux/actions/action.interface';
import { applyNow, easyApplyWithDefaultResume, navigate } from '../../../utility';
import { HiddenJobsView } from './hidden-jobs.view';

interface IProps {
    loginUser: IUserDetail | null;
    hiddenJobs: number[];
    showLoading: (
        loadingText?: string,
        autoHide?: boolean,
        autoHideMilliseconds?: number,
    ) => IAction<ILoadingIndicator>;
    hideLoading: () => IAction<ILoadingIndicator>;
    unHideJob: (payload: number) => IAction<number>;
}

const HiddenJobsContainer = (props: IProps) => {
    const [searchResults, setSearchResults] = React.useState<IJobSearchResponse[]>([]);
    const [nextJobIndex, setNextJobIndex] = React.useState<number>(0);
    const [noHiddenJobs,setNoHiddenJobs] = React.useState<boolean>(false);
    const [currentPageSize, setCurrentPageSize] = React.useState<number>(
        defaultPageSizeSearchResults,
    );

    const [loadingIsInProcess, setLoadingIsInProcess] =
        React.useState<boolean>(false);
    const [isMoreResultsExists, setIsMoreResultsExists] =
        React.useState<boolean>(true);
    const jobAddToFav = (jobId: number) => { };

    const handleGoToJobDetail = (jobId: number) => {
        navigate<IJobDetailParam>(screens.JobDetail, { jobId });
    };
    const handleUnHideJob = (jobId: number) => {
        if (jobId > 0) {
            props.unHideJob(jobId);
        }
    };

    const handleLoadMore = () => {
        loadHiddenJobs();
    };

    const handleEasyApply = (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => {
        if (jobId > 0 && jobSourceId > 0 && !!jobTitle) {
          applyNow(jobId, jobSourceId, jobTitle, applicationUrl, isEasyApply ? internalEasyApply : undefined);
        } else {
          applyNow(undefined, 0, '', applicationUrl, isEasyApply ? internalEasyApply : undefined);
        }
      };

      const internalEasyApply = (jobId:number) => {
          props.showLoading();
        easyApplyWithDefaultResume(props?.loginUser?.Id ?? 0,jobId,(errorMessage:string) =>{
            props.hideLoading();
            showErrorMessage(errorMessage);
        },(successMessage:string) => {
            setSearchResults(oldResults => {
                return oldResults.map(m => {
                    if(m.Id == jobId){
                        m.IsApplied = true;
                    }
    
                    return m;
                });
            });
            props.hideLoading();
            showSuccessMessage(successMessage);
        });
      }

    const loadResults = (
        hiddenJobIds: number[],
        onSuccess: () => void,
    ) => {
        if (!loadingIsInProcess) {
            setLoadingIsInProcess(true);
            props.showLoading();

            searchApi
                .getJobsByIds(
                    hiddenJobIds,
                    props.loginUser?.Id,
                )
                .then((results: IJobSearchResponse[]) => {
                    if (!!results  && results.length > 0) {
                        setSearchResults([...searchResults, ...results]);
                    }
                    onSuccess();
                })
                .catch((err: any) => {
                    props.hideLoading();
                })
                .finally(() => {
                    props.hideLoading();
                    setLoadingIsInProcess(false);
                });
        }
    };

    React.useEffect(() => {
        setSearchResults([]);
        setNextJobIndex(0);
        loadHiddenJobs();
    }, []);

      React.useEffect(() => {
          if(!!searchResults && searchResults.length > 0){
              const hJobs = !!props.hiddenJobs ? props.hiddenJobs : [];
              const filteredSearchResults = searchResults.filter(m => hJobs.indexOf(m.Id) >= 0);
              setSearchResults([...filteredSearchResults]);
          }

          setNoHiddenJobs(!props.hiddenJobs || props.hiddenJobs.length <= 0);
      },[props.hiddenJobs]);

    const loadHiddenJobs = () => {
        if (!!props.hiddenJobs && props.hiddenJobs.length > 0) {
            if (nextJobIndex < props.hiddenJobs.length) {
                let lastJobIndex = (currentPageSize - 1) + nextJobIndex;

                if (lastJobIndex >= props.hiddenJobs.length) {
                    lastJobIndex = props.hiddenJobs.length - 1;
                }

                let jobIds = props.hiddenJobs.slice(nextJobIndex, (lastJobIndex + 1));//adding 1 because end is not inclusive

                loadResults(jobIds, () => {
                    setNextJobIndex(lastJobIndex + 1);
                    setIsMoreResultsExists(lastJobIndex + 1 < props.hiddenJobs.length);
                });
            }
        }else{
            setNoHiddenJobs(true);
        }
    }
    return <HiddenJobsView
        searchResults={searchResults}
        loadMore={handleLoadMore}
        onUnHideJob={handleUnHideJob}
        jobAddToFav={jobAddToFav}
        goToJobDetail={handleGoToJobDetail}
        handleEasyApply={handleEasyApply}
        isMoreResultsExists={isMoreResultsExists}
        noHiddenJobsFound={noHiddenJobs}
    />;
}

const mapStateToProps = (state: IApplicationState) => {
    return {
        hiddenJobs: state.hiddenJobs,
        loginUser: state.loginUser
    };
}

const mapDispatchToProps = {
    showLoading: showLoading,
    hideLoading: hideLoading,
    unHideJob:unHideJobAction
};

const connectedHiddenJobsContainer = connect(mapStateToProps, mapDispatchToProps)(HiddenJobsContainer);
export { connectedHiddenJobsContainer as HiddenJobsContainer };