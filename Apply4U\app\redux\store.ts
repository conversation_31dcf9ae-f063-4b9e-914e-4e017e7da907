import {createStore, applyMiddleware} from 'redux';
import rootReducer from './reducers/root.reducer';
import {initialState} from './initial-state';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: [
    'token',
    'loginUser',
    'isFirstVisit',
    'searchParam',
    'hiddenJobs',
    'favJobs',
    'recentSearches',
  ],
};

//need to remove this reduxImmutable state invariant before creating production build
//for app. Because this is just to check and ensure we are not mutating the state while using
//redux store.

const configureStore = (initialState: any) => {
  return createStore(
    persistReducer(persistConfig, rootReducer),
    initialState,
  );
};

const store:any = configureStore(initialState);
const persistor = persistStore(store);
export {store, persistor};
