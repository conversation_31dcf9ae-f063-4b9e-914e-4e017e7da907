{"abi": "ARMEABI_V7A", "info": {"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6632dn4x\\armeabi-v7a", "soFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6632dn4x\\obj\\armeabi-v7a", "soRepublishFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cmake\\debug\\obj\\armeabi-v7a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=73", "-DANDROID_TOOLCHAIN=clang", "-DBOOST_VERSION=", "-DREACT_NATIVE_DIR=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native", "-DJS_RUNTIME=jsc", "-DJS_RUNTIME_DIR=D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native\\ReactCommon\\jsi", "-DIS_NEW_ARCHITECTURE_ENABLED=false", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.8.1", "-DHERMES_ENABLE_DEBUGGER=0"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": ["reanimated"], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\.cxx", "intermediatesBaseFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates", "intermediatesFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-reanimated", "moduleRootFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android", "moduleBuildFile": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build.gradle", "makeFile": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.0.0\\f2702b5ca13df54e3ca92f29d6b403fb6285d8df\\cli-2.0.0-all.jar"], "prefabPackages": ["C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e1231b5b187cb579b629117f9511a862\\transformed\\jetified-react-android-0.73.6-debug\\prefab", "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\057fda56c2cfc52d70a46042a30c6944\\transformed\\jetified-fbjni-0.5.1\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6632dn4x\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "6632dn4x4r364z1y5c11j4z4i373r2y5s1q6v3c1339305f2e16716f513u6q", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.1.1.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native-reanimated/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DC<PERSON>KE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BD:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native-reanimated/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DREACT_NATIVE_MINOR_VERSION=73\n-DANDROID_TOOLCHAIN=clang\n-DREACT_NATIVE_DIR=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native\n-DJS_RUNTIME=jsc\n-DJS_RUNTIME_DIR=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native/ReactCommon/jsi\n-DIS_NEW_ARCHITECTURE_ENABLED=false\n-DIS_REANIMATED_EXAMPLE_APP=false\n-DREANIMATED_VERSION=3.8.1\n-DHERMES_ENABLE_DEBUGGER=0", "configurationArguments": ["-HD:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6632dn4x\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6632dn4x\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6632dn4x\\prefab\\armeabi-v7a\\prefab", "-BD:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6632dn4x\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DREACT_NATIVE_MINOR_VERSION=73", "-DANDROID_TOOLCHAIN=clang", "-DREACT_NATIVE_DIR=D:/A4U CODE DATA/Mobile App/Mobile.tariq.ameen/Apply4U/node_modules/react-native", "-DJS_RUNTIME=jsc", "-DJS_RUNTIME_DIR=D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native\\ReactCommon\\jsi", "-DIS_NEW_ARCHITECTURE_ENABLED=false", "-DIS_REANIMATED_EXAMPLE_APP=false", "-DREANIMATED_VERSION=3.8.1", "-DHERMES_ENABLE_DEBUGGER=0"], "stlLibraryFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "intermediatesParentFolder": "D:\\A4U CODE DATA\\Mobile App\\Mobile.tariq.ameen\\Apply4U\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6632dn4x"}