import React from 'react';
import {Alert, Image, Pressable, StyleSheet, View} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import {Text} from '.';
import ProgressCircle from 'react-native-progress-circle';

interface IProps {
  percentCompleted: number;
  onViewProfileClick: () => void;
}

export const ProfileCompletionCard = (props: IProps) => {
  const {styles, colors} = useCurrentTheme(createStyles);
  return (
    <View style={styles.suggestionWrapper}>
      <Text styles={styles.dashboardHeadingStyle} text={'Profile'} />
      <View style={styles.banner}>
        <View style={styles.bannerBox1}>
          <ProgressCircle
            percent={props.percentCompleted}
            radius={35}
            borderWidth={6}
            color={colors.secondary}
            shadowColor={colors.white}
            bgColor={colors.primary}>
            <Text styles={{fontSize: 18}} text={`${props.percentCompleted}%`} />
          </ProgressCircle>
        </View>

        <View style={styles.bannerBox2}>
          <Pressable
            style={styles.buttonStyle}
            onPress={() => {
              props.onViewProfileClick();
            }}>
            <Text styles={styles.label2} text={'Edit Profile'} />
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    suggestionWrapper: {
      margin: 10,
      height:135,
    },
    dashboardHeadingStyle: {
      marginBottom: 10,
      fontWeight: 'bold',
      letterSpacing: 0.5,
      color: '#333',
    },
    banner: {
      flex: 1,
      height: 110,
      justifyContent:'center',
      padding: 10,
      borderRadius: theme.borderRadius(15),
      flexDirection: 'row',
      backgroundColor: theme.palette.primary,
    },
    bannerBox1: {
      width: '70%',
      justifyContent:'center'
    },
    bannerBox2: {
      width: '30%',
      padding: 5,
      justifyContent:'center',
    },
    BannerText1: {
      marginTop: 15,
      fontSize: 15,
      color: '#fff',
      fontWeight: 'bold',
    },
    buttonStyle: {
      height: 40,
      borderRadius: theme.borderRadius(5),
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.palette.secondary,
    },
    label: {
      fontSize: 13,
      color: '#fff',
      letterSpacing: 0.5,
    },
    label2: {
      ...theme.typography.bold.extraSmall,
    },
  });

  const colors = {
    primary: theme.palette.primary,
    secondary: theme.palette.secondary,
    white: theme.palette.white,
    info: theme.palette.info,
  };
  return {styles, colors};
};
