import { IEmailFrequency } from "./email-frequency.interface";
import { IJobType } from "./job-type.interface";
import { ISector } from "./sector.interface";

export interface IAutoSearch {
    Id?: number;
    UserId?: number;
    KeyWords?: string;
    AllofTheseWords?: string;
    ExactPharse?: string;
    AnyofTheseWords?: string;
    NoneofTheseWords?: string;
    LocationId?: number;
    LocationText?: string;
    SalaryDurationId?: any;
    SalaryFrom?: any;
    SalaryTo?: any;
    JobTypeIds?: string;
    JobCategoryIds?: string;
    PositionTypeIds?: string;
    CandidateTypeIds?: string;
    SectorId?: number;
    SearchWithInLast?: any;
    Radius?: number;
    IsSearchInTitle?: boolean;
    IsBroadMatch?: boolean;
    IsFilterApplyed?: boolean;
    IsJobSearch?: boolean;
    IsCvSearch?: boolean;
    IsBooleanSearch?: boolean;
    IsSearchByLocation?: boolean;
    IsSearchByIndustry?: boolean;
    EmailFrequencyId?: number;
    SavedSearchTypeId?: number;
    CreatedOn?: Date;
    IsActive?: boolean;
    Sector?: ISector;
    IsDefault?: any;
    JobTypes?: IJobType[];
    JobCategories?: any;
    CandidateTypes?: any;
    Location?: any;
    SalaryDuration?: any;
    PositionTypes?: any;
    EmailFrequency?: IEmailFrequency;
}