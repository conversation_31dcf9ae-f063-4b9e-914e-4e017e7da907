# C/C++ build system timings
generate_cxx_metadata
  [gap of 26ms]
  create-invalidation-state 57ms
  [gap of 17ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 124ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 35ms
generate_cxx_metadata completed in 60ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 39ms
  [gap of 13ms]
generate_cxx_metadata completed in 68ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 34ms
  [gap of 13ms]
generate_cxx_metadata completed in 66ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 37ms
  [gap of 12ms]
  write-metadata-json-to-file 20ms
generate_cxx_metadata completed in 90ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 33ms
  [gap of 17ms]
generate_cxx_metadata completed in 54ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 17ms
  [gap of 11ms]
generate_cxx_metadata completed in 41ms

