import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Keyboard,
  TouchableOpacity,
  Modal,
  FlatList,
  Switch,
} from 'react-native';
import { TextInput, Text, TouchableRipple, HelperText } from 'react-native-paper';

import { ITheme, useCurrentTheme } from '../../../theme';
import { navigate } from '../../../utility';
import { screens } from '../../../app.constant';
import { RegistrationStepComponent, A4UHuma, InteractiveMap } from '../../common';
import { a4uHuma } from '../../../assets';

const radiusOptions = [
  { label: '5 Miles', value: '5 Miles' },
  { label: '10 Miles', value: '10 Miles' },
  { label: '20 Miles', value: '20 Miles' },
  { label: '50 Miles', value: '50 Miles' }
];

export const NewRegisterStepFourWithMapView = () => {
  const styles = useCurrentTheme(createStyles);
  const [location, setLocation] = useState('London');
  const [radius, setRadius] = useState(20); // Changed to number for map integration
  const [showDropdown, setShowDropdown] = useState(false);
  const [useInteractiveMap, setUseInteractiveMap] = useState(true);
  const [locationError, setLocationError] = useState('');
  const [radiusError, setRadiusError] = useState('');

  const [touched, setTouched] = useState({
    location: false,
    radius: false,
  });

  const validateLocation = (text: string): boolean => {
    if (!text.trim()) {
      setLocationError('Location is required');
      return false;
    } else {
      setLocationError('');
      return true;
    }
  };

  const validateRadius = (radiusValue: number): boolean => {
    if (!radiusValue || radiusValue < 1) {
      setRadiusError('Radius is required');
      return false;
    } else {
      setRadiusError('');
      return true;
    }
  };

  const handleRadiusChange = (newRadius: number) => {
    setRadius(newRadius);
    if (touched.radius) {
      validateRadius(newRadius);
    }
  };

  const handleContinue = () => {
    Keyboard.dismiss();

    const isLocationValid = validateLocation(location);
    const isRadiusValid = validateRadius(radius);

    setTouched({
      location: true,
      radius: true,
    });

    if (isLocationValid && isRadiusValid) {
      // Here you would typically save the location preferences
      console.log('Location preferences:', { location, radius });
      navigate(screens.NewRegisterStepFive);
    }
  };

  const handleSkip = () => {
    Keyboard.dismiss();
    navigate(screens.NewRegisterStepFive);
  };

  const handleGoBack = () => {
    navigate(screens.NewRegisterStepThree);
  };

  const handleGoHome = () => {
    navigate(screens.Dashboard);
  };

  return (
    <RegistrationStepComponent
      title="Location Preferences"
      currentStep={4}
      totalSteps={5}
      onContinue={handleContinue}
      onSkip={handleSkip}
      onBack={handleGoBack}
      onHome={handleGoHome}
    >
      <A4UHuma
        image={a4uHuma}
        text="Set your preferred job search location and radius. You can use the interactive map or traditional controls."
        containerStyle={styles.humaContainer}
        imageSize={60}
        textStyle={styles.humaText}
        floating={true}
      />

      {/* Location Input */}
      <View style={styles.inputGroup}>
        <TextInput
          mode="outlined"
          label="Desired Location"
          value={location}
          onChangeText={(text) => {
            setLocation(text);
            if (touched.location) {
              validateLocation(text);
            }
          }}
          placeholder="Enter location"
          style={styles.input}
          outlineStyle={[
            styles.inputOutline,
            !!locationError && touched.location && styles.errorInputOutline
          ]}
          activeOutlineColor="#0E1C5D"
          outlineColor={!!locationError && touched.location ? "#FF3B30" : "#39608F"}
          error={!!locationError && touched.location}
          onBlur={() => {
            setTouched({ ...touched, location: true });
            validateLocation(location);
          }}
        />
        {!!locationError && touched.location && (
          <HelperText type="error" visible={true} style={styles.helperText}>
            {locationError}
          </HelperText>
        )}
      </View>

      {/* Map Toggle */}
      <View style={styles.toggleContainer}>
        <Text style={styles.toggleLabel}>Use Interactive Map</Text>
        <Switch
          value={useInteractiveMap}
          onValueChange={setUseInteractiveMap}
          trackColor={{ false: '#767577', true: '#0E1C5D' }}
          thumbColor={useInteractiveMap ? '#479ffd' : '#f4f3f4'}
        />
      </View>

      {/* Interactive Map or Traditional Controls */}
      {useInteractiveMap ? (
        <View style={styles.mapContainer}>
          <InteractiveMap
            initialRadius={radius}
            minRadius={1}
            maxRadius={50}
            onRadiusChange={handleRadiusChange}
            centerLabel={location}
            radiusUnit="miles"
            showGrid={true}
            showSlider={true}
          />
        </View>
      ) : (
        <View style={styles.inputGroup}>
          <View>
            <TextInput
              mode="outlined"
              label="Radius"
              value={`${radius} Miles`}
              placeholder="Select radius"
              style={styles.input}
              outlineStyle={[
                styles.inputOutline,
                !!radiusError && touched.radius && styles.errorInputOutline
              ]}
              right={<TextInput.Icon icon="chevron-down" onPress={() => setShowDropdown(true)} />}
              activeOutlineColor="#0E1C5D"
              outlineColor={!!radiusError && touched.radius ? "#FF3B30" : "#39608F"}
              error={!!radiusError && touched.radius}
              editable={false}
              onPressIn={() => {
                setShowDropdown(true);
                setTouched({ ...touched, radius: true });
                validateRadius(radius);
              }}
            />
            {!!radiusError && touched.radius && (
              <HelperText type="error" visible={true} style={styles.helperText}>
                {radiusError}
              </HelperText>
            )}
          </View>

          <Modal
            visible={showDropdown}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowDropdown(false)}
          >
            <TouchableOpacity
              style={styles.modalOverlay}
              activeOpacity={1}
              onPress={() => setShowDropdown(false)}
            >
              <View style={styles.dropdownContainer}>
                <FlatList
                  data={radiusOptions}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item }) => (
                    <TouchableRipple
                      style={styles.dropdownItem}
                      onPress={() => {
                        const numericValue = parseInt(item.value.split(' ')[0]);
                        setRadius(numericValue);
                        setShowDropdown(false);
                      }}
                      rippleColor="rgba(14, 28, 93, 0.1)"
                    >
                      <Text style={styles.dropdownItemText}>{item.label}</Text>
                    </TouchableRipple>
                  )}
                  ItemSeparatorComponent={() => <View style={styles.separator} />}
                />
              </View>
            </TouchableOpacity>
          </Modal>
        </View>
      )}
    </RegistrationStepComponent>
  );
};
