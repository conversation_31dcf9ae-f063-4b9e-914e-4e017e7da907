import React, { useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  View,
  Animated,
  Dimensions,
  Image as RNImage,
} from 'react-native';
import { Text } from 'react-native-paper';
import { ITheme, useCurrentTheme } from '../../theme';
import { a4uHuma } from '../../assets';

interface IProps {
  visible: boolean;
  onComplete?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const CVProcessingAnimation = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const { visible, onComplete } = props;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const humaRotateAnim = useRef(new Animated.Value(0)).current;
  const humaTransformAnim = useRef(new Animated.Value(0)).current;
  const humaScaleTransformAnim = useRef(new Animated.Value(0.6)).current;
  const documentFlyAnim = useRef(new Animated.Value(-100)).current;
  const documentScaleAnim = useRef(new Animated.Value(0.5)).current;
  const cvScanLineAnim = useRef(new Animated.Value(0)).current;
  const cvHighlightAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const particleAnim = useRef(new Animated.Value(0)).current;
  const dataStreamAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const aiBeamAnim = useRef(new Animated.Value(0)).current;
  const textExtractionAnim = useRef(new Animated.Value(0)).current;

  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const messages = [
    "I am receiving your CV...",
    "I am scanning your CV content...",
    "extracting your skills & experience...",
    "analyzing your qualifications...",
    "I have processed your CV successfully!"
  ];

  useEffect(() => {
    if (visible) {
      startAnimation();
    } else {
      resetAnimation();
    }
  }, [visible]);

  const startAnimation = () => {
    resetAnimation();

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(humaTransformAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(humaScaleTransformAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start(() => {
      startProcessingSequence();
    });
  };

  const startProcessingSequence = () => {
    setCurrentMessageIndex(0);
    setShowMessage(true);

    Animated.parallel([
      Animated.timing(documentFlyAnim, {
        toValue: 0,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.timing(documentScaleAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      })
    ]).start(() => {

      setTimeout(() => {
        setCurrentMessageIndex(1);
        startCVScanning();
      }, 500);
    });
  };

  const startCVScanning = () => {
    Animated.timing(aiBeamAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();

    Animated.loop(
      Animated.timing(cvScanLineAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      { iterations: 3 }
    ).start();

  
    Animated.loop(
      Animated.sequence([
        Animated.timing(humaRotateAnim, {
          toValue: 0.3,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.timing(humaRotateAnim, {
          toValue: -0.3,
          duration: 1200,
          useNativeDriver: true,
        }),
        Animated.timing(humaRotateAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      { iterations: 1 }
    ).start(() => {

      setTimeout(() => {
        setCurrentMessageIndex(2);
        startTextExtraction();
      }, 500);
    });
  };

  const startTextExtraction = () => {

    Animated.loop(
      Animated.timing(cvHighlightAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      { iterations: 4 }
    ).start();

    Animated.loop(
      Animated.timing(textExtractionAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      })
    ).start();

    Animated.timing(progressAnim, {
      toValue: 0.7,
      duration: 2000,
      useNativeDriver: false,
    }).start();

    Animated.loop(
      Animated.timing(particleAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();

    setTimeout(() => {
      setCurrentMessageIndex(3);
      startFinalAnalysis();
    }, 2000);
  };

  const startFinalAnalysis = () => {
    Animated.timing(progressAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: false,
    }).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
      { iterations: 3 }
    ).start();

    setTimeout(() => {
      setCurrentMessageIndex(4);
      completeAnimation();
    }, 1500);
  };

  const completeAnimation = () => {
    setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        onComplete?.();
      });
    }, 1000);
  };

  const resetAnimation = () => {
    fadeAnim.setValue(0);
    scaleAnim.setValue(0.8);
    humaRotateAnim.setValue(0);
    humaTransformAnim.setValue(0);
    humaScaleTransformAnim.setValue(0.6);
    documentFlyAnim.setValue(-100);
    documentScaleAnim.setValue(0.5);
    cvScanLineAnim.setValue(0);
    cvHighlightAnim.setValue(0);
    progressAnim.setValue(0);
    particleAnim.setValue(0);
    dataStreamAnim.setValue(0);
    pulseAnim.setValue(1);
    aiBeamAnim.setValue(0);
    textExtractionAnim.setValue(0);
    setCurrentMessageIndex(0);
    setShowMessage(false);
  };

  if (!visible) return null;

  const humaRotation = humaRotateAnim.interpolate({
    inputRange: [-1, 0, 1],
    outputRange: ['-3deg', '0deg', '3deg'],
  });

  const humaTranslateX = humaTransformAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0],
  });

  const humaTranslateY = humaTransformAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-250, 0],
  });

  const documentTranslateX = documentFlyAnim.interpolate({
    inputRange: [-1, 0],
    outputRange: [-screenWidth, 0],
  });

  const cvScanLineTranslateY = cvScanLineAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 120],
  });

  const cvHighlightOpacity = cvHighlightAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.3, 1, 0.3],
  });

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  const particleOpacity = particleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 1, 0],
  });

  const aiBeamOpacity = aiBeamAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.8],
  });

  const textExtractionTranslateX = textExtractionAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -80],
  });

  const textExtractionOpacity = textExtractionAnim.interpolate({
    inputRange: [0, 0.3, 0.7, 1],
    outputRange: [0, 1, 1, 0],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        }
      ]}
    >
      <View style={styles.overlay} />

      <View style={styles.content}>
        <View style={styles.processingArea}>
          <View style={styles.humaContainer}>
            <Animated.View
              style={[
                styles.humaWrapper,
                {
                  transform: [
                    { translateX: humaTranslateX },
                    { translateY: humaTranslateY },
                    { rotate: humaRotation },
                    { scale: humaScaleTransformAnim },
                    { scale: pulseAnim }
                  ],
                }
              ]}
            >
              <RNImage
                source={a4uHuma}
                style={styles.humaImage}
                resizeMode="contain"
              />
            </Animated.View>
          </View>

          <Animated.View
            style={[
              styles.aiBeam,
              { opacity: aiBeamOpacity }
            ]}
          />

          <View style={styles.cvContainer}>
            <Animated.View
              style={[
                styles.cvDocument,
                {
                  transform: [
                    { translateX: documentTranslateX },
                    { scale: documentScaleAnim }
                  ],
                }
              ]}
            >
              <Animated.View
                style={[
                  styles.cvBackground,
                  { opacity: cvHighlightOpacity }
                ]}
              />

              <View style={styles.cvContent}>
                <View style={styles.cvProfileSection}>
                  <View style={styles.profileIcon}>
                    <Text style={styles.profileIconText}>👤</Text>
                  </View>
                  <View style={styles.profileInfo}>
                    <View style={styles.cvNameLine} />
                    <View style={styles.cvTitleLine} />
                  </View>
                </View>

                <View style={styles.cvTextSection}>
                  <View style={styles.cvSectionHeader} />
                  <View style={styles.cvLine} />
                  <View style={styles.cvLine} />
                  <View style={styles.cvLineShort} />

                  <View style={styles.cvSectionHeader} />
                  <View style={styles.cvLine} />
                  <View style={styles.cvLineShort} />
                  <View style={styles.cvLine} />

                  <View style={styles.cvSectionHeader} />
                  <View style={styles.cvLineShort} />
                  <View style={styles.cvLine} />
                </View>
              </View>

              <Animated.View
                style={[
                  styles.cvScanLine,
                  {
                    transform: [{ translateY: cvScanLineTranslateY }],
                    opacity: cvScanLineAnim,
                  }
                ]}
              />
            </Animated.View>

            <Animated.View
              style={[
                styles.cvParticle1,
                { opacity: particleOpacity }
              ]}
            />
            <Animated.View
              style={[
                styles.cvParticle2,
                { opacity: particleOpacity }
              ]}
            />
            <Animated.View
              style={[
                styles.cvParticle3,
                { opacity: particleOpacity }
              ]}
            />
          </View>
        </View>

        {showMessage && (
          <View style={styles.messageContainer}>
            <Text style={styles.messageText}>
              {messages[currentMessageIndex]}
            </Text>
          </View>
        )}

        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <Animated.View
              style={[
                styles.progressBar,
                { width: progressWidth }
              ]}
            />
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

const createStyles = (theme: ITheme) => {
  return StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'white',
    },
    content: {
      alignItems: 'center',
      padding: 20,
    },
    processingArea: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      marginBottom: 30,
    },
    humaContainer: {
      position: 'relative',
      marginRight: 20,
    },
    humaWrapper: {
      alignItems: 'center',
    },
    humaImage: {
      width: 100,
      height: 100,
    },
    aiBeam: {
      position: 'absolute',
      left: 90,
      top: 50,
      width: 80,
      height: 2,
      backgroundColor: '#007CFF',
      shadowColor: '#007CFF',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.8,
      shadowRadius: 4,
    },
    cvContainer: {
      position: 'relative',
      flex: 1,
    },
    cvDocument: {
      width: 120,
      height: 150,
      backgroundColor: 'white',
      borderRadius: 8,
      borderWidth: 2,
      borderColor: '#0E1C5D',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 8,
      padding: 10,
    },
    cvBackground: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: '#E3F2FD',
      borderRadius: 6,
    },
    cvContent: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 4,
    },
    cvProfileSection: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
      paddingBottom: 6,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    profileIcon: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: '#F5F5F5',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 6,
    },
    profileIconText: {
      fontSize: 10,
      color: '#666',
    },
    profileInfo: {
      flex: 1,
    },
    cvNameLine: {
      height: 4,
      backgroundColor: '#333',
      borderRadius: 1,
      marginBottom: 2,
      width: '80%',
    },
    cvTitleLine: {
      height: 2,
      backgroundColor: '#666',
      borderRadius: 1,
      width: '60%',
    },
    cvTextSection: {
      flex: 1,
      justifyContent: 'space-around',
    },
    cvSectionHeader: {
      height: 3,
      backgroundColor: '#0E1C5D',
      borderRadius: 1,
      marginVertical: 3,
      width: '50%',
    },
    cvLine: {
      height: 2,
      backgroundColor: '#BDBDBD',
      borderRadius: 1,
      marginVertical: 1,
    },
    cvLineShort: {
      height: 2,
      backgroundColor: '#BDBDBD',
      borderRadius: 1,
      marginVertical: 1,
      width: '70%',
    },
    cvScanLine: {
      position: 'absolute',
      left: 0,
      right: 0,
      height: 3,
      backgroundColor: '#007CFF',
      shadowColor: '#007CFF',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 1,
      shadowRadius: 4,
      top: 10,
    },

    cvParticle1: {
      position: 'absolute',
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: '#007CFF',
      top: -10,
      left: 20,
    },
    cvParticle2: {
      position: 'absolute',
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#00C851',
      top: 30,
      right: -15,
    },
    cvParticle3: {
      position: 'absolute',
      width: 5,
      height: 5,
      borderRadius: 2.5,
      backgroundColor: '#FF6B35',
      bottom: -10,
      left: 30,
    },

    messageContainer: {
      marginTop: 20,
      paddingHorizontal: 20,
    },
    messageText: {
      fontSize: 18,
      fontFamily: 'Poppins-SemiBold',
      color: '#0E1C5D',
      textAlign: 'center',
    },
    progressContainer: {
      marginTop: 30,
      width: 250,
    },
    progressBackground: {
      height: 4,
      backgroundColor: '#E0E0E0',
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
      backgroundColor: '#007CFF',
      borderRadius: 2,
    },
  });
};
