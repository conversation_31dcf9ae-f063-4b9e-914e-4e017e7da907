import React from 'react';
import {connect} from 'react-redux';
import {
  IUserDetail,
  IApplySuccessfulParam,
  IApplyNowParam,
  IResume,
  IJobApplication,
  IFileDescription,
  ILoadingIndicator,
} from '../../../interfaces';
import {IApplicationState} from '../../../redux';
import {ApplyNowView} from './apply-now.view';
import {
  canGoBack,
  getExtensionFromFileName,
  getParams,
  goBack,
  isNullOrWhitespace,
  navigate,
  selectFile,
} from '../../../utility';
import {Alert} from 'react-native';
import {
  alreadyAppliedForJob,
  docFileExtension,
  screens,
  validResumeExtensions,
} from '../../../app.constant';
import {jobApplicationApi, resumeApi} from '../../../http';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {fileApi} from '../../../http/file.http';
import {showErrorMessage, showSuccessMessage} from '../../../external/toaster';
import {hideLoading, showLoading} from '../../../redux/actions';
import {IAction} from '../../../redux/actions/action.interface';

interface IProps {
  loginUser: IUserDetail | null;
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => IAction<ILoadingIndicator>;
  hideLoading: () => IAction<ILoadingIndicator>;
}

const ApplyNowContainer = (props: IProps) => {
  const [newCV, setNewCV] = React.useState<DocumentPickerResponse>();
  const [additionalFile, setAdditionalFile] =
    React.useState<DocumentPickerResponse | null>();
  const [additionalFileName, setAdditionalFileName] =
    React.useState<string | null>('');
  const [resume, setResume] = React.useState<IResume[]>([]);
  const [selectedExistingResume, setSelectedExistingResume] =
    React.useState<IResume>();
  const [fileName, setFileName] = React.useState<string>('');
  const [fileExtension, setFileExtension] =
    React.useState<string>('');

  const [coverLetterText, setCoverLetterText] = React.useState<string>('');

  React.useEffect(() => {
    jobApplicationApi
      .isAlreadyApplied(props.loginUser?.Id ?? 0, job?.jobId ?? 0)
      .then(result => {
        if (result === alreadyAppliedForJob && canGoBack()) {
          goBack();
        }
      });
  }, []);

  const uploadNewCV = () => {
    selectFile('Resume').then(
      result => {
        if (result) {
          if (result.size && result.size / 1024 > 1024) {
            Alert.alert('File size must be less than or equal to 1 MB.');
            return;
          } else {
            if (result && !isNullOrWhitespace(result.name)) {
              const extension = getExtensionFromFileName(result.name);

              if (validResumeExtensions.includes(extension)) {
                setNewCV({...result});
                setFileNameAndExtension(
                  result.name,
                  getExtensionFromFileName(result.name),
                );
              } else {
                Alert.alert('File Format Not Support For Resume.');
              }
            }
          }
        }
      },
      error => {},
    );
  };

  const addAdditionalFile = () => {
    selectFile('Resume').then(
      result => {
        if (result) {
          if (result.size && result.size / 1024 > 1024) {
            Alert.alert('File size must be less than or equal to 1 MB.');
            return;
          } else {
            if (result && !isNullOrWhitespace(result.name)) {
              const extension = getExtensionFromFileName(result.name);

              if (validResumeExtensions.includes(extension)) {
                setAdditionalFile({...result});
                setAdditionalFileName(result.name);
              } else {
                Alert.alert('File Format Not Support For Resume.');
              }
            }
          }
        }
      },
      error => {},
    );
  };

  const getCandidateResume = React.useCallback(() => {
    resumeApi.getResume(props.loginUser?.Id ?? 0).then(
      results => {
        if (results && results.length > 0) {
          setResume({...results});
          let selectedResume = {...results[0]};
          const defaultResume = results.find(m => m.IsDefault == true);
          if (defaultResume && defaultResume.Id > 0) {
            selectedResume = {...defaultResume};
          }

          if (selectedResume && selectedResume.Id > 0) {
            setSelectedExistingResume({...selectedResume});

            if (selectedResume.File && selectedResume.File.Id > 0) {
              setFileNameAndExtension(
                selectedResume.File.FileName,
                selectedResume.File.Extension,
              );
            }
          }
        }
      },
      error => {},
    )
    .catch((e) => {});
  }, [props.loginUser?.Id]);

  const setFileNameAndExtension = (fileName: string | null, extension: string) => {
    setFileName(fileName ?? 'File');
    setFileExtension(extension);
  };

  const job = getParams<IApplyNowParam>();

  const submit = (includeCoverLetterText: boolean) => {
    //get the resume Id or upload the new cv and get the resume id
    //upload additional file if exists
    //check if we need to add cover letter

    props.showLoading();
    jobApplicationApi
      .isAlreadyApplied(props.loginUser?.Id ?? 0, job?.jobId ?? 0)
      .then(result => {
        if (result === alreadyAppliedForJob) {
          props.hideLoading();
          showSuccessMessage('You already have applied for this job.');
          if (canGoBack()) {
            goBack();
          }
        } else {
          uploadFilesAndPostJobApplication(includeCoverLetterText);
        }
      })
      .catch(error => {
        props.hideLoading();
        showErrorMessage('Something went wrong. Please try again');
      });
  };

  const uploadFilesAndPostJobApplication = (
    includeCoverLetterText: boolean,
  ) => {
    let allPromisesToAwait: any[] = [];

    if (additionalFile) {
      let data = new FormData();
      data.append('DocumentFile', additionalFile);
      allPromisesToAwait.push(fileApi.uploadFile(data));
    }

    if (newCV) {
      let data = new FormData();
      data.append('CVFile', newCV);
      allPromisesToAwait.push(fileApi.uploadFile(data));
    }

    if (allPromisesToAwait && allPromisesToAwait.length > 0) {
      Promise.all(allPromisesToAwait)
        .then(responses => {
          let additionalFileUploadDescription: IFileDescription | null = null;
          let newCVFileUploadDescription: IFileDescription | null = null;

          if (responses && responses.length > 0) {
            if (responses.length === 1) {
              const fileDescriptionResponses: IFileDescription[] = responses[0];
              if (
                fileDescriptionResponses &&
                fileDescriptionResponses.length > 0
              ) {
                if (additionalFile) {
                  additionalFileUploadDescription = {
                    ...fileDescriptionResponses[0],
                  };
                } else if (newCV) {
                  newCVFileUploadDescription = {...fileDescriptionResponses[0]};
                }
              }
            } else if (responses.length === 2) {
              const additionalFileDescriptions: IFileDescription[] =
                responses[0];
              const newCVFileDescriptions: IFileDescription[] = responses[1];

              if (
                additionalFileDescriptions &&
                additionalFileDescriptions.length > 0
              ) {
                additionalFileUploadDescription = {
                  ...additionalFileDescriptions[0],
                };
              }

              if (newCVFileDescriptions && newCVFileDescriptions.length > 0) {
                newCVFileUploadDescription = {...newCVFileDescriptions[0]};
              }
            }
          }

          postJobApplication(
            additionalFileUploadDescription,
            newCVFileUploadDescription,
            includeCoverLetterText,
          );
        })
        .catch(error => {
          props.hideLoading();
          showErrorMessage(
            'Something went wrong while uploading the documents.',
          );
        });
    } else {
      postJobApplication(null, null, includeCoverLetterText);
    }
  };

  const postJobApplication = (
    additionalFileDescription: IFileDescription | null,
    newCvFileDescription: IFileDescription | null,
    includeCoverLetter: boolean,
  ) => {
    const jobApplication: IJobApplication = {
      CoveringLetter: includeCoverLetter ? coverLetterText : '',
      IsDeleted: false,
      IsViewedByRecruiter: false,
      JobId: job?.jobId ?? 0,
      ResumeId:
        newCvFileDescription == null || newCvFileDescription == undefined
          ? selectedExistingResume?.Id
          : 0,
      UserId: props.loginUser?.Id,
      AppliedOn: new Date(),
      IsEmailSimilarJobs: false,
      IsPersonalityAssessment: false,
      FileDescription: newCvFileDescription,
      AttachedDocumentDescription: additionalFileDescription,
      Id: 0,
    };

    jobApplicationApi
      .postApplication(jobApplication)
      .then(response => {
        props.hideLoading();
        navigateToSuccesScreen();
      })
      .catch(error => {
        props.hideLoading();
        showErrorMessage('Failed to post the application');
      });
  };

  const navigateToSuccesScreen = () => {
    navigate<IApplySuccessfulParam>(screens.ApplySuccessful, {
      jobTitle: job?.jobTitle ?? '',
    });
  };

  const handleCoverLetterTextChange = (value: string) => {
    setCoverLetterText(value);
  };
  const resetAdditionalFileSelection = () => {
    setAdditionalFileName('');
    setAdditionalFile(null);
  };

  React.useEffect(() => {
    getCandidateResume();
  }, []);

  return (
    <ApplyNowView
      loginUserName={props.loginUser?.UserProfile?.FullName}
      jobTitle={job?.jobTitle ?? ''}
      cvFileName={fileName}
      fileExtension={fileExtension}
      additionalFileName={additionalFileName ?? 'File'}
      coverLetterText={coverLetterText}
      uploadNewCVClick={uploadNewCV}
      addAdditionalFile={addAdditionalFile}
      resetAdditionalFileSelection={resetAdditionalFileSelection}
      hanldeCoverLetterTextChange={handleCoverLetterTextChange}
      canGoBack={canGoBack}
      goBack={goBack}
      submit={submit}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser: state.loginUser,
  };
};

const mapDispatchToProps = {
  showLoading: showLoading,
  hideLoading: hideLoading,
};

const connectedApplyNowContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(ApplyNowContainer);

export {connectedApplyNowContainer as ApplyNowContainer};
