import React from 'react';

import {
  createD<PERSON>er<PERSON>avi<PERSON>or,
  DrawerContentComponentProps,
} from '@react-navigation/drawer';
import { useSelector } from 'react-redux';

import {
  DashboardContainer,
  ForgotPasswordContainer,
  JobSearchFilterContainer,
  LoginContainer,
  NewLoginContainer,
  RegisterStepOneContainer,
  RegisterStepThreeContainer,
  RegisterStepTwoContainer,
  JobSearchResultsContainer,
  HomeContainer,
  JobDetailContainer,
  NotificationsContainer,
  RegisterStepFourContainer,
  RegisterStepFiveContainer,
  PricingContainer,
  IntroSliderContainer,
  ApplyNowContainer,
  ApplySuccessfulContainer,
  HiddenJobsContainer,
  MyJobsContainer,
  ShortlistedJobsContainer,
  BrowserContainer,
  NewRegisterIntroContainer,
  CvUploadContainer,
  NewRegisterStepOneContainer,
  NewRegisterStepTwoContainer,
  NewRegisterStepThreeContainer,
  NewRegisterStepFourContainer,
  NewRegisterStepFiveContainer,
  CreateAccountContainer,
  OtpVerificationContainer,
  NewForgotPasswordContainer
} from '../components';
import { SuccessScreenComponent } from '../components/common';

import { DrawerContentComponent } from './drawer-content.component';
import { screens } from '../app.constant';
import { isAuthenticated } from '../utility';
import { IApplicationState } from '../redux';
import { SettingsContainer } from '../components/user/settings/settings.container';

const Drawer = createDrawerNavigator();
const DrawerNavigator = () => {
  const isFirstVisit = useSelector(
    (state: IApplicationState) => state.isFirstVisit,
  );

  const initialScreen = isFirstVisit
    ? screens.IntroSlider.screen
    : isAuthenticated()
      ? screens.Dashboard.screen
      : screens.Login.screen;
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerPosition: 'right',
      }}
      drawerContent={(props: DrawerContentComponentProps) => {
        return <DrawerContentComponent {...props} />;
      }}
      backBehavior={'history'}
      initialRouteName={initialScreen}>
      <Drawer.Screen
        name={screens.Home.screen}
        options={{
          drawerLabel: screens.Home.drawerLabel,
        }}
        component={HomeContainer}
      />
      <Drawer.Screen
        name={screens.Dashboard.screen}
        options={{
          drawerLabel: screens.Dashboard.drawerLabel,
          unmountOnBlur: true,
        }}
        component={DashboardContainer}
      />
      <Drawer.Screen
        name={screens.JobSearchResults.screen}
        options={{
          drawerLabel: screens.JobSearchResults.drawerLabel,
        }}
        component={JobSearchResultsContainer}
      />

      <Drawer.Screen
        name={screens.Notifications.screen}
        options={{
          drawerLabel: screens.Notifications.drawerLabel,
          unmountOnBlur:true
        }}
        component={NotificationsContainer}
      />

      <Drawer.Screen
        name={screens.HiddenJobs.screen}
        options={{
          drawerLabel: screens.HiddenJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={HiddenJobsContainer}
      />

      <Drawer.Screen
        name={screens.MyJobs.screen}
        options={{
          drawerLabel: screens.MyJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={MyJobsContainer}
      />

<Drawer.Screen
        name={screens.ShortlistedJobs.screen}
        options={{
          drawerLabel: screens.ShortlistedJobs.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ShortlistedJobsContainer}
      />

    <Drawer.Screen
        name={screens.Settings.screen}
        options={{
          drawerLabel: screens.Settings.drawerLabel,
        }}
        component={SettingsContainer}
      />

      <Drawer.Screen
        name={screens.Login.screen}
        options={{
          drawerLabel: screens.Login.drawerLabel,
        }}
        component={NewLoginContainer}
      />

      <Drawer.Screen
        name={screens.NewLogin.screen}
        options={{
          drawerLabel: screens.NewLogin.drawerLabel,
        }}
        component={NewLoginContainer}
      />

      <Drawer.Screen
        name={screens.JobDetail.screen}
        options={{
          drawerLabel: screens.JobDetail.drawerLabel,
          unmountOnBlur:true,
        }}
        component={JobDetailContainer}
      />

      <Drawer.Screen
        name={screens.JobSearchFilter.screen}
        options={{
          drawerLabel: screens.JobSearchFilter.drawerLabel,
          unmountOnBlur:true,
        }}
        component={JobSearchFilterContainer}
      />

      <Drawer.Screen
        name={screens.CreateAccount.screen}
        options={{
          drawerLabel: screens.CreateAccount.drawerLabel,
        }}
        component={CreateAccountContainer}
      />

      <Drawer.Screen
        name={screens.OtpVerification.screen}
        options={{
          drawerLabel: screens.OtpVerification.drawerLabel,
        }}
        component={OtpVerificationContainer}
      />

      <Drawer.Screen
        name={screens.SuccessScreen.screen}
        options={{
          drawerLabel: screens.SuccessScreen.drawerLabel,
        }}
        component={SuccessScreenComponent}
      />

      <Drawer.Screen
        name={screens.NewForgotPassword.screen}
        options={{
          drawerLabel: screens.NewForgotPassword.drawerLabel,
        }}
        component={NewForgotPasswordContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterIntro.screen}
        options={{
          drawerLabel: screens.NewRegisterIntro.drawerLabel,
        }}
        component={NewRegisterIntroContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterStepOne.screen}
        options={{
          drawerLabel: screens.NewRegisterStepOne.drawerLabel,
        }}
        component={NewRegisterStepOneContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterStepTwo.screen}
        options={{
          drawerLabel: screens.NewRegisterStepTwo.drawerLabel,
        }}
        component={NewRegisterStepTwoContainer}
      />

      <Drawer.Screen
        name={screens.CvUpload.screen}
        options={{
          drawerLabel: screens.CvUpload.drawerLabel,
        }}
        component={CvUploadContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterStepThree.screen}
        options={{
          drawerLabel: screens.NewRegisterStepThree.drawerLabel,
        }}
        component={NewRegisterStepThreeContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterStepFour.screen}
        options={{
          drawerLabel: screens.NewRegisterStepFour.drawerLabel,
        }}
        component={NewRegisterStepFourContainer}
      />

      <Drawer.Screen
        name={screens.NewRegisterStepFive.screen}
        options={{
          drawerLabel: screens.NewRegisterStepFive.drawerLabel,
        }}
        component={NewRegisterStepFiveContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepOne.screen}
        options={{
          drawerLabel: screens.RegisterStepOne.drawerLabel,
        }}
        component={RegisterStepOneContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepTwo.screen}
        options={{
          drawerLabel: screens.RegisterStepTwo.drawerLabel,
        }}
        component={RegisterStepTwoContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepThree.screen}
        options={{
          drawerLabel: screens.RegisterStepThree.drawerLabel,
        }}
        component={RegisterStepThreeContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepFour.screen}
        options={{
          drawerLabel: screens.RegisterStepFour.drawerLabel,
        }}
        component={RegisterStepFourContainer}
      />

      <Drawer.Screen
        name={screens.RegisterStepFive.screen}
        options={{
          drawerLabel: screens.RegisterStepFive.drawerLabel,
        }}
        component={RegisterStepFiveContainer}
      />
      <Drawer.Screen
        name={screens.ForgotPassword.screen}
        options={{
          drawerLabel: screens.ForgotPassword.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ForgotPasswordContainer}
      />
      <Drawer.Screen
        name={screens.Pricing.screen}
        options={{
          drawerLabel: screens.Pricing.drawerLabel,
        }}
        component={PricingContainer}
      />

      <Drawer.Screen
        name={screens.IntroSlider.screen}
        options={{
          drawerLabel: screens.IntroSlider.drawerLabel,
        }}
        component={IntroSliderContainer}
      />

      <Drawer.Screen
        name={screens.ApplyNow.screen}
        options={{
          drawerLabel: screens.ApplyNow.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ApplyNowContainer}
      />
      <Drawer.Screen
        name={screens.ApplySuccessful.screen}
        options={{
          drawerLabel: screens.ApplySuccessful.drawerLabel,
          unmountOnBlur: true,
        }}
        component={ApplySuccessfulContainer}
      />
      <Drawer.Screen
        name={screens.Browser.screen}
        options={{
          drawerLabel: screens.Browser.drawerLabel,
          unmountOnBlur: true,
        }}
        component={BrowserContainer}
      />
    </Drawer.Navigator>
  );
};

export { DrawerNavigator };
