import React from 'react';
import { Animated, Keyboard, StyleSheet, View } from 'react-native';
import { useCurrentTheme, ITheme } from '../../../theme';
import { Text, Button, MainLayout, JobCardList, KeywordsAutoComplete } from '../../common';
import { getThousandsSeparated, isNullOrWhitespace } from '../../../utility';
import { IJobSearchResponse, ISearchParam } from '../../../interfaces';
import { SearchFiltersContainer } from '../search-filters/search-filters.container';
import { zIndex } from '../../../app.constant';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faList, faBell } from '@fortawesome/free-solid-svg-icons';

interface IProps {
  searchResults: IJobSearchResponse[];
  searchParam?: ISearchParam | null;
  sortBy?: 'Relevancy' | 'Date';
  isMoreResultsExists: boolean;
  showFilters: boolean;
  keywords: string;
  totalResults:number;
  isLoading:boolean;
  setKeywords: (keywords: string) => void;
  onSelectKeywords: (searchKeywords: string) => void;
  favJobsAddOrRemove: (jobId: number, isFav: boolean) => void;
  goToJobDetail: (jobId: number) => void;
  handleSortChange?: () => void;
  loadMore: () => void;
  onHideJob: (jobId: number) => void;
  handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
  showHideFilters:() => void;
  hanldeEmailMeMatchingJobs:() => void;
}

const JobSearchResultsView = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const [isKeywordsAutoCompleteShowingSuggestions, setIsKeywordsAutoCompleteShowingSuggestions] = React.useState<boolean>(false);
  return (
    <MainLayout>
      <View style={isKeywordsAutoCompleteShowingSuggestions ? styles.keywordsViewHeight : styles.keywordsView}>
        <KeywordsAutoComplete
        autoCompletekey={'jobSearchResultsKeywords'}
        key='Different'
          name={'jobSearchResultsKeywords'}
          placeholder={'What (e.g Job Title, Skill)'}
          styles={styles.inputField}
          value={props.keywords}
          onChange={(value: string, name: string) => {
            props.setKeywords(value);
          }}
          onOpen={() => { 
            setIsKeywordsAutoCompleteShowingSuggestions(true); }
          }
          onClose={() => { setIsKeywordsAutoCompleteShowingSuggestions(false); }}
          onSelect={props.onSelectKeywords}
          addTypoToItems={true}
        />

        {!isKeywordsAutoCompleteShowingSuggestions && <View style={{ marginTop: 10,flexDirection:'row' }}>
          <Button
            styles={styles.btnFilter}
            textStyles={styles.btnFilterTextStyle}
            pressed={() => {
              Keyboard.dismiss();
              props.showHideFilters();
            }}>
            <View style={{ flex: 1, flexDirection: 'row' }}>
              <View style={{ flex: 1, justifyContent: 'center', }}>
                <Text styles={styles.buttonTextDefault} text={`Filter`} />
              </View>
              <View style={{ flex: 1, justifyContent: 'center', }}>
                <FontAwesomeIcon icon={faList} color={styles.faColor} style={{ alignSelf: 'flex-end', marginRight: 10, }} />
              </View>
            </View>
          </Button>
          <Button
            styles={styles.btnMatchingJobs}
            textStyles={styles.btnFilterTextStyle}
            pressed={() => {
              Keyboard.dismiss();
              props.hanldeEmailMeMatchingJobs();
            }}>
            <View style={{ flex: 1, flexDirection: 'row' }}>
            <View style={{ flex: 1, justifyContent: 'center', }}>
                <FontAwesomeIcon icon={faBell} color={styles.faColor} style={{ alignSelf: 'flex-start', marginLeft: 10, }} />
              </View>
              <View style={{ flex: 7, justifyContent: 'center', }}>
                <Text styles={styles.buttonTextMatchingJobs} text={`Email me matching jobs`} />
              </View>
            </View>
          </Button>
        </View>}

      </View>
      {!props.showFilters && !!props.searchResults && props.searchResults.length > 0 && (!!props.searchParam?.keywords && props.searchParam?.keywords.trim() != '') && <View>
        <Text styles={styles.totalRecordsTextStyle} numberOfLines={2} text={`${getThousandsSeparated(props.totalResults)+(props.totalResults >= 10000 ? "+":"")} "${!!props.searchParam?.keywords && props.searchParam?.keywords.trim() != '' ? props.searchParam?.keywords : ''}" jobs`} />
        </View>}

        {!props.showFilters && !!props.searchResults && props.searchResults.length > 0 && (!props.searchParam?.keywords || props.searchParam?.keywords.trim() == '') && <View>
        <Text styles={styles.totalRecordsTextStyle} numberOfLines={2} text={`${getThousandsSeparated(props.totalResults)} jobs`} />
        </View>}

        {!props.showFilters && (props.totalResults <= 0) && !props.isLoading && (!props.searchResults || props.searchResults.length <= 0) && <View>
        <Text styles={styles.totalRecordsTextStyle} numberOfLines={2} text={`no jobs found`} />
        </View>}
        {!!props.showFilters && <View style={{flex:1,marginBottom:20}}><SearchFiltersContainer key={'JobSearchResultsPage'} isSearchResultsPage={true} /></View>}
      {!props.showFilters && <View style={styles.listContainer}>
        <JobCardList
          jobResults={props.searchResults}
          isMoreResultsExists={props.isMoreResultsExists}
          isSwipeLeftEnabled={true}
          isSwipeRightEnabled={true}
          swipeRightText='Hide'
          isSwipeable={true}
          showFavIcon={true}
          showShareIcon={true}
          goToJobDetail={props.goToJobDetail}
          handleEasyApply={props.handleEasyApply}
          favJobsAddOrRemove={props.favJobsAddOrRemove}
          loadMore={props.loadMore}
          onSwipeRight={props.onHideJob}
        />
      </View>}
    </MainLayout>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    header2: {
      height: 60,
      width: '100%',
      flexWrap: 'wrap',
      flexDirection: 'row',
      backgroundColor: '#5178e1',
    },
    headerButton: {
      width: '40%',
      textAlign: 'right',
    },
    headerCol: {
      width: '55%',
      marginLeft: 10,
    },
    headerText: {
      color: '#fff',
      fontSize: 13,
      marginTop: 12,
      fontWeight: 'bold',
      alignItems: 'center',
      justifyContent: 'center',
    },
    btnStyle: {
      fontSize: 11,
      borderRadius: 5,
      color: '#333333',
      backgroundColor: '#fdba4f',
    },
    listContainer: {
      flex: 1,
      marginBottom: 5
    },
    inputField: {
      borderRadius: 6,
    },
    keywordsViewHeight: {
      padding: theme.spacing(7.5),
      width: '100%',
      backgroundColor: theme.palette.primary,
      ...theme.zIndex(zIndex.locationAutoCompleteContainer),
      height: '90%',
    },
    keywordsView: {
      padding: theme.spacing(7.5),
      width: '100%',
      backgroundColor: theme.palette.primary,
      height: 140,
      ...theme.zIndex(zIndex.locationAutoCompleteContainer),
    },
    btnFilter: {
      ...theme.buttons.secondary,
      width: '35%',
      backgroundColor: theme.palette.primary,
      borderWidth: 1,
      borderColor: theme.palette.lightGray,
      borderRadius: theme.borderRadius(10),
      height:35,
    },
    btnMatchingJobs: {
      ...theme.buttons.secondary,
      width: '60%',
      backgroundColor: theme.palette.primary,
      borderWidth: 1,
      borderColor: theme.palette.lightGray,
      borderRadius: theme.borderRadius(10),
      ...theme.zIndex(1),
      marginLeft:theme.spacing(7),
      height:35,
    },
    btnFilterTextStyle: {
      color: 'green',
      ...theme.zIndex(1),
    },
    buttonIcon: {

      flexDirection: 'row',


      alignSelf: 'flex-end',
      ...theme.zIndex(1),
    },
    buttonTextDefault: {
      ...theme.typography.bold.small,
      color: theme.palette.white,
      marginLeft: 10,
    },
    buttonTextMatchingJobs: {
      ...theme.typography.bold.small,
      color: theme.palette.white,
      marginLeft: 10,
      alignSelf:'flex-start'
    },
    totalRecordsTextStyle:{
      ...theme.typography.bold.medium,
      color:theme.palette.lightGray,
      alignSelf:'center',
      marginTop:theme.spacing(7),
      marginBottom:theme.spacing(3)
    }

  });

  const colors = { faColor: theme.palette.white };
  return { ...styles, ...colors };
};

export { JobSearchResultsView };
