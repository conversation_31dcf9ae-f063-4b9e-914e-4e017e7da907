import React from 'react';
import { connect } from 'react-redux';
import { defaultPageNumberJobSearch, defaultPageSizeSearchResults } from '../../../app.constant';
import { userApi } from '../../../http';
import { ILoadingIndicator, INotification, IUserDetail } from '../../../interfaces';
import { IApplicationState } from '../../../redux';
import { hideLoading, showLoading } from '../../../redux/actions';
import { IAction } from '../../../redux/actions/action.interface';
import {NotificationsView} from './notifications.view';

interface IProps{
  loginUser:IUserDetail | null;
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => IAction<ILoadingIndicator>;
  hideLoading: () => IAction<ILoadingIndicator>;
}
const NotificationsContainer = (props:IProps) => {
  const [notifications,setNotifications] = React.useState<INotification[]>([]);
  const [isMoreResultsExists,setIsMoreResultsExists] = React.useState<boolean>(true);
  const [loadingIsInProcess,setLoadingIsInProcess] = React.useState<boolean>(false);
  const [currentPageNumber, setCurrentPageNumber] = React.useState<number>(
    defaultPageNumberJobSearch,
  );
  const [totalNotifications,setTotalNotifications] = React.useState<number>(0);

  const [currentPageSize, setCurrentPageSize] = React.useState<number>(
    defaultPageSizeSearchResults,
  );

  const loadMore = () => {
    const pageNumber = currentPageNumber + 1;
    loadResults(pageNumber, currentPageSize, true, true);
  };

  React.useEffect(() =>{
    setNotifications([]);
    setCurrentPageNumber(1);
    setIsMoreResultsExists(true);
    getTotalNotificationsCount();
  },[]);

  React.useEffect(() => {
    if(!!totalNotifications && totalNotifications > 0){
      loadResults(currentPageNumber,currentPageSize,false,true);
    }else{
      setIsMoreResultsExists(false);
    }
  },[totalNotifications]);

  const loadResults = (
    pageNumber: number,
    pageSize: number,
    addResults: boolean,
    showLoadingIndicator: boolean,
  ) => {
    if (!loadingIsInProcess) {
      setLoadingIsInProcess(true);
      if (showLoadingIndicator) {
        props.showLoading();
      }

      userApi
        .getUserNotifications(props.loginUser?.Id??0,pageNumber,pageSize)
        .then((results: INotification[]) => {
          if (!!results && results.length > 0) {
            if (addResults) {
              setNotifications([...notifications, ...results]);
            } else {
              setNotifications([...results]);
            }

            setCurrentPageNumber(pageNumber);
            setIsMoreResultsExists(((currentPageNumber * currentPageSize) < totalNotifications));
          } else {
            setIsMoreResultsExists(false);
          }
        })
        .catch((err: any) => {
          props.hideLoading();
        })
        .finally(() => {
          props.hideLoading();
          setLoadingIsInProcess(false);
        });
    }
  };

  const markAsRead = (notificationId:number) => {
    props.showLoading();
    userApi.markNotificationAsRead(notificationId)
    .then(() => {
      const newNotifications = [...notifications];
      const processedNotifications = newNotifications.map(n => {n.IsViewed = n.Id == notificationId ? true : n.IsViewed; return n;})
      setNotifications([...processedNotifications]);
      props.hideLoading();
    })
    .catch(() => {
    })
    .finally(() => {
      props.hideLoading();
    });
  }

  const deleteNotification = (notificationId:number) => {
    props.showLoading();
    userApi.deleteNotification(notificationId)
    .then(() => {
      const newNotifications = [...notifications];
      const processedNotifications = newNotifications.filter(n => n.Id != notificationId);
      setNotifications([...processedNotifications]);
      props.hideLoading();
    })
    .catch(() => {
    }).finally(() => {props.hideLoading();});
  }

  const getTotalNotificationsCount = () =>{
    props.showLoading();
    userApi.getTotalNotifications(props.loginUser?.Id??0)
    .then(result => {
      setTotalNotifications(result);
    })
    .catch(() => {})
    .finally(() =>{
      props.hideLoading();
    });
  }

  return <NotificationsView 
          notifications={notifications} 
          loadMore={loadMore} 
          isMoreResultExists={isMoreResultsExists}
          markAsRead={markAsRead}
          deleteNotification={deleteNotification}
        />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loginUser:state.loginUser
  };
};

const mapDispatchToProps = {
  showLoading: showLoading,
  hideLoading: hideLoading,
};

const connectedNotificationsContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(NotificationsContainer);
export { connectedNotificationsContainer as NotificationsContainer };