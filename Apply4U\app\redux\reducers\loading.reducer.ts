import {loadingSpinnerActionConstants} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {ILoadingIndicator} from '../../interfaces';
import {initialState} from '../initial-state';

export const loadingReducer = (
  state: ILoadingIndicator = initialState.loading,
  action: IAction<ILoadingIndicator>,
) => {
  switch (action.type) {
    case loadingSpinnerActionConstants.LOADING_SPINNER_STATE:
      let newState: ILoadingIndicator = {...action.payload};
      return newState;
    default:
      return state;
  }
};
