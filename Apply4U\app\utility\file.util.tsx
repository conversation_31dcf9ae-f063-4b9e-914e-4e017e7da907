import {PermissionsAndroid} from 'react-native';
import {requestSinglePermission} from '.';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import {readFile} from 'react-native-fs';
import {EncodingType, FileType} from '../types';
import {storagePermission} from '../app.constant';
import {IChooseFileResult} from '../interfaces';

export const chooseFile = async (
  fileType: FileType,
): Promise<IChooseFileResult[]> => {
  let chooseFileResults: IChooseFileResult[] = [];
  try {
    const permissionResult = await requestSinglePermission(
      PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      storagePermission.title,
      storagePermission.message,
    );

    if (permissionResult === true) {
      let documentTypes: string[] = [];
      if (fileType === 'Resume' || fileType === 'CoverLetter') {
        documentTypes = [
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.pdf,
          DocumentPicker.types.plainText,
        ];
      } else if (fileType === 'Image') {
        documentTypes = [DocumentPicker.types.images];
      } else if (fileType === 'Document') {
        documentTypes = [
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.pdf,
          DocumentPicker.types.plainText,
          DocumentPicker.types.xlsx,
          DocumentPicker.types.xls,
          DocumentPicker.types.pptx,
          DocumentPicker.types.ppt,
          DocumentPicker.types.csv,
        ];
      } else if (fileType === 'Audio') {
        documentTypes = [DocumentPicker.types.audio];
      } else if (fileType === 'Video') {
        documentTypes = [DocumentPicker.types.video];
      } else if (fileType === 'Zip') {
        documentTypes = [DocumentPicker.types.zip];
      } else if (fileType === 'All') {
        documentTypes = [DocumentPicker.types.allFiles];
      }

      const res = await DocumentPicker.pick({
        type: documentTypes,
      });

      if (res && res.length > 0) {
        res.forEach(result => {
          chooseFileResults.push({
            path: result.uri,
            name: result.name ?? 'File',
            size: result.size,
            type: result.type,
          });
        });
      }
    }
  } catch (err) {
    if (DocumentPicker.isCancel(err)) {
    } else {
    }
  }

  return chooseFileResults;
};

export const selectFile = async (
  fileType: FileType,
): Promise<DocumentPickerResponse | null> => {
  let documentPickerResponse: DocumentPickerResponse | null = null;

  try {
    const permissionResult = await requestSinglePermission(
      PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
      storagePermission.title,
      storagePermission.message,
    );

    if (permissionResult === true) {
      let documentTypes: string[] = [];
      if (fileType === 'Resume' || fileType === 'CoverLetter') {
        documentTypes = [
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.pdf,
          DocumentPicker.types.plainText,
        ];
      } else if (fileType === 'Image') {
        documentTypes = [DocumentPicker.types.images];
      } else if (fileType === 'Document') {
        documentTypes = [
          DocumentPicker.types.doc,
          DocumentPicker.types.docx,
          DocumentPicker.types.pdf,
          DocumentPicker.types.plainText,
          DocumentPicker.types.xlsx,
          DocumentPicker.types.xls,
          DocumentPicker.types.pptx,
          DocumentPicker.types.ppt,
          DocumentPicker.types.csv,
        ];
      } else if (fileType === 'Audio') {
        documentTypes = [DocumentPicker.types.audio];
      } else if (fileType === 'Video') {
        documentTypes = [DocumentPicker.types.video];
      } else if (fileType === 'Zip') {
        documentTypes = [DocumentPicker.types.zip];
      } else if (fileType === 'All') {
        documentTypes = [DocumentPicker.types.allFiles];
      }

      const res = await DocumentPicker.pick({
        type: documentTypes,
      });

      if (res && res.length > 0) {
        documentPickerResponse = res[0];
      }
    }
  } catch (err) {
    if (DocumentPicker.isCancel(err)) {
    } else {
    }
  }

  return documentPickerResponse;
};

export const readFileFromStorage = (
  choosenFile: IChooseFileResult,
  onSuccess: (response: any) => void,
  onError: (error: any) => void,
  encodingOptions: EncodingType = 'utf8',
) => {
  readFile(choosenFile.path, encodingOptions)
    .then(resp => {
      onSuccess(resp);
    })
    .catch(err => {
      onError(err);
    });
};
