# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 26ms
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 62ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 50ms
  [gap of 19ms]
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 24ms
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 58ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 30ms
  [gap of 12ms]
generate_cxx_metadata completed in 58ms

