import {ImageStyle, TextStyle, ViewStyle} from 'react-native';

interface IPalette {
  primary: string;
  secondary: string;
  error: string;
  warning: string;
  info: string;
  success: string;
  white: string;
  gray: string;
  lightGray: string;
  blue: string;
  black: string;
}

interface ITypography {
  extraSmall: ViewStyle | TextStyle | ImageStyle;
  small: ViewStyle | TextStyle | ImageStyle;
  medium: ViewStyle | TextStyle | ImageStyle;
  large: ViewStyle | TextStyle | ImageStyle;
}

export interface ITypographyMain {
  bold: ITypography;
  normal: ITypography;
}

export interface IButtons {
  primary: ViewStyle | TextStyle | ImageStyle;
  secondary: ViewStyle | TextStyle | ImageStyle;
  info: ViewStyle | TextStyle | ImageStyle;
  warning: ViewStyle | TextStyle | ImageStyle;
  danger: ViewStyle | TextStyle | ImageStyle;
}

interface IBackgroundPalette {
  primary: string;
  secondary: string;
}

export interface ITheme {
  id: string;
  palette: IPalette;
  typography: ITypographyMain;
  spacing: (factor: number) => number;
  borderRadius: (factor: number) => number;
  zIndex:(index:number) => any;
  buttons: IButtons;
  backgroundPalette: IBackgroundPalette;
}
