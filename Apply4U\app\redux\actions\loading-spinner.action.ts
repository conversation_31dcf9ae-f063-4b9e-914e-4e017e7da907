import {IAction} from './action.interface';
import {loadingSpinnerActionConstants} from './action.constant';
import {ILoadingIndicator} from '../../interfaces';
import {
  isNullOrUndefined,
  isNullOrWhitespace,
  isNullOrZero,
} from '../../utility';
import {initialState} from '../initial-state';

export const showLoading = (
  loadingText?: string,
  autoHide?: boolean,
  autoHideMilliseconds?: number,
): IAction<ILoadingIndicator> => {
  let text: string = isNullOrUndefined(loadingText)
    ? (initialState.loading.text as string)
    : (loadingText as string);
  let isAutoHide: boolean = isNullOrUndefined(autoHide)
    ? (initialState.loading.autoHide as boolean)
    : (autoHide as boolean);
  let hideMilliseconds: number = isNullOrZero(autoHideMilliseconds)
    ? (initialState.loading.autoHideAfterMilliseconds as number)
    : (autoHideMilliseconds as number);

  let payload: ILoadingIndicator = {
    autoHide: isAutoHide,
    autoHideAfterMilliseconds: hideMilliseconds,
    show: true,
    text: text,
  };

  return {
    type: loadingSpinnerActionConstants.LOADING_SPINNER_STATE,
    payload,
  };
};

export const hideLoading = (): IAction<ILoadingIndicator> => {
  let payload: ILoadingIndicator = {...initialState.loading, show: false};
  return {
    type: loadingSpinnerActionConstants.LOADING_SPINNER_STATE,
    payload,
  };
};
