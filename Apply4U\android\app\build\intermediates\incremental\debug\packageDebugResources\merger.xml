<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res"><file name="rn_edit_text_material" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#0E1C5D</color></file><file path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Apply4U</string></file><file path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowBackground">@color/splash_background</item>
    </style></file><file name="launch_screen" path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\layout\launch_screen.xml" qualifiers="" type="layout"/><file path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="splash_background">#0E1C5D</color></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\generated\res\resValues\debug"><file path="D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\build\generated\res\resValues\debug\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer><integer name="react_native_inspector_proxy_port">8081</integer></file></source></dataSet><mergedItems/></merger>