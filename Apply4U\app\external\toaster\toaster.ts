import Toast from 'react-native-toast-message'; //https://www.npmjs.com/package/react-native-toast-message

export const showSuccessMessage = (message: string) => {
  Toast.show({
    type: 'success',
    text1: message,
    position: 'bottom',
  });
};

export const showInfoMessage = (message: string) => {
  Toast.show({
    type: 'info',
    text1: message,
    position: 'bottom',
  });
};

export const showErrorMessage = (message: string) => {
  Toast.show({
    type: 'error',
    text1: message,
    position: 'bottom',
  });
};
