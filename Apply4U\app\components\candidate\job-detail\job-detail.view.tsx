import React from 'react';
import {ScrollView, StyleSheet, View, Pressable} from 'react-native';
import {useCurrentTheme, ITheme} from '../../../theme';
import {Text, Button, MainLayout, Image} from '../../common';
import {useTranslation} from 'react-i18next';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {
  faAngleLeft,
  faShareAlt,
  faCommentDots,
  faHeart,
  faPoundSign,
  faMapMarker,
  faMapMarkerAlt,
  faBriefcase,
  faClock,
  faUniversity,
  faCalendarAlt,
} from '@fortawesome/free-solid-svg-icons';
import Icon from 'react-native-vector-icons/FontAwesome';
import {IJob, IJobSearchResponse} from '../../../interfaces';
import {formatedDateTime, hasValues, isNullOrWhitespace, timeAgo} from '../../../utility';
import {defaultCompanyLogo} from '../../../assets';
import RenderHtml from 'react-native-render-html';
import {useWindowDimensions} from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

interface IProps {
  job: IJobSearchResponse | undefined;
  canGoBack: boolean;
  isAlreadyApplied: boolean;
  isFav:boolean;
  goBack: () => void;
  applyNow: () => void;
  addToFav: () => void;
}
const JobDetailView = (props: IProps) => {
  const {t} = useTranslation(['JobDetail']);
  const styles = useCurrentTheme(createStyles);

  const {width} = useWindowDimensions();

  return (
    <MainLayout>
      {!!props.job && (
        <>
          <View>
            <View style={styles.header2}>
              <View style={styles.headerIcon}>
                {!!props.canGoBack && (
                  <Pressable onPress={props.goBack}>
                    <FontAwesomeIcon
                      icon={faAngleLeft}
                      style={styles.backIcon}
                      size={32}
                    />
                  </Pressable>
                )}
              </View>
              <View style={styles.headerTextContainer}>
                <Text text='Job Details' styles={styles.headerText} />
              </View>

              <View style={styles.headerIcon}></View>
            </View>
            <View style={styles.logoContainer}><Image
                style={styles.CompLogo}
                source={
                  !isNullOrWhitespace(props.job?.LogoUrl)
                      ? { uri: props.job.LogoUrl }
                      : defaultCompanyLogo
              }
                defaultSource={defaultCompanyLogo}
              />
            </View>
          </View>

          <ScrollView>
            <View style={styles.InfoWrapper}>
              <Text styles={styles.companyRank} text={`${props.job?.Title}`} />
              <Text
                styles={styles.postedAgoText}
                text={`Posted ${timeAgo(props.job.DisplayStartDate)}${!!props.job.CompanyName && props.job.CompanyName.trim() != '' ? ' by '+props.job.CompanyName : ''}`}
              />
            </View>

            <View style={{flex:1,height:100,paddingLeft:15,flexDirection:'row'}}>
              <View style={{flex:5,flexDirection:'column'}}>
              {!!props.job.LocationText && <View style={{flex:1,justifyContent:'center'}}>
                <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faMapMarkerAlt}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.job.LocationText}
                    </Text>
                </View>}
                {!!props.job.JobType && <View style={{flex:1,justifyContent:'center'}}>
                <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faClock}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.job.JobType}
                    </Text>
                </View>}
                {!!props.job.Salary && <View style={{flex:1,justifyContent:'center'}}>
                <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faPoundSign}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.job.Salary}
                    </Text>
                </View>}
                {!!props.job.JobCategory && <View style={{flex:1,justifyContent:'center'}}>
                <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faUniversity}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {props.job.JobCategory}
                    </Text>
                </View>}
                {!!props.job.DisplayEndDate && <View style={{flex:1,justifyContent:'center'}}>
                <Text styles={styles.jobOptionsText} numberOfLines={1}>
                        <View style={styles.iconStyle}>
                            <FontAwesomeIcon
                                icon={faCalendarAlt}
                                style={styles.iconStyle}
                                size={12}
                            />
                        </View>
                        {`Expires ${timeAgo(props.job.DisplayEndDate)}`}
                    </Text>
                </View>}
              </View>
              <View style={{flex:1,justifyContent:'flex-end'}}>
                <TouchableOpacity onPress={props.addToFav}>
                {!!props.isFav && <FontAwesomeIcon
                                        icon={faHeart}
                                        size={30}
                                        color={styles.primaryColor}
                                    />}
                                    {!props.isFav && <Icon
                                        name='heart-o'
                                        size={30}
                                        color={styles.primaryColor}
                                    />}
                   </TouchableOpacity>
              </View>
            </View>

            <View style={styles.detailContainer}>
              <Text styles={styles.detailHeading} text={'Job Description'} />
              <RenderHtml
                contentWidth={width}
                enableCSSInlineProcessing={true}
                source={{html: props.job?.Description ?? ''}}
              />
            </View>
          </ScrollView>
                  <Button
                    styles={styles.btnSpace}
                    pressed={() => {
                      if (props.isAlreadyApplied === false) {
                        props.applyNow();
                      }
                    }}>
                    <View style={styles.buttonIcon}>
                      <Text text={props.isAlreadyApplied === false ? 'Apply Now' : 'Already Applied'} />
                    </View>
                  </Button>
        </>
      )}
    </MainLayout>
  );
};

export {JobDetailView};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    viewPort: {
      height: '100%',
      width: '100%',
    },
    container: {
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      //backgroundColor: '#edf1fc',
    },
    header2: {
      height: 90,
      width: '100%',
      flexWrap: 'wrap',
      flexDirection: 'row',
      backgroundColor: theme.palette.primary,
      justifyContent:'center'
    },
    headerIcon: {
      height:50,
      flex:1,
      justifyContent:'flex-start'
    },
    headerTextContainer: {
      height:50,
      flex:3,
      justifyContent:'flex-start'
    },
    headerText: {
      ...theme.typography.bold.large,
      alignSelf:'center'
    },
    InfoWrapper: {
      padding: 10,
      width: '100%',
      flexDirection: 'column',
      marginTop:60,
    },
    companyText: {
      fontSize: 14,
      color: '#333333',
      fontWeight: 'bold',
      textAlign: 'center',
    },
    postedAgoText: {
      ...theme.typography.bold.small,
      color: '#4f40d1',
      textAlign: 'center',
    },
    companyRank: {
      ...theme.typography.bold.large,
      fontSize: 18,
      color: theme.palette.gray,
      textAlign: 'center',
    },
    detailContainer: {
      width: '100%',
      padding: 25,
      paddingBottom:60,
    },
    detailHeading: {
      fontSize: 18,
      color: theme.palette.gray,
      fontWeight: 'bold',
      marginBottom: 5,
    },
    detailText: {
      fontSize: 13,
      color: '#333333',
      lineHeight: 18,
    },

    buttonIcon: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      alignSelf: 'center',
    },
    backIcon: {
      color: theme.palette.white,
      alignSelf:'center',
      marginRight:theme.spacing(5)
    },
    faColor: {
      fontSize: 20,
      color: '#fff',
      marginRight: 5,
    },
    roundedWrapper: {
      margin: 10,
      padding: 10,
      borderRadius: 6,
      flexDirection: 'row',
      backgroundColor: '#edf1fc',
    },
    salary: {
      width: '33%',
      flexDirection: 'column',
    },
    applicants: {
      width: '33%',
      flexDirection: 'column',
    },
    expiry: {
      width: '33%',
      flexDirection: 'column',
    },
    buttonWrapper: {
      margin: 10,
      padding: 10,
      flexDirection: 'row',
      backgroundColor:'yellow',
      justifyContent:'center'
    },
    chat: {
      width: '20%',
      flexDirection: 'column',
    },
    apply: {
      width: '56%',
      marginRight: '2%',
      marginLeft: '2%',
      flexDirection: 'column',
      justifyContent: 'center',
      backgroundColor:'green',
    },
    like: {
      width: '20%',
      flexDirection: 'column',
    },
    btnSpace: {
      borderRadius: 6,
      backgroundColor:theme.palette.secondary,
      width:'70%',
      alignSelf:'center',
      marginBottom:theme.spacing(5),
      position:'absolute',
      bottom:1,
    },
    btnLike: {
      borderWidth: 2,
      borderRadius: 6,
      backgroundColor: '#fff',
      borderColor: '#e5ebf0',
    },
    buttonIconColor: {
      fontSize: 20,
      color: '#88add2',
      marginRight: 5,
      alignSelf: 'center',
    },
    CompLogo: {
      height: '95%',
      width: '95%',
      alignSelf: 'center',
      resizeMode: 'contain',
    },
    logoContainer: {
      height: 100,
      width: 200,
      alignSelf: 'center',
      position:'absolute',
      top:40,
      backgroundColor:theme.palette.white,
      ...theme.zIndex(1000),
    },
    alreadyAppliedText: {
      alignSelf: 'center',
    },
    jobOptionsText: {
      ...theme.typography.bold.extraSmall,
      paddingLeft: theme.spacing(5),
      color: theme.palette.gray,
  },
  iconStyle: {
    color: '#a8a7a7',
    marginRight:theme.spacing(10),
},
  });

  const colors = { defaultIconColor: '#a2b3e0', green: 'green',primaryColor:theme.palette.primary }

  return {...styles,...colors};
};
