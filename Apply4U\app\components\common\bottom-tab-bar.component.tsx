import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ITabBarOptions} from '../../interfaces';
import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {Text} from '.';
import {ITheme, useCurrentTheme} from '../../theme';
import {navigate} from '../../utility';
import {useRoute} from '@react-navigation/core';

interface IProps {
  tabBarOptions: ITabBarOptions[];
}
export const BottomTabBarComponent = (props: IProps) => {
  const {styles, colors} = useCurrentTheme(createStyles);
  const route = useRoute();
  const [currentScreen, setCurrentScreen] = React.useState<string>(route?.name);

  React.useEffect(() => {
    setCurrentScreen(route?.name);
  }, [route]);

  return (
    <View style={styles.bottomView}>
      <View style={styles.container}>
        {!!props.tabBarOptions &&
          props.tabBarOptions.length > 0 &&
          props.tabBarOptions.map((item, index) => {
            const isActive = item.screen.screen === currentScreen;
            return (
              <View style={[styles.box1]} key={`tabBarOption${index}`}>
                <Pressable
                  onPress={() => {
                    navigate(item.screen);
                  }}>
                  <View>
                    <View style={styles.box1Inside}>
                      <FontAwesomeIcon
                        icon={item.icon}
                        color={isActive ? colors.secondary : colors.white}
                        size={30}
                      />
                    </View>
                    <View style={styles.box2Inside}>
                      <Text
                        styles={
                          isActive ? styles.activeTextStyle : styles.textStyle
                        }
                        text={item.name}
                      />
                    </View>
                  </View>
                </Pressable>
                {!!item.showNotificationsDot && <View style={{backgroundColor:colors.secondary,width:16,height:16,position:'absolute',top:2,right:30,borderRadius:8}}></View>}
              </View>
            );
          })}
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      height: 60,
      flexDirection: 'row',
      marginBottom: theme.spacing(0),
      width: '100%',
    },
    box1: {
      flex: 1,
      height: 60,
      width: 50,
      backgroundColor: theme.palette.primary,
      justifyContent: 'space-evenly',
      alignItems: 'center',
    },

    box1Inside: {
      flex: 1,
      flexDirection: 'column',
      paddingTop: theme.spacing(3),
      justifyContent: 'space-evenly',
      alignItems: 'center',
    },
    box2Inside: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'space-evenly',
      alignItems: 'center',
    },
    textStyle: {
      ...theme.typography.bold.extraSmall,
      fontSize: 10,
    },
    activeTextStyle: {
      ...theme.typography.bold.extraSmall,
      fontSize: 10,
      color: theme.palette.secondary,
    },
    bottomView: {
      width: '100%',
      height: 60,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  const colors = {
    primary: theme.palette.primary,
    secondary: theme.palette.secondary,
    white: theme.palette.white,
  };
  return {styles, colors};
};
