import {store} from '../redux/store';
import axios, {AxiosResponse} from 'axios';
import {handleError} from '../http-error-handler';
import {ApiBaseUrl, TokenApiUrl} from '../app.constant';

const axiosInstance = axios.create({
  baseURL: ApiBaseUrl,
});

const getCancelTokenSource = () => axios.CancelToken.source();

axiosInstance.interceptors.request.use(
  request => {
    const token = store.getState().token;
    if (token && token.access_token) {
      request.headers['Authorization'] = `Bearer ${token.access_token}`;
    }

    return request;
  },
  error => {
    return Promise.reject(error);
  },
);

const axiosInstanceForToken = axios.create({
  baseURL: TokenApiUrl,
});

export {axiosInstance as http,axiosInstanceForToken as tokenHttp, getCancelTokenSource as getCancelTokenSource};
