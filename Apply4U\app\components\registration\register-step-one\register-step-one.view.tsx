import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  ImageBackground,
  SafeAreaView,
} from 'react-native';

import {transparentLogo, background_2} from '../../../assets';

import {useTranslation} from 'react-i18next';
import {ITheme, useCurrentTheme} from '../../../theme';
import {Button, InputText, Text} from '../../common';
import {InputType} from '../../../enum';

const RegisterStepOneView = () => {
  const styles = useCurrentTheme(createStyles);
  const {t} = useTranslation(['Registration']);

  return (
    <ImageBackground source={background_2} style={styles.container}>
      <SafeAreaView>
        <ScrollView contentInsetAdjustmentBehavior="automatic">
          <View style={styles.logoContainer}>
            <Image
              source={transparentLogo}
              style={styles.appLogo}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formBox}>
            <KeyboardAvoidingView enabled>
              <View style={styles.inputView}>
                <InputText
                  name={'FirstName'}
                  inputType={InputType.Text}
                  placeholder={t('Placeholders.First_Name')}
                />
              </View>

              <View style={styles.inputView}>
                <InputText
                  name={'LastName'}
                  inputType={InputType.Text}
                  placeholder={t('Placeholders.Last_Name')}
                />
              </View>

              <View style={styles.inputView}>
                <InputText
                  name={'Email'}
                  inputType={InputType.Text}
                  placeholder={t('Placeholders.Enter_Email')}
                />
              </View>

              <View style={styles.inputView}>
                <InputText
                  name={'Password'}
                  inputType={InputType.Password}
                  placeholder={t('Placeholders.Enter_Password')}
                />
              </View>

              <View style={styles.inputView}>
                <InputText
                  name={'ReEnterPassword'}
                  inputType={InputType.Password}
                  placeholder={t('Placeholders.Re_Enter_Password')}
                />
              </View>

              <Button
                pressed={() => {
                  Keyboard.dismiss();
                }}
                loadingText={t('Messages.Creating_Account')}
                isLoading={true}
                text={t('Buttons.Continue')}
              />
            </KeyboardAvoidingView>
          </View>

          <Text
            styles={styles.memberTextStyle}
            text={t('Labels.Already_Member_Login')}
          />
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'column',
      flexGrow: 1,
      justifyContent: 'space-between',
    },
    logoContainer: {
      alignItems: 'center',
      marginTop: theme.spacing(20),
    },
    appLogo: {
      height: 100,
      width: '50%',
      marginTop: theme.spacing(30),
      marginRight: theme.spacing(20),
      marginBottom: theme.spacing(20),
      marginLeft: theme.spacing(20),
      resizeMode: 'contain',
    },
    formBox: {
      flex: 1,
      margin: theme.spacing(10),
      padding: theme.spacing(7.5),
      height: '100%',
      borderTopLeftRadius: theme.borderRadius(5),
      borderTopRightRadius: theme.borderRadius(5),
      borderBottomLeftRadius: theme.borderRadius(5),
      borderBottomRightRadius: theme.borderRadius(5),
      backgroundColor: theme.backgroundPalette.primary,
    },
    inputView: {
      height: 40,
      marginBottom: theme.spacing(7.5),
      flexDirection: 'row',
    },
    memberTextStyle: {
      textAlign: 'center',
      alignSelf: 'center',
      ...theme.typography.bold.small,
    },
  });

  return {...styles};
};

export {RegisterStepOneView};
