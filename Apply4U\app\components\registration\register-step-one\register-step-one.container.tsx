import React from 'react';
import {RegisterStepOneView} from './register-step-one.view';
import {} from '../../../utility';
import {connect} from 'react-redux';
import {setTokenAction} from '../../../redux/actions';
import {IApplicationState} from '../../../redux';

const RegisterStepOneContainer = (props: any) => {
  return <RegisterStepOneView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const mapDispatchToProps = {
  setToken: setTokenAction,
};

const connectedLoginContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(RegisterStepOneContainer);
export {connectedLoginContainer as RegisterStepOneContainer};
