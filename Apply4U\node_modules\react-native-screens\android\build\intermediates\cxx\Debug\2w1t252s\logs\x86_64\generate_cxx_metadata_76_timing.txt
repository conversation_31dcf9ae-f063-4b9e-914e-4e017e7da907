# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 35ms
  [gap of 10ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 91ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 31ms
  write-metadata-json-to-file 19ms
generate_cxx_metadata completed in 72ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 35ms]
  create-invalidation-state 62ms
  [gap of 19ms]
generate_cxx_metadata completed in 116ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 25ms
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 62ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 26ms
  [gap of 11ms]
generate_cxx_metadata completed in 52ms

