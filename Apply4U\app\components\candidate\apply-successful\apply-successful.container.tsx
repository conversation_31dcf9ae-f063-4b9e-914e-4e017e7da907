import React from 'react';
import {screens} from '../../../app.constant';
import {IApplySuccessfulParam, ISearchParam} from '../../../interfaces';
import {getParams, navigate} from '../../../utility';
import {ApplySuccessfulView} from './apply-successful.view';
import {connect} from 'react-redux';
import {IApplicationState} from '../../../redux';
import {setSearchParamAction} from '../../../redux/actions';

interface IProps {
  searchParam: ISearchParam | null;
  setSearchParam: (payload: ISearchParam | null) => any;
}

const ApplySuccessfulContainer = (props: IProps) => {
  const backToHome = () => {
    navigate(screens.Dashboard);
  };

  const jobTitle = getParams<IApplySuccessfulParam>();

  const goToSimilarJobs = () => {
    props.setSearchParam({keywords: jobTitle?.jobTitle});
    navigate(screens.JobSearchResults);
  };

  return (
    <ApplySuccessfulView
      backToHome={backToHome}
      goToSimilarJobs={goToSimilarJobs}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    searchParam: state.searchParam,
  };
};

const mapDispatchToProps = {
  setSearchParam: setSearchParamAction,
};

const connectedApplySuccessfulContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(ApplySuccessfulContainer);

export {connectedApplySuccessfulContainer as ApplySuccessfulContainer};
