import React from 'react';
import {Alert, Pressable, StyleSheet, View} from 'react-native';
import {IUserPackage} from '../../interfaces';
import {ITheme, useCurrentTheme} from '../../theme';
import {Text} from '../common';

interface IProps {
  userPackage?: IUserPackage;
  onUpgradeClick?: () => void;
}
export const SubscriptionBanner = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  return (
    <View style={styles.bottomBanner1}>
      <View style={styles.bannerBox1}>
        <Text styles={styles.BannerText1} text={'Your Current Subscription'} />
        <Text
          styles={styles.label}
          text={
            props.userPackage?.PackageName
              ? props.userPackage.PackageName
              : 'Free'
          }
        />
        <Text
          styles={styles.BannerText2}
          text={'Upgrade Your Package to Access More Perks'}
        />
      </View>

      <View style={styles.bannerBox2}>
        <Pressable
          style={styles.buttonStyle}
          onPress={() => {
            if (props.onUpgradeClick) {
              props.onUpgradeClick();
            }
          }}>
          <Text styles={styles.label2} text={'Upgrade'} />
        </Pressable>
      </View>
    </View>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    bottomBanner1: {
      flex: 1,
      margin: 10,
      height: 110,
      padding: 10,
      borderRadius: theme.borderRadius(10),
      flexWrap: 'wrap',
      flexDirection: 'row',
      backgroundColor: theme.palette.gray,
    },
    bannerBox1: {
      width: '70%',
    },
    bannerBox2: {
      width: '30%',
      paddingHorizontal: 5,
      alignSelf:'center'
    },
    BannerText1: {
      fontSize: 18,
      color: '#fff',
      fontWeight: 'bold',
    },
    BannerText2: {
      fontSize: 13,
      color: '#fff',
      marginTop: 6,
      letterSpacing: 0.5,
    },
    buttonStyle: {
      height: 30,
      borderRadius: 8,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.palette.secondary,
      marginBottom:0,
      paddingTop:0,
      paddingBottom:0,
    },
    label: {
      fontSize: 13,
      color: '#fff',
      letterSpacing: 0.5,
    },
    label2: {
      fontSize: 13,
      color: theme.palette.white,
      letterSpacing: 0.5,
    },
  });
  return styles;
};
