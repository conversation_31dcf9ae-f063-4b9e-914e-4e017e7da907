import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {ITheme, useCurrentTheme} from '../../theme';
import {Text} from './text.component';

interface IProps {
  styles?: any | null;
  text?: string | null;
  pressed?: (event: any) => any;
  children?: any | null;
  isLoading?: boolean | null;
  loadingText?: string | null;
  textStyles?: any | null;
}

const Button = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);

  return (
    <TouchableOpacity
      disabled={!!props.isLoading && props.isLoading === true}
      style={
        props.styles
          ? {...styles.buttonPrimary, ...props.styles}
          : styles.buttonPrimary
      }
      onPress={(e: any) => {
        if (props.pressed) {
          props.pressed(e);
        }
      }}>
      {!!props.children
        ? props.children
        : props.text && (
            <Text
              text={
                props.isLoading === true && props.loadingText
                  ? props.loadingText
                  : props.text
              }
              styles={{...styles.buttonTextDefault, ...props.textStyles}}
            />
          )}
    </TouchableOpacity>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    buttonPrimary: {...theme.buttons.primary},
    buttonTextDefault: {
      ...theme.typography.bold.small,
    },
  });

  return {...styles};
};

export {Button};
