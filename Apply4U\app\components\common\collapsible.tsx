import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from '.';
import { ITheme, useCurrentTheme } from '../../theme';
import {faChevronDown, faChevronUp} from '@fortawesome/free-solid-svg-icons';

interface IProps{
    children:any;
    title:string;
    isCollapsed:boolean;
    onCollapseChanged:() =>void;
}
export const Collapsible = (props:IProps) => {
    const handleCollapseChanged = () =>{
        props.onCollapseChanged();
    }

    const styles = useCurrentTheme(createStyles);
    return <View style={styles.container}>
        <TouchableOpacity onPress={handleCollapseChanged} style={styles.header}>
            <View style={styles.headTextContainer}>
                <Text styles={styles.headText} text={props.title} />
            </View>
            <View style={styles.iconContainer}>
                <FontAwesomeIcon style={styles.iconStyle} icon={props.isCollapsed ? faChevronDown : faChevronUp} size={15} color={styles.iconColor} />
            </View>
        </TouchableOpacity>
        {!props.isCollapsed && <View style={styles.contentContainer}>
            {props.children}
        </View>
        }
    </View>
};

const createStyles = (theme:ITheme) =>{
    const styles = StyleSheet.create({
        container:{
            
        },
        header:{
            borderTopWidth:2,
            borderTopColor:theme.palette.lightGray,
            //borderBottomWidth:2,
            //borderBottomColor:theme.palette.lightGray,
            width:'95%',
            height:45,
            alignSelf:'center',
            paddingLeft:theme.spacing(5),
            paddingRight:theme.spacing(5),
            flexDirection:'row'
        },
        contentContainer:{
            width:'95%',
            alignSelf:'center',
            justifyContent:'center',
            paddingLeft:theme.spacing(5),
            paddingRight:theme.spacing(5),
            borderBottomWidth:1,
            borderBottomColor:theme.palette.lightGray,
            paddingBottom:theme.spacing(5),
        },
        headTextContainer:{
            flex:3,
            justifyContent:'center'
        },
        iconContainer:{
            flex:1,
            justifyContent:'center'
        },
        headText:{
            ...theme.typography.bold.small,
            color:theme.palette.primary,
            alignSelf:'flex-start',
        },
        iconStyle:{
            alignSelf:'flex-end',
        },
    });

    const colors = {iconColor:theme.palette.primary};

    return {...styles,...colors};
}