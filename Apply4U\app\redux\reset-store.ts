import {store} from './store';
import {
  setFavJobsAction,
  setIsFirstVisitAction,
  setLoginUserDetailAction,
  setRecentSearchesAction,
  setTokenAction,
} from './actions';
import {initialState} from './initial-state';
import {setHiddenJobsAction} from './actions/hidden-jobs.action';

export const resetStore = () => {
  store.dispatch(setIsFirstVisitAction(initialState.isFirstVisit));
  store.dispatch(setLoginUserDetailAction(null));
  store.dispatch(setTokenAction(null));
  store.dispatch(setHiddenJobsAction([]));
  store.dispatch(setFavJobsAction([]));
  store.dispatch(setRecentSearchesAction(initialState.recentSearches));
};
