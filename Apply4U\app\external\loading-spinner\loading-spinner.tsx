import React from 'react';
import {connect} from 'react-redux';
import Spinner from 'react-native-loading-spinner-overlay'; //https://www.npmjs.com/package/react-native-loading-spinner-overlay
import {ITheme, useCurrentTheme} from '../../theme';
import {StyleSheet} from 'react-native';
import {IApplicationState} from '../../redux';
import {ILoadingIndicator} from '../../interfaces';
import {isNullOrZero} from '../../utility';
import {hideLoading} from '../../redux/actions';

interface IProps {
  loadingState: ILoadingIndicator;
  hideLoading: () => void;
}

const LoadingSpinner = (props: IProps) => {
  const {styles, loadingSpinnerColor} = useCurrentTheme(createStyles);

  React.useEffect(() => {
    if (
      props.loadingState.autoHide === true &&
      !isNullOrZero(props.loadingState.autoHideAfterMilliseconds)
    ) {
      setInterval(() => {
        props.hideLoading();
      }, props.loadingState.autoHideAfterMilliseconds);
    }
  }, [props.loadingState.autoHide]);

  return (
    <Spinner
      visible={props.loadingState.show}
      textContent={props.loadingState.text}
      textStyle={styles.spinnerTextStyle}
      color={loadingSpinnerColor}
    />
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    spinnerTextStyle: {
      ...theme.typography.bold.medium,
    },
    overlayColor: {
      color: theme.palette.primary,
    },
  });

  const loadingSpinnerColor: string = theme.palette.primary;

  return {styles, loadingSpinnerColor};
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    loadingState: state.loading,
  };
};

const mapDispatchToProps = {
  hideLoading,
};

const connectedLoadingIndicator = connect(
  mapStateToProps,
  mapDispatchToProps,
)(LoadingSpinner);
export {connectedLoadingIndicator as LoadingSpinner};
