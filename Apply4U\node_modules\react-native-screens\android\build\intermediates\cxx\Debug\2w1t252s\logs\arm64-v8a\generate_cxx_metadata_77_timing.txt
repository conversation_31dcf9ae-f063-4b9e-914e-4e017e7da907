# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 63ms
  [gap of 11ms]
  write-metadata-json-to-file 41ms
generate_cxx_metadata completed in 145ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 10ms
  [gap of 20ms]
generate_cxx_metadata completed in 33ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 42ms
  [gap of 13ms]
  write-metadata-json-to-file 43ms
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 21ms
  [gap of 10ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 64ms

