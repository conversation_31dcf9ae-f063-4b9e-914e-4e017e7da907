import React from 'react';
import { NewRegisterStepThreeView } from './new-register-step-three.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';

const NewRegisterStepThreeContainer = () => {
  return <NewRegisterStepThreeView />;
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    token: state.token,
  };
};

const connectedNewRegisterContainer = connect(
  mapStateToProps,
  null,
)(NewRegisterStepThreeContainer);

export { connectedNewRegisterContainer as NewRegisterStepThreeContainer };
