import {SearchParametersConstants} from '../actions/action.constant';
import {IAction} from '../actions/action.interface';
import {ISearchParam} from '../../interfaces';
import {initialState} from '../initial-state';

export const searchReducer = (
  state = initialState.searchParam,
  action: IAction<ISearchParam>,
) => {
  switch (action.type) {
    case SearchParametersConstants.SET_SEARCH_PARAMETERS:
      let newState: ISearchParam = {...action.payload};
      return newState;
    default:
      return state;
  }
};
