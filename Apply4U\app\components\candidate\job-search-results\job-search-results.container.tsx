import React from 'react';
import { JobSearchResultsView } from './job-search-results.view';
import { connect } from 'react-redux';
import { IApplicationState } from '../../../redux';
import {
  addRecentSearchAction,
  addToFavJobsAction,
  hideJobAction,
  hideLoading,
  removeFromFavJobsAction,
  setSearchParamAction,
  showLoading,
} from '../../../redux/actions';
import {
  IAttbJobAlert,
  IJobDetailParam,
  IJobGateJobAlert,
  IJobSearchResponse,
  IJobSearchResult,
  ILoadingIndicator,
  ISearchParam,
  IUserDetail,
} from '../../../interfaces';
import { IAction } from '../../../redux/actions/action.interface';
import { jobAlertApi, searchApi } from '../../../http';
import {
  defaultPageNumberJobSearch,
  defaultPageSizeSearchResults,
  screens,
} from '../../../app.constant';
import { applyNow, easyApplyWithDefaultResume, navigate } from '../../../utility';
import { showErrorMessage, showSuccessMessage } from '../../../external/toaster';

interface IProps {
  searchParam: ISearchParam | null;
  loginUser: IUserDetail | null;
  hiddenJobs: number[];
  favJobs: number[];
  setSearchParam: (payload: ISearchParam | null) => IAction<ISearchParam>;
  showLoading: (
    loadingText?: string,
    autoHide?: boolean,
    autoHideMilliseconds?: number,
  ) => IAction<ILoadingIndicator>;
  hideLoading: () => IAction<ILoadingIndicator>;
  hideJob:(payload:number) => IAction<number>;
  addToFav: (payload: number) => IAction<number>;
  removeFromFav: (payload: number) => IAction<number>;
  addRecentSearch:(payload:string) => IAction<string>;
}
const JobSearchResultsContainer = (props: IProps) => {
  const [currentPageNumber, setCurrentPageNumber] = React.useState<number>(
    defaultPageNumberJobSearch,
  );

  const [currentPageSize, setCurrentPageSize] = React.useState<number>(
    defaultPageSizeSearchResults,
  );

  const [searchResults, setSearchResults] = React.useState<IJobSearchResponse[]>([]);
  const [sortBy, setSortBy] = React.useState(props?.searchParam?.sortBy);
  const [loadingIsInProcess, setLoadingIsInProcess] =
    React.useState<boolean>(false);
  const [isMoreResultsExists, setIsMoreResultsExists] =
    React.useState<boolean>(true);
  const [totalResults,setTotalResults] = React.useState<number>(0);
    const [showFilters,setShowFilters] = React.useState<boolean>(false);

  const loadResults = (
    pageNumber: number,
    pageSize: number,
    addResults: boolean,
    showLoadingIndicator: boolean,
  ) => {
    if (!loadingIsInProcess) {
      setLoadingIsInProcess(true);
      if (showLoadingIndicator) {
        props.showLoading();
      }

      searchApi
        .getJobResults(
          props.searchParam,
          props.hiddenJobs,
          props.loginUser?.Id,
          pageNumber,
          pageSize,
        )
        .then((results: IJobSearchResult) => {
          if (!!results && !!results.Response && results.Response.length > 0) {
            if (addResults) {
              markFavAndSetResults([...searchResults, ...results.Response]);
            } else {
              markFavAndSetResults([...results.Response]);

              if(!!props.searchParam?.keywords && props.searchParam.keywords.trim() != ''){
                props.addRecentSearch(props.searchParam.keywords);
              }
            }

            setCurrentPageNumber(pageNumber);
            setIsMoreResultsExists(((currentPageNumber * currentPageSize) < results.TotalRecords));
            setTotalResults(results.TotalRecords);
          } else {
            setIsMoreResultsExists(false);
          }
        })
        .catch((err: any) => {
          props.hideLoading();
        })
        .finally(() => {
          props.hideLoading();
          setLoadingIsInProcess(false);
        });
    }
  };

  const handleSortChange = () => {
    if (!loadingIsInProcess) {
      let sortedBy = props.searchParam?.sortBy;

      if (!!sortedBy && sortedBy === 'Relevancy') {
        sortedBy = 'Date';
      } else {
        sortedBy = 'Relevancy';
      }

      setSortBy(sortedBy);
      props.setSearchParam({ ...props.searchParam, sortBy: sortedBy });
    }
  };

  const favJobsAddOrRemove = (jobId: number, isFav: boolean) => {
    if (isFav === true) {
      props.removeFromFav(jobId);
    } else {
      props.addToFav(jobId);
    }
  };

  const handleGoToJobDetail = (jobId: number) => {
    navigate<IJobDetailParam>(screens.JobDetail, { jobId });
  };

  const handleHideJob = (jobId: number) => {
    if (jobId > 0) {
      props.hideJob(jobId);
    }
  };

  const handleLoadMore = () => {
    const pageNumber = currentPageNumber + 1;
    loadResults(pageNumber, currentPageSize, true, true);
  };

  const handleEasyApply = (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => {
    if (jobId > 0 && jobSourceId > 0 && !!jobTitle) {
      applyNow(jobId, jobSourceId, jobTitle, applicationUrl, isEasyApply ? internalEasyApply : undefined);
    } else {
      applyNow(undefined, 0, '', applicationUrl, isEasyApply ? internalEasyApply : undefined);
    }
  };

  const internalEasyApply = (jobId:number) => {
      props.showLoading();
    easyApplyWithDefaultResume(props?.loginUser?.Id ?? 0,jobId,(errorMessage:string) =>{
        props.hideLoading();
        showErrorMessage(errorMessage);
    },(successMessage:string) => {
        setSearchResults(oldResults => {
            return oldResults.map(m => {
                if(m.Id == jobId){
                    m.IsApplied = true;
                }

                return m;
            });
        });
        props.hideLoading();
        showSuccessMessage(successMessage);
    });
  }

  const markFavAndSetResults = (resultsToMark:IJobSearchResponse[]) => {
    if(!!resultsToMark && resultsToMark.length > 0){
      if (!!props.favJobs && props.favJobs.length > 0){
        const resultsWithFavMark = resultsToMark.map(m => {
          m.IsFav = props.favJobs.indexOf(m.Id) >= 0;
          return m;
        });
  
        setSearchResults([...resultsWithFavMark]);
      }else{
        const resultsWithNoFavMark = resultsToMark.map(m => {
          m.IsFav = false;
          return m;
        });
  
        setSearchResults([...resultsWithNoFavMark]);
      }

    }else{
      setSearchResults([...resultsToMark]);
    }
  }

  const [keywords,setKeywords] = React.useState<string>(props.searchParam?.keywords ?? '');
  const onSelectKeywords = (searchKeywords:string) => {
    let searchParam: ISearchParam = {...props.searchParam,applyFilters:false,locationId:undefined,locationText:undefined,selectedLocation:undefined};
    searchParam['keywords'] = searchKeywords;
    props.setSearchParam({...searchParam});
    setKeywords(searchKeywords)
  };

  React.useEffect(() => {
      setSearchResults([]);
      setTotalResults(0);
      loadResults(
        defaultPageNumberJobSearch,
        defaultPageSizeSearchResults,
        false,
        true,
      );
      setShowFilters(false);
      if(!!props.searchParam?.keywords){
        setKeywords(props.searchParam?.keywords ?? '');
      }else{
        setKeywords('');
      }
  }, [props.searchParam]);

  React.useEffect(() => {
    markFavAndSetResults(searchResults);
  }, [props.favJobs]);

  React.useEffect(() => {
    if(!!props.hiddenJobs && props.hiddenJobs.length > 0 && !!searchResults && searchResults.length > 0){
      const filteredSearchResults = searchResults.filter(m => props.hiddenJobs.indexOf(m.Id) < 0);
        setSearchResults([...filteredSearchResults]);
    }
  },[props.hiddenJobs]);

  const showHideFilters = () => {
    setShowFilters(!showFilters);
  }

  const handleKeywordsChange = (words:string) => {
    setKeywords(words);
  }

  const hanldeEmailMeMatchingJobs = () => {
    if((!props.searchParam?.keywords || props.searchParam?.keywords.trim() == '') && (!props.searchParam?.locationText || props.searchParam?.locationText.trim() == '')){
      showErrorMessage('No Keywords or location entered to set alerts');
      return;
    }

    props.showLoading();
    jobAlertApi.searchJobAlert(props.searchParam.keywords,props.searchParam.locationText,props.loginUser?.Id ?? 0)
    .then((alert:any[]) => {
      props.hideLoading();
      if(!!alert && alert.length > 0){
        showErrorMessage('Email Alert is already set for current search');
      }else{
        const attbJobAlert:IAttbJobAlert = {
          DesiredKewords:props.searchParam?.keywords,
          DesiredLocation:props.searchParam?.locationText,
          UserId:props.loginUser?.Id,
          FrequencyId:1
        };

        props.showLoading();
        jobAlertApi.saveAttbJobAlert(attbJobAlert).then(() => {
          showSuccessMessage('Email Alert Set Successfully');
        })
        .catch(() => {})
        .finally(() => {
          props.hideLoading();
        });

        const jobGateAlert:IJobGateJobAlert = {
          UserId:props.loginUser?.Id,
          SearchKeyWord:props.searchParam?.keywords,
          LocationText:props.searchParam?.locationText,
        };

        jobAlertApi.saveJobGateAlert(jobGateAlert).then(() => {}).catch(() => {});
      }
    })
    .catch(() => {})
    .finally(() => {props.hideLoading();});
  }

  return (
    <JobSearchResultsView
      searchResults={searchResults}
      sortBy={sortBy}
      searchParam={props.searchParam}
      loadMore={handleLoadMore}
      onHideJob={handleHideJob}
      favJobsAddOrRemove={favJobsAddOrRemove}
      goToJobDetail={handleGoToJobDetail}
      handleSortChange={handleSortChange}
      handleEasyApply={handleEasyApply}
      isMoreResultsExists={isMoreResultsExists}
      showFilters={showFilters}
      keywords={keywords}
      setKeywords={handleKeywordsChange}
      onSelectKeywords={onSelectKeywords}
      showHideFilters={showHideFilters}
      hanldeEmailMeMatchingJobs={hanldeEmailMeMatchingJobs}
      totalResults={totalResults}
      isLoading={loadingIsInProcess}
    />
  );
};

const mapStateToProps = (state: IApplicationState) => {
  return {
    searchParam: state.searchParam,
    loginUser: state.loginUser,
    hiddenJobs: state.hiddenJobs,
    favJobs: state.favJobs
  };
};

const mapDispatchToProps = {
  setSearchParam: setSearchParamAction,
  showLoading: showLoading,
  hideLoading: hideLoading,
  hideJob:hideJobAction,
  addToFav: addToFavJobsAction,
  removeFromFav: removeFromFavJobsAction,
  addRecentSearch:addRecentSearchAction,
};

const connectedJobSearchResultsContainer = connect(
  mapStateToProps,
  mapDispatchToProps,
)(JobSearchResultsContainer);
export { connectedJobSearchResultsContainer as JobSearchResultsContainer };
