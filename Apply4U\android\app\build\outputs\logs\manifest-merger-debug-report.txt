-- Merging decision tree log ---
manifest
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:1:1-29:12
MERGED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:1:1-29:12
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\7ceec762c322db3b2ee74240d8e71306\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_masked-view] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_slider] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-picker_picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-device-info] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-document-picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fs] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-geolocation-service] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-navbar-color] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-navbar-color\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2b5ba21ddda073e33e012f0d689623c6\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78a2f7f2f5ffb04c17532765310fabea\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\73ba53becf6a818dacbfe76ccb7dfd5a\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78816c54b596e77b24ddd4d407d9d3f7\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ec5ffae71883a2ffe61d9bb0ecd6125b\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f47c646146e0a916b229951b5277ec72\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9463ade28057419e742a85d1eb83c573\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\fe65cbe136c7a6ed6c0a1803f973fedb\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c5fbdd7e5fcfdaf5ce16f3f875e3e6a8\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a47a4e08eda3385848d214641bc7b9d8\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a87cda0c4aab8edddd525f59ec5d6ecf\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7cc434b4f1d90ca6b4967888f3cd4218\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\be2b599ee25b72ff91792f062e90e7d1\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7a9c524454ec247ef6b3d4f6fb3068fc\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\47e39260d4b885e43f7a9a8653a0dcdf\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3312377aaac51673b757679288f8c083\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\0038d2d4036a85741b91001e4541d656\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f4ec48f14b0c757d76386dd1d920f74\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:8:1-16:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\061d078e19c3ae7d999b8bf4f4fbd3f7\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\07d7c371ac06ab970dc33279314e8d16\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3627ca143232ec2ceb6e319eb18971d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c15af12ef96f8697638bb46980c84f\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\555fb818efa94b42627c0ab402b93001\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13a9b4bfb320ed3ae08327afbb8baf19\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f99def624e8a479ea1dfe4328f199eda\transformed\jetified-play-services-tasks-17.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933cca3d682df19ab6b85bbd4417e041\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\b3aae516de8ada04cd390a9558584616\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\e58f5a5a9361b8dc0ec210eacdd61c46\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac29b736242a45651a9bc02634f7a838\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fabb327706f5707c9cd6e95d136c30d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ba283a3244530bbd4580a31b408c1c9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b400e3fa62f6a4287be6e71a3821f\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c17d1aa2ccc813649d410d5ca1839aa\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [org.webkit:android-jsc:r250231] C:\Users\<USER>\.gradle\caches\transforms-3\11506ec08fbf39dc719a47bbad104fad\transformed\jetified-android-jsc-r250231\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\699d8495cfbcf29796d79737ecf7fe4f\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\38344280fe9342d918737701bd2e1811\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b356190bdf4bb7065067fc153a69c6d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1ad4007c9d50652fdaa15828c92907b\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\35973e4bf37b8f4368f9e646e1c09d65\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d614963c22b23ab93f8be235bc125f\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27f32e5559bcd43034d6914ede1dc092\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56c281bcb8243724411ecc33f6c1bde0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a293f7f653f0b588e6fae043d6c99347\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b7c687cf2ef0cdf849c0c1404623f4f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\345ebcb3e4094628456bcdee8e74a5da\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdd42089cdfdb89a5f7c93d6af7471d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd1425c12b4ad547fe75169beca3740\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73759641b17cf7e1f9f823253eaf747b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\efce1b44886555346c024fcbf6fda611\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b375b5e368b03c6f05fa335182ca0d8\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\057fda56c2cfc52d70a46042a30c6944\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078806bd157ae28fc0dc0dff008ec247\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2c67d6ef97116786f5e00cacbb4d36d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e642d0218a62e2463dc98bf97fe4741\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\424bb7f5e8999507734fb924a0875fe1\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b9668a697a30efe2c2676a2ef171ba3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6119c53d075509c7c00b74ccdeb41c3b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0533ea7e787d4330488437d8f040f117\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\605c5c983d653fbcbe978ef5aeb9a8b2\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afde1eb41d6777512f3b82b8c0854e7\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\47b7a7c93b579e9c1ca6a01deaba4de3\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fab882c1c4696f4a34c8d66981c3728\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\635c327730b2ff9d6991544b258a04a1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d57a71de353d6f615f194fd1f99fb600\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\9293682cb53936b0e929c86eac194797\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
	package
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:3:22-64
application
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:5:5-24:19
MERGED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:5:5-24:19
MERGED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:5:5-24:19
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\07d7c371ac06ab970dc33279314e8d16\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\07d7c371ac06ab970dc33279314e8d16\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:26:5-20
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:26:5-20
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c15af12ef96f8697638bb46980c84f\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c15af12ef96f8697638bb46980c84f\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\555fb818efa94b42627c0ab402b93001\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\555fb818efa94b42627c0ab402b93001\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13a9b4bfb320ed3ae08327afbb8baf19\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13a9b4bfb320ed3ae08327afbb8baf19\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f99def624e8a479ea1dfe4328f199eda\transformed\jetified-play-services-tasks-17.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f99def624e8a479ea1dfe4328f199eda\transformed\jetified-play-services-tasks-17.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:11:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b9668a697a30efe2c2676a2ef171ba3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b9668a697a30efe2c2676a2ef171ba3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:7:7-39
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:9:7-52
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:8:7-41
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:10:7-34
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:10:7-34
	android:theme
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:11:7-38
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:6:7-38
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:6:7-38
activity#com.apply4u.MainActivity
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:12:7-23:18
	android:label
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:14:9-41
	android:launchMode
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:16:9-40
	android:windowSoftInputMode
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:17:9-51
	android:exported
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:18:9-32
	android:configChanges
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:15:9-118
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:13:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:19:9-22:25
action#android.intent.action.MAIN
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:20:13-65
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:20:21-62
category#android.intent.category.LAUNCHER
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:21:13-73
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:21:23-70
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:25:9-84
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:25:26-81
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:26:5-81
MERGED from [:react-native-fs] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:26:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:27:5-78
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:27:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:28:5-80
	android:name
		ADDED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\main\AndroidManifest.xml:28:22-78
uses-sdk
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\7ceec762c322db3b2ee74240d8e71306\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\7ceec762c322db3b2ee74240d8e71306\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_masked-view] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_slider] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-community\slider\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\@react-native-picker\picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-device-info] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-device-info] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-device-info\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-document-picker] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-document-picker\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-geolocation-service] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-geolocation-service] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-geolocation-service\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-navbar-color] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-navbar-color\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-navbar-color] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-navbar-color\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2b5ba21ddda073e33e012f0d689623c6\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\2b5ba21ddda073e33e012f0d689623c6\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78a2f7f2f5ffb04c17532765310fabea\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78a2f7f2f5ffb04c17532765310fabea\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\73ba53becf6a818dacbfe76ccb7dfd5a\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\73ba53becf6a818dacbfe76ccb7dfd5a\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78816c54b596e77b24ddd4d407d9d3f7\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\78816c54b596e77b24ddd4d407d9d3f7\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ec5ffae71883a2ffe61d9bb0ecd6125b\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ec5ffae71883a2ffe61d9bb0ecd6125b\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f47c646146e0a916b229951b5277ec72\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f47c646146e0a916b229951b5277ec72\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9463ade28057419e742a85d1eb83c573\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9463ade28057419e742a85d1eb83c573\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\fe65cbe136c7a6ed6c0a1803f973fedb\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\fe65cbe136c7a6ed6c0a1803f973fedb\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c5fbdd7e5fcfdaf5ce16f3f875e3e6a8\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c5fbdd7e5fcfdaf5ce16f3f875e3e6a8\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a47a4e08eda3385848d214641bc7b9d8\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a47a4e08eda3385848d214641bc7b9d8\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a87cda0c4aab8edddd525f59ec5d6ecf\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\a87cda0c4aab8edddd525f59ec5d6ecf\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7cc434b4f1d90ca6b4967888f3cd4218\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7cc434b4f1d90ca6b4967888f3cd4218\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\be2b599ee25b72ff91792f062e90e7d1\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\be2b599ee25b72ff91792f062e90e7d1\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7a9c524454ec247ef6b3d4f6fb3068fc\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\7a9c524454ec247ef6b3d4f6fb3068fc\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\47e39260d4b885e43f7a9a8653a0dcdf\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\47e39260d4b885e43f7a9a8653a0dcdf\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3312377aaac51673b757679288f8c083\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\3312377aaac51673b757679288f8c083\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\0038d2d4036a85741b91001e4541d656\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\0038d2d4036a85741b91001e4541d656\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f4ec48f14b0c757d76386dd1d920f74\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f4ec48f14b0c757d76386dd1d920f74\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\061d078e19c3ae7d999b8bf4f4fbd3f7\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\061d078e19c3ae7d999b8bf4f4fbd3f7\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3a8a24834f295d8078f18b8298041f7\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\07d7c371ac06ab970dc33279314e8d16\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\07d7c371ac06ab970dc33279314e8d16\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dd997c22ecb777a9e0898386a3d502a5\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3627ca143232ec2ceb6e319eb18971d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b3627ca143232ec2ceb6e319eb18971d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c15af12ef96f8697638bb46980c84f\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11c15af12ef96f8697638bb46980c84f\transformed\jetified-play-services-location-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\555fb818efa94b42627c0ab402b93001\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\555fb818efa94b42627c0ab402b93001\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13a9b4bfb320ed3ae08327afbb8baf19\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-places-placereport:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13a9b4bfb320ed3ae08327afbb8baf19\transformed\jetified-play-services-places-placereport-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f99def624e8a479ea1dfe4328f199eda\transformed\jetified-play-services-tasks-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f99def624e8a479ea1dfe4328f199eda\transformed\jetified-play-services-tasks-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933cca3d682df19ab6b85bbd4417e041\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\933cca3d682df19ab6b85bbd4417e041\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\b3aae516de8ada04cd390a9558584616\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\b3aae516de8ada04cd390a9558584616\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\e58f5a5a9361b8dc0ec210eacdd61c46\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\e58f5a5a9361b8dc0ec210eacdd61c46\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac29b736242a45651a9bc02634f7a838\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac29b736242a45651a9bc02634f7a838\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fabb327706f5707c9cd6e95d136c30d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9fabb327706f5707c9cd6e95d136c30d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ba283a3244530bbd4580a31b408c1c9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ba283a3244530bbd4580a31b408c1c9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b400e3fa62f6a4287be6e71a3821f\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b400e3fa62f6a4287be6e71a3821f\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c17d1aa2ccc813649d410d5ca1839aa\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c17d1aa2ccc813649d410d5ca1839aa\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [org.webkit:android-jsc:r250231] C:\Users\<USER>\.gradle\caches\transforms-3\11506ec08fbf39dc719a47bbad104fad\transformed\jetified-android-jsc-r250231\AndroidManifest.xml:7:5-9:41
MERGED from [org.webkit:android-jsc:r250231] C:\Users\<USER>\.gradle\caches\transforms-3\11506ec08fbf39dc719a47bbad104fad\transformed\jetified-android-jsc-r250231\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\699d8495cfbcf29796d79737ecf7fe4f\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\699d8495cfbcf29796d79737ecf7fe4f\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\38344280fe9342d918737701bd2e1811\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\38344280fe9342d918737701bd2e1811\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b356190bdf4bb7065067fc153a69c6d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b356190bdf4bb7065067fc153a69c6d\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1ad4007c9d50652fdaa15828c92907b\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1ad4007c9d50652fdaa15828c92907b\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\35973e4bf37b8f4368f9e646e1c09d65\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\35973e4bf37b8f4368f9e646e1c09d65\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d614963c22b23ab93f8be235bc125f\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\46d614963c22b23ab93f8be235bc125f\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27f32e5559bcd43034d6914ede1dc092\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\27f32e5559bcd43034d6914ede1dc092\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56c281bcb8243724411ecc33f6c1bde0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\56c281bcb8243724411ecc33f6c1bde0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a293f7f653f0b588e6fae043d6c99347\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a293f7f653f0b588e6fae043d6c99347\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b7c687cf2ef0cdf849c0c1404623f4f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b7c687cf2ef0cdf849c0c1404623f4f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\345ebcb3e4094628456bcdee8e74a5da\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\345ebcb3e4094628456bcdee8e74a5da\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdd42089cdfdb89a5f7c93d6af7471d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fdd42089cdfdb89a5f7c93d6af7471d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd1425c12b4ad547fe75169beca3740\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd1425c12b4ad547fe75169beca3740\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73759641b17cf7e1f9f823253eaf747b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\73759641b17cf7e1f9f823253eaf747b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\efce1b44886555346c024fcbf6fda611\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\efce1b44886555346c024fcbf6fda611\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b375b5e368b03c6f05fa335182ca0d8\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b375b5e368b03c6f05fa335182ca0d8\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\057fda56c2cfc52d70a46042a30c6944\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\057fda56c2cfc52d70a46042a30c6944\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078806bd157ae28fc0dc0dff008ec247\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\078806bd157ae28fc0dc0dff008ec247\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2c67d6ef97116786f5e00cacbb4d36d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2c67d6ef97116786f5e00cacbb4d36d\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e642d0218a62e2463dc98bf97fe4741\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e642d0218a62e2463dc98bf97fe4741\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\424bb7f5e8999507734fb924a0875fe1\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\424bb7f5e8999507734fb924a0875fe1\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b9668a697a30efe2c2676a2ef171ba3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b9668a697a30efe2c2676a2ef171ba3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6119c53d075509c7c00b74ccdeb41c3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6119c53d075509c7c00b74ccdeb41c3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0533ea7e787d4330488437d8f040f117\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0533ea7e787d4330488437d8f040f117\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\605c5c983d653fbcbe978ef5aeb9a8b2\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\605c5c983d653fbcbe978ef5aeb9a8b2\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afde1eb41d6777512f3b82b8c0854e7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7afde1eb41d6777512f3b82b8c0854e7\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\47b7a7c93b579e9c1ca6a01deaba4de3\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\47b7a7c93b579e9c1ca6a01deaba4de3\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fab882c1c4696f4a34c8d66981c3728\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fab882c1c4696f4a34c8d66981c3728\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\635c327730b2ff9d6991544b258a04a1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\635c327730b2ff9d6991544b258a04a1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d57a71de353d6f615f194fd1f99fb600\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d57a71de353d6f615f194fd1f99fb600\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\9293682cb53936b0e929c86eac194797\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\9293682cb53936b0e929c86eac194797\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
	android:targetSdkVersion
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] D:\A4U CODE DATA\Mobile App\Mobile.tariq.ameen\Apply4U\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1231b5b187cb579b629117f9511a862\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:20:13-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
	android:name
		ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b2c28bd5719adb7e465469e2ca76c12\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:23:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-iid:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d00a3d973e62c64256363b4cb614082\transformed\jetified-play-services-iid-17.0.0\AndroidManifest.xml:23:22-79
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:23:9-26:75
	android:exported
		ADDED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\19ec269da49c6c76309942cfa013a60d\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:24:13-79
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4343b2ae69420088cd050005b2aafd9c\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:24:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\57875db857c52963776a564122dcb84b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\faa3592d2fc203d6c74b9a5f601058a1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.apply4u.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.apply4u.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\13659b94c66de92d98d7f8af61234b95\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\71b91ea8216105cda74b0317da307340\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\03d018e18e7118763cc731de8f689ba0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\7347c325151bfcdf75aa8769efa26987\transformed\jetified-installreferrer-1.1.2\AndroidManifest.xml:9:22-107
