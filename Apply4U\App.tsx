import React from 'react';
import {LightTheme, ThemeProvider} from './app/theme';
import {Provider as ReduxProvider} from 'react-redux';
import {Provider as PaperProvider} from 'react-native-paper';
import {store, persistor} from './app/redux';
import {CustomNavigationContainer} from './app/navigation';
import {ScreenView} from './app/components/common';
import {LoadingSpinner} from './app/external/loading-spinner';
import {PersistGate} from 'redux-persist/integration/react';
import {ToasterComponent} from './app/external/toaster';
import 'react-native-gesture-handler'

const App = React.memo(() => {
  return (
    <ReduxProvider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <PaperProvider>
          <ThemeProvider initial={LightTheme}>
            <ScreenView>
              <CustomNavigationContainer />
              <LoadingSpinner />
              <ToasterComponent />
            </ScreenView>
          </ThemeProvider>
        </PaperProvider>
      </PersistGate>
    </ReduxProvider>
  );
});

export default App;
