import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { jobCardHeight } from '../../../app.constant';
import { IJobSearchResponse } from '../../../interfaces';
import { ITheme, useCurrentTheme } from '../../../theme';
import { JobCardList, MainLayout, Text } from '../../common';

interface IProps {
    searchResults: IJobSearchResponse[];
    isMoreResultsExists: boolean;
    noHiddenJobsFound:boolean;
    jobAddToFav: (jobId: number) => void;
    goToJobDetail: (jobId: number) => void;
    handleSortChange?: () => void;
    loadMore: () => void;
    onUnHideJob: (jobId: number) => void;
    handleEasyApply: (jobId: number, jobSourceId: number, jobTitle: string, applicationUrl: string, isEasyApply:boolean) => void;
}

export const HiddenJobsView = (props: IProps) => {
    const styles = useCurrentTheme(createStyles);
    return (
        <MainLayout>
            <View style={styles.header}>
                <Text
                    styles={styles.headerText}
                    text={`Hidden Jobs`}
                    numberOfLines={2}
                />
            </View>

            <View style={styles.container}>
                {!props.noHiddenJobsFound && <JobCardList
                    jobResults={props.searchResults}
                    isMoreResultsExists={props.isMoreResultsExists}
                    isSwipeable={true}
                    isSwipeLeftEnabled={true}
                    isSwipeRightEnabled={true}
                    swipeRightText='Unhide'
                    showFavIcon={false}
                    showShareIcon={false}
                    goToJobDetail={props.goToJobDetail}
                    handleEasyApply={props.handleEasyApply}
                    favJobsAddOrRemove={props.jobAddToFav}
                    loadMore={props.loadMore}
                    onSwipeRight={props.onUnHideJob}
                />}
                {!!props.noHiddenJobsFound && props.noHiddenJobsFound == true && <View style={styles.noResultsMessageContainer}>
                    <Text styles={styles.noResultsMessageText} text='There is no hidden job.' />
                </View>

                }
            </View>
        </MainLayout>
    );
}

const createStyles = (theme: ITheme) => {
    const styles = StyleSheet.create({
        container: {
            flex: 1,
            marginBottom: 10,
        },
        header: {
            height: 60,
            width: '100%',
            flexDirection: 'row',
            backgroundColor: theme.palette.primary,
            justifyContent: 'center',
            alignItems: 'center'
        },
        headerText: {
            ...theme.typography.bold.medium,
            color: '#fff',
            textAlign: 'center',
        },
        noResultsMessageContainer:{
            backgroundColor:theme.palette.white,
            borderRadius:theme.borderRadius(5),
            height:jobCardHeight,
            marginTop:theme.spacing(100),
            width:'95%',
            alignSelf:'center',
            justifyContent:'center',
        },
        noResultsMessageText:{
            ...theme.typography.bold.large,
            color:'#333333',
            alignSelf:'center',
        }

    });

    return styles;
};