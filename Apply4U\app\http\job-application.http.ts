import {http} from './http-base';
import {IJobApplication} from '../interfaces';

const postApplication = async (
  jobApplication: IJobApplication,
): Promise<any> => {
  let result = await http
    .post('api/Jobs/applications', jobApplication)
    .catch(error => Promise.reject(error));

  return result.data;
};

const getJobApplicationByUserId = async (
  userId: number,
): Promise<IJobApplication[]> => {
  let url: string = `api/Jobs/applications/${userId}`;
  let result = await http
    .get<IJobApplication[]>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const isAlreadyApplied = async (
  userId: number,
  jobId: number,
): Promise<number> => {
  let url: string = `api/Jobs/applyjobstatus?userId=${userId}&jobId=${jobId}`;
  let result = await http
    .get<number>(url)
    .catch(error => Promise.reject(error));

  return result.data;
};

const jobApplicationApi = {
  postApplication,
  getJobApplicationByUserId,
  isAlreadyApplied,
};

export {jobApplicationApi};
