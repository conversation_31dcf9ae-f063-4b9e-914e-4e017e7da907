import { faMapMarkerAlt, faCrosshairs } from '@fortawesome/free-solid-svg-icons';
import React from 'react';
import {StyleSheet, View, ScrollView, Pressable,Keyboard} from 'react-native';
import {InputText, Text} from '.';
import { zIndex } from '../../app.constant';
import {InputType} from '../../enum';
import {autocompleteApi} from '../../http';
import {IAutoComplete} from '../../interfaces';
import {ITheme, useCurrentTheme} from '../../theme';
import {getCurrentLocation} from '../../utility';


interface IProps {
  placeholder: string;
  name: string;
  styles?: any;
  value?: string;
  selectedItem?: IAutoComplete;
  isFilterPage?:boolean;
  onChange?: (
    value: string,
    name: string,
    selectedItem: IAutoComplete | null | undefined,
  ) => void;
  onOpen:() => void;
  onClose:() => void;
}
export const LocationAutoComplete = (props: IProps) => {
  const styles = useCurrentTheme(createStyles);
  const [inputValue, setInputValue] = React.useState<String>(props.value ?? '');

  const [selectedItem, setSelectedItem] = React.useState<
    IAutoComplete | null | undefined
  >(props.selectedItem);
  const minCharacterToSendRequest = 3;

  const handleInputChange = (value: string, name: string) => {
    setData([]);
    setSelectedItem(null);
    setInputValue(value);
    if (props.onChange) {
      props.onChange(value, name, null);
    }

    if (
      (!selectedItem || selectedItem.DisplayValue !== value) &&
      value &&
      value.length >= minCharacterToSendRequest
    ) {
      autocompleteApi.getLocationAutoComplete(value).then(result => {
        if (result && result.length > 0) {
          setData(result);
        }
      }).catch(() => {});
    }
  };

  const [data, setData] = React.useState<IAutoComplete[]>([]);

  const handleRequestLocation = async () => {
    await  getCurrentLocation((latitude:number,longitude:number) => {
    });
  }
  React.useEffect(() => {
    if(!!data && data.length > 0){
      props.onOpen();
    }else{
      props.onClose();
    }
  },[data]);

  React.useEffect(() => {
    const keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide',() => {
      setData([]);
    });
    return () => {
      Keyboard.removeAllListeners('keyboardDidHide');
    }
  },[]);

  const RenderItem = (innerProps: any) => {
    const item: IAutoComplete = innerProps.item;
    return (
      <Pressable
        onPress={() => {
          setData([]);
          setSelectedItem(item);
          setInputValue(item.DisplayValue);
          if (props.onChange) {
            props.onChange(item.DisplayValue, props.name, item);
          }
        }}
        style={styles.itemContainer}
        >
        <Text numberOfLines={1} styles={styles.itemTextStyle} text={item.DisplayValue} />
      </Pressable>
    );
  };

  return (
    <>
    <View style={styles.container}>
      <View style={styles.secondContainer}>
        <View style={styles.inputContainer}>
          <InputText
            placeholder={props.placeholder}
            name={props.name}
            styles={props.styles}
            inputType={InputType.Text}
            value={inputValue}
            onBlur={() => {
              setData([]);
            }}
            onChangeText={handleInputChange}
            height={50}
            icon={faMapMarkerAlt}
            inputIconSize={20}
            // iconEnd={faCrosshairs}
            // iconEndPressed={handleRequestLocation}
          />
        </View>
      </View>
    </View>
    {!!data && data.length > 0 && (
      <View style={!!props.isFilterPage && props.isFilterPage == true ? styles.filterPageitemsContainer : styles.itemsContainer}>
        <ScrollView keyboardShouldPersistTaps={'always'}>
          {data.map((item: IAutoComplete) => {
            return (
              <RenderItem
                key={`list_item_${item.Key}_${item.Value}`}
                item={item}
              />
            );
          })}
          </ScrollView>
      </View>
    )}
    </>
  );
};

const createStyles = (theme: ITheme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
    },
    secondContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    inputContainer: {
      flex: 1,
      flexDirection: 'column',
    },
    itemsContainer: {
      position:'absolute',
      width:'100%',
      top:145,
      left:15,
      height:'60%',
      borderLeftWidth: 0.5,
      borderRightWidth: 0.5,
      borderTopWidth: 0.5,
      borderColor: theme.palette.gray,
      ...theme.zIndex(zIndex.locationAutoComplete),
    },
    filterPageitemsContainer: {
      position:'absolute',
      width:'100%',
      top:76,
      left:15,
      height:'60%',
      borderLeftWidth: 0.5,
      borderRightWidth: 0.5,
      borderTopWidth: 0.5,
      borderColor: theme.palette.gray,
      ...theme.zIndex(zIndex.locationAutoComplete),
    },
    itemContainer: {
      height: 40,
      width: '100%',
      paddingLeft: theme.spacing(5),
      borderColor: 'gray',
      backgroundColor: theme.palette.white,
      justifyContent: 'center',
    },
    itemTextStyle: {
      ...theme.typography.normal.small,
      color: theme.palette.gray,
    },
    scrollView: {
      flex: 1,
    }
  });

  return styles;
};
